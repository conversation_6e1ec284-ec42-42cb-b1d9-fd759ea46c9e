import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/db'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  // Check if <PERSON><PERSON> is configured
  if (!stripe) {
    return NextResponse.json({
      error: 'Stripe is not configured'
    }, { status: 500 })
  }

  const body = await request.text()
  const signature = headers().get('stripe-signature')

  if (!signature) {
    return NextResponse.json({ error: 'No signature' }, { status: 400 })
  }

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const userId = session.metadata?.userId
  
  if (!userId) {
    console.error('No userId in checkout session metadata')
    return
  }

  if (session.mode === 'subscription' && session.subscription) {
    const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
    await upsertSubscription(subscription, userId)
  }
}

async function handleSubscriptionChange(subscription: Stripe.Subscription) {
  const customer = await stripe.customers.retrieve(subscription.customer as string)
  
  if (customer.deleted) {
    console.error('Customer was deleted')
    return
  }

  const userId = customer.metadata?.userId
  
  if (!userId) {
    console.error('No userId in customer metadata')
    return
  }

  await upsertSubscription(subscription, userId)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  await prisma.subscription.deleteMany({
    where: {
      stripeSubscriptionId: subscription.id
    }
  })

  // Update user premium status
  const customer = await stripe.customers.retrieve(subscription.customer as string)
  
  if (!customer.deleted && customer.metadata?.userId) {
    await prisma.user.update({
      where: { id: customer.metadata.userId },
      data: { isPremium: false }
    })
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
    const customer = await stripe.customers.retrieve(subscription.customer as string)
    
    if (!customer.deleted && customer.metadata?.userId) {
      await prisma.user.update({
        where: { id: customer.metadata.userId },
        data: { isPremium: true }
      })
    }
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  // Handle failed payment - could send email notification, etc.
  console.log('Payment failed for invoice:', invoice.id)
}

async function upsertSubscription(subscription: Stripe.Subscription, userId: string) {
  const subscriptionData = {
    userId,
    stripeSubscriptionId: subscription.id,
    status: subscription.status,
    priceId: subscription.items.data[0]?.price.id || '',
    currentPeriodStart: new Date(subscription.current_period_start * 1000),
    currentPeriodEnd: new Date(subscription.current_period_end * 1000),
  }

  await prisma.subscription.upsert({
    where: {
      stripeSubscriptionId: subscription.id
    },
    update: subscriptionData,
    create: subscriptionData
  })

  // Update user premium status
  const isPremium = subscription.status === 'active' || subscription.status === 'trialing'
  
  await prisma.user.update({
    where: { id: userId },
    data: { isPremium }
  })
}
