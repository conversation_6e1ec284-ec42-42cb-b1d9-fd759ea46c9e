import { Metadata } from 'next'

export const siteConfig = {
  name: 'C<PERSON><PERSON>',
  description: 'Create professional resumes with our easy-to-use CV builder. Choose from premium templates and download as PDF.',
  url: process.env.NEXTAUTH_URL || 'https://cvcraft.com',
  ogImage: '/og-image.png',
  creator: '@cvcraft',
  keywords: [
    'resume builder',
    'CV maker',
    'professional resume',
    'job application',
    'career tools',
    'PDF resume',
    'resume templates',
    'online CV builder',
    'free resume maker',
    'ATS friendly resume'
  ]
}

export function createMetadata({
  title,
  description,
  image,
  noIndex = false,
  ...props
}: {
  title?: string
  description?: string
  image?: string
  noIndex?: boolean
} & Metadata = {}): Metadata {
  const fullTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name
  const fullDescription = description || siteConfig.description
  const fullImage = image || siteConfig.ogImage

  return {
    metadataBase: new URL(siteConfig.url),
    title: fullTitle,
    description: fullDescription,
    keywords: siteConfig.keywords,
    authors: [{ name: siteConfig.name }],
    creator: siteConfig.creator,
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: siteConfig.url,
      title: fullTitle,
      description: fullDescription,
      siteName: siteConfig.name,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: fullTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description: fullDescription,
      images: [fullImage],
      creator: siteConfig.creator,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    ...props,
  }
}

// Page-specific metadata
export const pageMetadata = {
  home: createMetadata({
    title: 'Professional Resume Builder',
    description: 'Create stunning resumes in minutes with our professional resume builder. Choose from premium templates, get live preview, and download as PDF. Start free today!'
  }),
  
  login: createMetadata({
    title: 'Sign In',
    description: 'Sign in to your CVCraft account to access your resumes and premium features.',
    noIndex: true
  }),
  
  register: createMetadata({
    title: 'Create Account',
    description: 'Join CVCraft today and start building professional resumes. Free account includes 1 resume and modern template.',
    noIndex: true
  }),
  
  dashboard: createMetadata({
    title: 'Dashboard',
    description: 'Manage your resumes, view your subscription, and access premium features.',
    noIndex: true
  }),
  
  billing: createMetadata({
    title: 'Billing & Subscription',
    description: 'Manage your CVCraft subscription, view billing history, and upgrade to premium.',
    noIndex: true
  }),
  
  resumeBuilder: createMetadata({
    title: 'Resume Builder',
    description: 'Build your professional resume with our step-by-step builder. Add your experience, education, skills, and more.',
    noIndex: true
  }),
  
  templates: createMetadata({
    title: 'Resume Templates',
    description: 'Browse our collection of professional resume templates. Choose from modern, elegant, and minimal designs.'
  })
}

// Structured data for SEO
export const structuredData = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    url: siteConfig.url,
    logo: `${siteConfig.url}/logo.png`,
    description: siteConfig.description,
    sameAs: [
      'https://twitter.com/cvcraft',
      'https://linkedin.com/company/cvcraft'
    ]
  },
  
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteConfig.url}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  },
  
  softwareApplication: {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: siteConfig.name,
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web',
    description: siteConfig.description,
    url: siteConfig.url,
    author: {
      '@type': 'Organization',
      name: siteConfig.name
    },
    offers: {
      '@type': 'Offer',
      price: '9.99',
      priceCurrency: 'USD',
      priceValidUntil: '2025-12-31',
      availability: 'https://schema.org/InStock'
    }
  }
}
