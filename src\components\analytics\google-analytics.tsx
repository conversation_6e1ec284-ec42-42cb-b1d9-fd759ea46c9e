'use client'

import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

declare global {
  interface Window {
    gtag: (command: string, ...args: any[]) => void
  }
}

export function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!GA_MEASUREMENT_ID || !window.gtag) return

    const url = pathname + searchParams.toString()
    
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_location: url,
    })
  }, [pathname, searchParams])

  if (!GA_MEASUREMENT_ID) {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_location: window.location.href,
              page_title: document.title,
            });
          `,
        }}
      />
    </>
  )
}

// Analytics event tracking functions
export const analytics = {
  // Track page views
  pageView: (url: string) => {
    if (!window.gtag) return
    window.gtag('config', GA_MEASUREMENT_ID!, {
      page_location: url,
    })
  },

  // Track custom events
  event: (action: string, parameters?: Record<string, any>) => {
    if (!window.gtag) return
    window.gtag('event', action, parameters)
  },

  // Track user registration
  signUp: (method: string = 'email') => {
    analytics.event('sign_up', {
      method,
    })
  },

  // Track user login
  login: (method: string = 'email') => {
    analytics.event('login', {
      method,
    })
  },

  // Track resume creation
  resumeCreated: (template: string) => {
    analytics.event('resume_created', {
      template,
      event_category: 'engagement',
    })
  },

  // Track PDF downloads
  pdfDownload: (resumeId: string, template: string) => {
    analytics.event('pdf_download', {
      resume_id: resumeId,
      template,
      event_category: 'engagement',
    })
  },

  // Track subscription events
  subscribe: (plan: string, value: number) => {
    analytics.event('purchase', {
      transaction_id: Date.now().toString(),
      value,
      currency: 'USD',
      items: [{
        item_id: plan,
        item_name: `CVCraft ${plan}`,
        category: 'subscription',
        quantity: 1,
        price: value,
      }],
    })
  },

  // Track template selection
  templateSelected: (template: string, isPremium: boolean) => {
    analytics.event('template_selected', {
      template,
      is_premium: isPremium,
      event_category: 'engagement',
    })
  },

  // Track form completion steps
  formStep: (step: number, stepName: string) => {
    analytics.event('form_step_completed', {
      step_number: step,
      step_name: stepName,
      event_category: 'engagement',
    })
  },

  // Track errors
  error: (errorMessage: string, errorLocation: string) => {
    analytics.event('exception', {
      description: errorMessage,
      fatal: false,
      custom_map: {
        location: errorLocation,
      },
    })
  },
}
