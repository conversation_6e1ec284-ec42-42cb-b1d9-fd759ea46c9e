import { 
  isPremiumTemplate, 
  canAccessTemplate, 
  canCreateResume, 
  getResumeLimit 
} from '@/lib/premium'

describe('Premium Features', () => {
  describe('isPremiumTemplate', () => {
    it('should return true for premium templates', () => {
      expect(isPremiumTemplate('elegant')).toBe(true)
      expect(isPremiumTemplate('minimal')).toBe(true)
    })

    it('should return false for free templates', () => {
      expect(isPremiumTemplate('modern')).toBe(false)
    })
  })

  describe('canAccessTemplate', () => {
    it('should allow free users to access free templates', () => {
      expect(canAccessTemplate('modern', false)).toBe(true)
    })

    it('should not allow free users to access premium templates', () => {
      expect(canAccessTemplate('elegant', false)).toBe(false)
      expect(canAccessTemplate('minimal', false)).toBe(false)
    })

    it('should allow premium users to access all templates', () => {
      expect(canAccessTemplate('modern', true)).toBe(true)
      expect(canAccessTemplate('elegant', true)).toBe(true)
      expect(canAccessTemplate('minimal', true)).toBe(true)
    })
  })

  describe('canCreateResume', () => {
    it('should allow free users to create their first resume', () => {
      expect(canCreateResume(0, false)).toBe(true)
    })

    it('should not allow free users to create more than 1 resume', () => {
      expect(canCreateResume(1, false)).toBe(false)
      expect(canCreateResume(2, false)).toBe(false)
    })

    it('should allow premium users to create unlimited resumes', () => {
      expect(canCreateResume(0, true)).toBe(true)
      expect(canCreateResume(1, true)).toBe(true)
      expect(canCreateResume(10, true)).toBe(true)
    })
  })

  describe('getResumeLimit', () => {
    it('should return 1 for free users', () => {
      expect(getResumeLimit(false)).toBe(1)
    })

    it('should return null (unlimited) for premium users', () => {
      expect(getResumeLimit(true)).toBe(null)
    })
  })
})
