{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n      }\n      return session\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC5B;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/ModernTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface ModernTemplateProps {\n  data: ResumeData\n}\n\nexport default function ModernTemplate({ data }: ModernTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-8\">\n        <div className=\"flex items-center space-x-6\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-24 h-24 rounded-full border-4 border-white object-cover\"\n            />\n          )}\n          <div className=\"flex-1\">\n            <h1 className=\"text-4xl font-bold mb-2\">{data.fullName}</h1>\n            <div className=\"flex flex-wrap gap-4 text-blue-100\">\n              <span>{data.email}</span>\n              {data.phone && <span>{data.phone}</span>}\n              {data.address && <span>{data.address}</span>}\n            </div>\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6\">\n            <p className=\"text-blue-50 leading-relaxed\">{data.summary}</p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Experience */}\n            {data.experience.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Professional Experience\n                </h2>\n                <div className=\"space-y-6\">\n                  {data.experience.map((exp) => (\n                    <div key={exp.id}>\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-800\">{exp.position}</h3>\n                          <p className=\"text-blue-600 font-medium\">{exp.company}</p>\n                          {exp.location && <p className=\"text-gray-600 text-sm\">{exp.location}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-600\">\n                          <p>\n                            {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {exp.description && (\n                        <div className=\"text-gray-700 text-sm leading-relaxed\">\n                          {exp.description.split('\\n').map((line, index) => (\n                            <p key={index} className=\"mb-1\">{line}</p>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Education */}\n            {data.education.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Education\n                </h2>\n                <div className=\"space-y-4\">\n                  {data.education.map((edu) => (\n                    <div key={edu.id}>\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-800\">{edu.degree}</h3>\n                          <p className=\"text-blue-600 font-medium\">{edu.institution}</p>\n                          {edu.fieldOfStudy && <p className=\"text-gray-600 text-sm\">{edu.fieldOfStudy}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-600\">\n                          <p>\n                            {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {edu.description && (\n                        <p className=\"text-gray-700 text-sm leading-relaxed\">{edu.description}</p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-8\">\n            {/* Skills */}\n            {data.skills.length > 0 && (\n              <section>\n                <h2 className=\"text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Skills\n                </h2>\n                <div className=\"space-y-3\">\n                  {data.skills.map((skill) => (\n                    <div key={skill.id}>\n                      <div className=\"flex justify-between items-center mb-1\">\n                        <span className=\"text-gray-800 font-medium\">{skill.name}</span>\n                        {skill.level && (\n                          <span className=\"text-xs text-gray-600 capitalize\">{skill.level}</span>\n                        )}\n                      </div>\n                      {skill.level && (\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{\n                              width: skill.level === 'expert' ? '100%' : \n                                     skill.level === 'advanced' ? '80%' : \n                                     skill.level === 'intermediate' ? '60%' : '40%'\n                            }}\n                          ></div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Languages */}\n            {data.languages.length > 0 && (\n              <section>\n                <h2 className=\"text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Languages\n                </h2>\n                <div className=\"space-y-2\">\n                  {data.languages.map((lang) => (\n                    <div key={lang.id} className=\"flex justify-between items-center\">\n                      <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                      <span className=\"text-sm text-blue-600 capitalize\">{lang.level}</span>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,eAAe,EAAE,IAAI,EAAuB;IAClE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,gPAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,gPAAC;gBAAI,WAAU;;kCACb,gPAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,gPAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,gPAAC;gCAAI,WAAU;;kDACb,gPAAC;wCAAG,WAAU;kDAA2B,KAAK,QAAQ;;;;;;kDACtD,gPAAC;wCAAI,WAAU;;0DACb,gPAAC;0DAAM,KAAK,KAAK;;;;;;4CAChB,KAAK,KAAK,kBAAI,gPAAC;0DAAM,KAAK,KAAK;;;;;;4CAC/B,KAAK,OAAO,kBAAI,gPAAC;0DAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oBAIzC,KAAK,OAAO,kBACX,gPAAC;wBAAI,WAAU;kCACb,cAAA,gPAAC;4BAAE,WAAU;sCAAgC,KAAK,OAAO;;;;;;;;;;;;;;;;;0BAK/D,gPAAC;gBAAI,WAAU;0BACb,cAAA,gPAAC;oBAAI,WAAU;;sCAEb,gPAAC;4BAAI,WAAU;;gCAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,gPAAC;;sDACC,gPAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,gPAAC;4CAAI,WAAU;sDACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,oBACpB,gPAAC;;sEACC,gPAAC;4DAAI,WAAU;;8EACb,gPAAC;;sFACC,gPAAC;4EAAG,WAAU;sFAAuC,IAAI,QAAQ;;;;;;sFACjE,gPAAC;4EAAE,WAAU;sFAA6B,IAAI,OAAO;;;;;;wEACpD,IAAI,QAAQ,kBAAI,gPAAC;4EAAE,WAAU;sFAAyB,IAAI,QAAQ;;;;;;;;;;;;8EAErE,gPAAC;oEAAI,WAAU;8EACb,cAAA,gPAAC;;4EACE,WAAW,IAAI,SAAS;4EAAE;4EAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;wDAItG,IAAI,WAAW,kBACd,gPAAC;4DAAI,WAAU;sEACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,gPAAC;oEAAc,WAAU;8EAAQ;mEAAzB;;;;;;;;;;;mDAhBN,IAAI,EAAE;;;;;;;;;;;;;;;;gCA2BvB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;sDACC,gPAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,gPAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,gPAAC;;sEACC,gPAAC;4DAAI,WAAU;;8EACb,gPAAC;;sFACC,gPAAC;4EAAG,WAAU;sFAAuC,IAAI,MAAM;;;;;;sFAC/D,gPAAC;4EAAE,WAAU;sFAA6B,IAAI,WAAW;;;;;;wEACxD,IAAI,YAAY,kBAAI,gPAAC;4EAAE,WAAU;sFAAyB,IAAI,YAAY;;;;;;;;;;;;8EAE7E,gPAAC;oEAAI,WAAU;8EACb,cAAA,gPAAC;;4EACE,WAAW,IAAI,SAAS;4EAAE;4EAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;wDAItG,IAAI,WAAW,kBACd,gPAAC;4DAAE,WAAU;sEAAyC,IAAI,WAAW;;;;;;;mDAd/D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,gPAAC;4BAAI,WAAU;;gCAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,gPAAC;;sDACC,gPAAC;4CAAG,WAAU;sDAAuE;;;;;;sDAGrF,gPAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,gPAAC;;sEACC,gPAAC;4DAAI,WAAU;;8EACb,gPAAC;oEAAK,WAAU;8EAA6B,MAAM,IAAI;;;;;;gEACtD,MAAM,KAAK,kBACV,gPAAC;oEAAK,WAAU;8EAAoC,MAAM,KAAK;;;;;;;;;;;;wDAGlE,MAAM,KAAK,kBACV,gPAAC;4DAAI,WAAU;sEACb,cAAA,gPAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,MAAM,KAAK,KAAK,WAAW,SAC3B,MAAM,KAAK,KAAK,aAAa,QAC7B,MAAM,KAAK,KAAK,iBAAiB,QAAQ;gEAClD;;;;;;;;;;;;mDAfE,MAAM,EAAE;;;;;;;;;;;;;;;;gCA0BzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;sDACC,gPAAC;4CAAG,WAAU;sDAAuE;;;;;;sDAGrF,gPAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,gPAAC;oDAAkB,WAAU;;sEAC3B,gPAAC;4DAAK,WAAU;sEAA6B,KAAK,IAAI;;;;;;sEACtD,gPAAC;4DAAK,WAAU;sEAAoC,KAAK,KAAK;;;;;;;mDAFtD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/ElegantTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface ElegantTemplateProps {\n  data: ResumeData\n}\n\nexport default function ElegantTemplate({ data }: ElegantTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"border-b-4 border-gray-800 p-8\">\n        <div className=\"text-center\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-32 h-32 rounded-full mx-auto mb-4 object-cover border-4 border-gray-200\"\n            />\n          )}\n          <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.fullName}</h1>\n          <div className=\"flex justify-center space-x-6 text-gray-600 text-sm\">\n            <span>{data.email}</span>\n            {data.phone && <span>{data.phone}</span>}\n            {data.address && <span>{data.address}</span>}\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-gray-700 leading-relaxed max-w-3xl mx-auto italic\">\n              \"{data.summary}\"\n            </p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8\">\n        {/* Experience */}\n        {data.experience.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n              Professional Experience\n            </h2>\n            <div className=\"space-y-6\">\n              {data.experience.map((exp, index) => (\n                <div key={exp.id} className=\"relative\">\n                  {index > 0 && <div className=\"absolute left-4 -top-3 w-0.5 h-6 bg-gray-300\"></div>}\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0 mt-1\">\n                      <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-serif font-semibold text-gray-800\">{exp.position}</h3>\n                          <p className=\"text-gray-600 font-medium\">{exp.company}</p>\n                          {exp.location && <p className=\"text-gray-500 text-sm\">{exp.location}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-500\">\n                          <p>\n                            {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {exp.description && (\n                        <div className=\"text-gray-700 text-sm leading-relaxed\">\n                          {exp.description.split('\\n').map((line, index) => (\n                            <p key={index} className=\"mb-1\">{line}</p>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Education */}\n          {data.education.length > 0 && (\n            <section>\n              <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                Education\n              </h2>\n              <div className=\"space-y-4\">\n                {data.education.map((edu) => (\n                  <div key={edu.id} className=\"text-center\">\n                    <h3 className=\"text-lg font-serif font-semibold text-gray-800\">{edu.degree}</h3>\n                    <p className=\"text-gray-600 font-medium\">{edu.institution}</p>\n                    {edu.fieldOfStudy && <p className=\"text-gray-500 text-sm\">{edu.fieldOfStudy}</p>}\n                    <p className=\"text-sm text-gray-500 mt-1\">\n                      {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                    </p>\n                    {edu.description && (\n                      <p className=\"text-gray-700 text-sm leading-relaxed mt-2\">{edu.description}</p>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n\n          {/* Skills & Languages */}\n          <div className=\"space-y-8\">\n            {/* Skills */}\n            {data.skills.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                  Skills\n                </h2>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  {data.skills.map((skill) => (\n                    <div key={skill.id} className=\"text-center\">\n                      <span className=\"text-gray-800 font-medium text-sm\">{skill.name}</span>\n                      {skill.level && (\n                        <p className=\"text-xs text-gray-500 capitalize\">{skill.level}</p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Languages */}\n            {data.languages.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                  Languages\n                </h2>\n                <div className=\"space-y-2\">\n                  {data.languages.map((lang) => (\n                    <div key={lang.id} className=\"text-center\">\n                      <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                      <p className=\"text-sm text-gray-500 capitalize\">{lang.level}</p>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,gBAAgB,EAAE,IAAI,EAAwB;IACpE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,gPAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,gPAAC;gBAAI,WAAU;;kCACb,gPAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,gPAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,gPAAC;gCAAG,WAAU;0CAAoD,KAAK,QAAQ;;;;;;0CAC/E,gPAAC;gCAAI,WAAU;;kDACb,gPAAC;kDAAM,KAAK,KAAK;;;;;;oCAChB,KAAK,KAAK,kBAAI,gPAAC;kDAAM,KAAK,KAAK;;;;;;oCAC/B,KAAK,OAAO,kBAAI,gPAAC;kDAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;oBAGvC,KAAK,OAAO,kBACX,gPAAC;wBAAI,WAAU;kCACb,cAAA,gPAAC;4BAAE,WAAU;;gCAAyD;gCAClE,KAAK,OAAO;gCAAC;;;;;;;;;;;;;;;;;;0BAMvB,gPAAC;gBAAI,WAAU;;oBAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,gPAAC;wBAAQ,WAAU;;0CACjB,gPAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,gPAAC;gCAAI,WAAU;0CACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,sBACzB,gPAAC;wCAAiB,WAAU;;4CACzB,QAAQ,mBAAK,gPAAC;gDAAI,WAAU;;;;;;0DAC7B,gPAAC;gDAAI,WAAU;;kEACb,gPAAC;wDAAI,WAAU;kEACb,cAAA,gPAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,gPAAC;wDAAI,WAAU;;0EACb,gPAAC;gEAAI,WAAU;;kFACb,gPAAC;;0FACC,gPAAC;gFAAG,WAAU;0FAAkD,IAAI,QAAQ;;;;;;0FAC5E,gPAAC;gFAAE,WAAU;0FAA6B,IAAI,OAAO;;;;;;4EACpD,IAAI,QAAQ,kBAAI,gPAAC;gFAAE,WAAU;0FAAyB,IAAI,QAAQ;;;;;;;;;;;;kFAErE,gPAAC;wEAAI,WAAU;kFACb,cAAA,gPAAC;;gFACE,WAAW,IAAI,SAAS;gFAAE;gFAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4DAItG,IAAI,WAAW,kBACd,gPAAC;gEAAI,WAAU;0EACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,gPAAC;wEAAc,WAAU;kFAAQ;uEAAzB;;;;;;;;;;;;;;;;;;;;;;;uCAtBV,IAAI,EAAE;;;;;;;;;;;;;;;;kCAkCxB,gPAAC;wBAAI,WAAU;;4BAEZ,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;kDACC,gPAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,gPAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,gPAAC;gDAAiB,WAAU;;kEAC1B,gPAAC;wDAAG,WAAU;kEAAkD,IAAI,MAAM;;;;;;kEAC1E,gPAAC;wDAAE,WAAU;kEAA6B,IAAI,WAAW;;;;;;oDACxD,IAAI,YAAY,kBAAI,gPAAC;wDAAE,WAAU;kEAAyB,IAAI,YAAY;;;;;;kEAC3E,gPAAC;wDAAE,WAAU;;4DACV,WAAW,IAAI,SAAS;4DAAE;4DAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;oDAElG,IAAI,WAAW,kBACd,gPAAC;wDAAE,WAAU;kEAA8C,IAAI,WAAW;;;;;;;+CARpE,IAAI,EAAE;;;;;;;;;;;;;;;;0CAiBxB,gPAAC;gCAAI,WAAU;;oCAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,gPAAC;;0DACC,gPAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,gPAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,gPAAC;wDAAmB,WAAU;;0EAC5B,gPAAC;gEAAK,WAAU;0EAAqC,MAAM,IAAI;;;;;;4DAC9D,MAAM,KAAK,kBACV,gPAAC;gEAAE,WAAU;0EAAoC,MAAM,KAAK;;;;;;;uDAHtD,MAAM,EAAE;;;;;;;;;;;;;;;;oCAYzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;0DACC,gPAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,gPAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,gPAAC;wDAAkB,WAAU;;0EAC3B,gPAAC;gEAAK,WAAU;0EAA6B,KAAK,IAAI;;;;;;0EACtD,gPAAC;gEAAE,WAAU;0EAAoC,KAAK,KAAK;;;;;;;uDAFnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/SimpleTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface SimpleTemplateProps {\n  data: ResumeData\n}\n\nexport default function SimpleTemplate({ data }: SimpleTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"border-b-2 border-gray-300 p-8\">\n        <div className=\"flex items-center space-x-6\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-20 h-20 rounded object-cover border border-gray-300\"\n            />\n          )}\n          <div className=\"flex-1\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{data.fullName}</h1>\n            <div className=\"text-gray-600 space-y-1\">\n              <p>{data.email}</p>\n              {data.phone && <p>{data.phone}</p>}\n              {data.address && <p>{data.address}</p>}\n            </div>\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6\">\n            <h2 className=\"text-lg font-bold text-gray-900 mb-2\">Professional Summary</h2>\n            <p className=\"text-gray-700 leading-relaxed\">{data.summary}</p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8 space-y-8\">\n        {/* Experience */}\n        {data.experience.length > 0 && (\n          <section>\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n              Professional Experience\n            </h2>\n            <div className=\"space-y-6\">\n              {data.experience.map((exp) => (\n                <div key={exp.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-bold text-gray-900\">{exp.position}</h3>\n                      <p className=\"text-gray-700 font-medium\">{exp.company}</p>\n                      {exp.location && <p className=\"text-gray-600 text-sm\">{exp.location}</p>}\n                    </div>\n                    <div className=\"text-right text-sm text-gray-600\">\n                      <p>\n                        {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                      </p>\n                    </div>\n                  </div>\n                  {exp.description && (\n                    <div className=\"text-gray-700 text-sm leading-relaxed\">\n                      {exp.description.split('\\n').map((line, index) => (\n                        <p key={index} className=\"mb-1\">{line}</p>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Education */}\n        {data.education.length > 0 && (\n          <section>\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n              Education\n            </h2>\n            <div className=\"space-y-4\">\n              {data.education.map((edu) => (\n                <div key={edu.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-bold text-gray-900\">{edu.degree}</h3>\n                      <p className=\"text-gray-700 font-medium\">{edu.institution}</p>\n                      {edu.fieldOfStudy && <p className=\"text-gray-600 text-sm\">{edu.fieldOfStudy}</p>}\n                    </div>\n                    <div className=\"text-right text-sm text-gray-600\">\n                      <p>\n                        {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                      </p>\n                    </div>\n                  </div>\n                  {edu.description && (\n                    <p className=\"text-gray-700 text-sm leading-relaxed\">{edu.description}</p>\n                  )}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {/* Skills */}\n          {data.skills.length > 0 && (\n            <section>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n                Skills\n              </h2>\n              <div className=\"space-y-2\">\n                {data.skills.map((skill) => (\n                  <div key={skill.id} className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-800 font-medium\">{skill.name}</span>\n                    {skill.level && (\n                      <span className=\"text-sm text-gray-600 capitalize\">{skill.level}</span>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n\n          {/* Languages */}\n          {data.languages.length > 0 && (\n            <section>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n                Languages\n              </h2>\n              <div className=\"space-y-2\">\n                {data.languages.map((lang) => (\n                  <div key={lang.id} className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                    <span className=\"text-sm text-gray-600 capitalize\">{lang.level}</span>\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,eAAe,EAAE,IAAI,EAAuB;IAClE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,gPAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,gPAAC;gBAAI,WAAU;;kCACb,gPAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,gPAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,gPAAC;gCAAI,WAAU;;kDACb,gPAAC;wCAAG,WAAU;kDAAyC,KAAK,QAAQ;;;;;;kDACpE,gPAAC;wCAAI,WAAU;;0DACb,gPAAC;0DAAG,KAAK,KAAK;;;;;;4CACb,KAAK,KAAK,kBAAI,gPAAC;0DAAG,KAAK,KAAK;;;;;;4CAC5B,KAAK,OAAO,kBAAI,gPAAC;0DAAG,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oBAItC,KAAK,OAAO,kBACX,gPAAC;wBAAI,WAAU;;0CACb,gPAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,gPAAC;gCAAE,WAAU;0CAAiC,KAAK,OAAO;;;;;;;;;;;;;;;;;;0BAKhE,gPAAC;gBAAI,WAAU;;oBAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,gPAAC;;0CACC,gPAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,gPAAC;gCAAI,WAAU;0CACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,oBACpB,gPAAC;;0DACC,gPAAC;gDAAI,WAAU;;kEACb,gPAAC;;0EACC,gPAAC;gEAAG,WAAU;0EAAmC,IAAI,QAAQ;;;;;;0EAC7D,gPAAC;gEAAE,WAAU;0EAA6B,IAAI,OAAO;;;;;;4DACpD,IAAI,QAAQ,kBAAI,gPAAC;gEAAE,WAAU;0EAAyB,IAAI,QAAQ;;;;;;;;;;;;kEAErE,gPAAC;wDAAI,WAAU;kEACb,cAAA,gPAAC;;gEACE,WAAW,IAAI,SAAS;gEAAE;gEAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4CAItG,IAAI,WAAW,kBACd,gPAAC;gDAAI,WAAU;0DACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,gPAAC;wDAAc,WAAU;kEAAQ;uDAAzB;;;;;;;;;;;uCAhBN,IAAI,EAAE;;;;;;;;;;;;;;;;oBA2BvB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;0CACC,gPAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,gPAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,gPAAC;;0DACC,gPAAC;gDAAI,WAAU;;kEACb,gPAAC;;0EACC,gPAAC;gEAAG,WAAU;0EAAmC,IAAI,MAAM;;;;;;0EAC3D,gPAAC;gEAAE,WAAU;0EAA6B,IAAI,WAAW;;;;;;4DACxD,IAAI,YAAY,kBAAI,gPAAC;gEAAE,WAAU;0EAAyB,IAAI,YAAY;;;;;;;;;;;;kEAE7E,gPAAC;wDAAI,WAAU;kEACb,cAAA,gPAAC;;gEACE,WAAW,IAAI,SAAS;gEAAE;gEAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4CAItG,IAAI,WAAW,kBACd,gPAAC;gDAAE,WAAU;0DAAyC,IAAI,WAAW;;;;;;;uCAd/D,IAAI,EAAE;;;;;;;;;;;;;;;;kCAsBxB,gPAAC;wBAAI,WAAU;;4BAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,gPAAC;;kDACC,gPAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,gPAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,gPAAC;gDAAmB,WAAU;;kEAC5B,gPAAC;wDAAK,WAAU;kEAA6B,MAAM,IAAI;;;;;;oDACtD,MAAM,KAAK,kBACV,gPAAC;wDAAK,WAAU;kEAAoC,MAAM,KAAK;;;;;;;+CAHzD,MAAM,EAAE;;;;;;;;;;;;;;;;4BAYzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,gPAAC;;kDACC,gPAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,gPAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,gPAAC;gDAAkB,WAAU;;kEAC3B,gPAAC;wDAAK,WAAU;kEAA6B,KAAK,IAAI;;;;;;kEACtD,gPAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAFtD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnC", "debugId": null}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/TemplateRenderer.tsx"], "sourcesContent": ["import React from 'react'\nimport ModernTemplate from './ModernTemplate'\nimport ElegantTemplate from './ElegantTemplate'\nimport SimpleTemplate from './SimpleTemplate'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface TemplateRendererProps {\n  data: ResumeData\n}\n\nexport default function TemplateRenderer({ data }: TemplateRendererProps) {\n  switch (data.template) {\n    case 'modern':\n      return <ModernTemplate data={data} />\n    case 'elegant':\n      return <ElegantTemplate data={data} />\n    case 'simple':\n      return <SimpleTemplate data={data} />\n    default:\n      return <ModernTemplate data={data} />\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAgDe,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IACtE,OAAQ,KAAK,QAAQ;QACnB,KAAK;YACH,qBAAO,gPAAC,mJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;QAC/B,KAAK;YACH,qBAAO,gPAAC,oJAAA,CAAA,UAAe;gBAAC,MAAM;;;;;;QAChC,KAAK;YACH,qBAAO,gPAAC,mJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;QAC/B;YACE,qBAAO,gPAAC,mJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;IACjC;AACF", "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/api/resume/%5Bid%5D/pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport puppeteer from 'puppeteer'\nimport { renderToString } from 'react-dom/server'\nimport React from 'react'\nimport Template<PERSON>enderer from '@/components/templates/TemplateRenderer'\n\n// GET /api/resume/[id]/pdf - Generate and download PDF\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n\n    if (!session?.user?.id) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Check if resume exists and belongs to user\n    const resume = await prisma.resume.findFirst({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n      include: {\n        education: {\n          orderBy: { createdAt: 'asc' },\n        },\n        experience: {\n          orderBy: { createdAt: 'asc' },\n        },\n        skills: {\n          orderBy: { createdAt: 'asc' },\n        },\n        languages: {\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    })\n\n    if (!resume) {\n      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })\n    }\n\n    // Generate HTML content\n    const resumeComponent = React.createElement(TemplateRenderer, { data: resume })\n    const htmlContent = renderToString(resumeComponent)\n\n    const fullHtml = `\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>${resume.title}</title>\n          <script src=\"https://cdn.tailwindcss.com\"></script>\n          <style>\n            @media print {\n              body { margin: 0; }\n              .shadow-lg { box-shadow: none !important; }\n            }\n          </style>\n        </head>\n        <body>\n          ${htmlContent}\n        </body>\n      </html>\n    `\n\n    // Launch Puppeteer and generate PDF\n    const browser = await puppeteer.launch({\n      headless: true,\n      args: ['--no-sandbox', '--disable-setuid-sandbox'],\n    })\n\n    const page = await browser.newPage()\n    await page.setContent(fullHtml, { waitUntil: 'networkidle0' })\n\n    const pdf = await page.pdf({\n      format: 'A4',\n      printBackground: true,\n      margin: {\n        top: '0.5in',\n        right: '0.5in',\n        bottom: '0.5in',\n        left: '0.5in',\n      },\n    })\n\n    await browser.close()\n\n    // Return PDF as response\n    return new NextResponse(pdf, {\n      headers: {\n        'Content-Type': 'application/pdf',\n        'Content-Disposition': `attachment; filename=\"${resume.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf\"`,\n      },\n    })\n  } catch (error) {\n    console.error('Error generating PDF:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,6CAA6C;QAC7C,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3C,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,WAAW;oBACT,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,YAAY;oBACV,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,QAAQ;oBACN,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,WAAW;oBACT,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,wBAAwB;QACxB,MAAM,gCAAkB,uMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qJAAA,CAAA,UAAgB,EAAE;YAAE,MAAM;QAAO;QAC7E,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,iBAAc,AAAD,EAAE;QAEnC,MAAM,WAAW,CAAC;;;;;iBAKL,EAAE,OAAO,KAAK,CAAC;;;;;;;;;;UAUtB,EAAE,YAAY;;;IAGpB,CAAC;QAED,oCAAoC;QACpC,MAAM,UAAU,MAAM,kHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;YACrC,UAAU;YACV,MAAM;gBAAC;gBAAgB;aAA2B;QACpD;QAEA,MAAM,OAAO,MAAM,QAAQ,OAAO;QAClC,MAAM,KAAK,UAAU,CAAC,UAAU;YAAE,WAAW;QAAe;QAE5D,MAAM,MAAM,MAAM,KAAK,GAAG,CAAC;YACzB,QAAQ;YACR,iBAAiB;YACjB,QAAQ;gBACN,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,MAAM;YACR;QACF;QAEA,MAAM,QAAQ,KAAK;QAEnB,yBAAyB;QACzB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK;YAC3B,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB,CAAC,sBAAsB,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC;YACnG;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}