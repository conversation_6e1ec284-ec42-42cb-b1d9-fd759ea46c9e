import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer'
import { PDFTemplateProps, formatPDFDateRange } from './base-pdf-template'

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Helvetica'
  },
  header: {
    borderBottomWidth: 4,
    borderBottomColor: '#2563eb',
    paddingBottom: 20,
    marginBottom: 20
  },
  name: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8
  },
  contactInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    fontSize: 10,
    color: '#6b7280'
  },
  contactItem: {
    marginRight: 15
  },
  section: {
    marginBottom: 20
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2563eb',
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingBottom: 3
  },
  summary: {
    fontSize: 11,
    color: '#374151',
    lineHeight: 1.5
  },
  experienceItem: {
    marginBottom: 15
  },
  jobTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#111827'
  },
  company: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#2563eb',
    marginTop: 2
  },
  location: {
    fontSize: 10,
    color: '#6b7280',
    marginTop: 2
  },
  dateRange: {
    fontSize: 10,
    color: '#6b7280',
    marginTop: 2
  },
  description: {
    fontSize: 10,
    color: '#374151',
    marginTop: 5,
    lineHeight: 1.4
  },
  educationItem: {
    marginBottom: 12
  },
  degree: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#111827'
  },
  institution: {
    fontSize: 11,
    color: '#2563eb',
    marginTop: 2
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 20
  },
  skillsColumn: {
    flex: 1,
    minWidth: 200
  },
  skillItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    fontSize: 10
  },
  skillName: {
    color: '#111827'
  },
  skillLevel: {
    color: '#6b7280'
  }
})

export function ModernPDFTemplate({ data }: PDFTemplateProps) {
  const { personalInfo, education, experience, skills, languages } = data

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.name}>{personalInfo.fullName}</Text>
          <View style={styles.contactInfo}>
            {personalInfo.email && <Text style={styles.contactItem}>{personalInfo.email}</Text>}
            {personalInfo.phone && <Text style={styles.contactItem}>{personalInfo.phone}</Text>}
            {personalInfo.location && <Text style={styles.contactItem}>{personalInfo.location}</Text>}
            {personalInfo.website && <Text style={styles.contactItem}>{personalInfo.website}</Text>}
            {personalInfo.linkedin && <Text style={styles.contactItem}>LinkedIn</Text>}
          </View>
        </View>

        {/* Summary */}
        {personalInfo.summary && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Professional Summary</Text>
            <Text style={styles.summary}>{personalInfo.summary}</Text>
          </View>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Work Experience</Text>
            {experience.map((exp) => (
              <View key={exp.id} style={styles.experienceItem}>
                <Text style={styles.jobTitle}>{exp.position}</Text>
                <Text style={styles.company}>{exp.company}</Text>
                {exp.location && <Text style={styles.location}>{exp.location}</Text>}
                <Text style={styles.dateRange}>
                  {formatPDFDateRange(exp.startDate, exp.endDate, exp.current)}
                </Text>
                <Text style={styles.description}>{exp.description}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Education */}
        {education.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Education</Text>
            {education.map((edu) => (
              <View key={edu.id} style={styles.educationItem}>
                <Text style={styles.degree}>{edu.degree} in {edu.field}</Text>
                <Text style={styles.institution}>{edu.institution}</Text>
                <Text style={styles.dateRange}>
                  {formatPDFDateRange(edu.startDate, edu.endDate, edu.current)}
                </Text>
                {edu.description && (
                  <Text style={styles.description}>{edu.description}</Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Skills and Languages */}
        {(skills.length > 0 || languages.length > 0) && (
          <View style={styles.skillsGrid}>
            {skills.length > 0 && (
              <View style={styles.skillsColumn}>
                <Text style={styles.sectionTitle}>Skills</Text>
                {skills.map((skill) => (
                  <View key={skill.id} style={styles.skillItem}>
                    <Text style={styles.skillName}>{skill.name}</Text>
                    <Text style={styles.skillLevel}>{skill.level}</Text>
                  </View>
                ))}
              </View>
            )}

            {languages.length > 0 && (
              <View style={styles.skillsColumn}>
                <Text style={styles.sectionTitle}>Languages</Text>
                {languages.map((lang) => (
                  <View key={lang.id} style={styles.skillItem}>
                    <Text style={styles.skillName}>{lang.name}</Text>
                    <Text style={styles.skillLevel}>{lang.proficiency}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </Page>
    </Document>
  )
}
