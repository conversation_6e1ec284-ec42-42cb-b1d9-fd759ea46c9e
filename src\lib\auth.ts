import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        // Temporary demo authentication - replace with real database later
        if (!credentials?.email || !credentials?.password) {
          console.log('Missing credentials')
          return null
        }

        console.log('Login attempt:', credentials.email)

        // Demo users for testing - remove in production
        const demoUsers = [
          {
            id: '1',
            email: '<EMAIL>',
            password: 'demo123',
            name: 'Demo User',
            isPremium: false
          },
          {
            id: '2',
            email: '<EMAIL>',
            password: 'admin123',
            name: 'Admin User',
            isPremium: true
          },
          {
            id: '3',
            email: '<EMAIL>',
            password: 'test123',
            name: 'Test User',
            isPremium: false
          }
        ]

        const user = demoUsers.find(u => u.email === credentials.email && u.password === credentials.password)

        if (user) {
          console.log('User authenticated:', user.email)
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            isPremium: user.isPremium
          }
        }

        console.log('Invalid credentials for:', credentials.email)
        return null
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.isPremium = (user as any).isPremium || false
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        ;(session.user as any).isPremium = token.isPremium as boolean
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/login',
    signUp: '/auth/register'
  },
  secret: process.env.NEXTAUTH_SECRET
}
