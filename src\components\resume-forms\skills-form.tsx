import { useState } from 'react'
import { Skill, ResumeData } from '@/types/resume'

interface SkillsFormProps {
  data: ResumeData
  onUpdateSkills: (skills: Skill[]) => void
}

export function SkillsForm({ data, onUpdateSkills }: SkillsFormProps) {
  const { skills } = data

  const addSkill = () => {
    const newSkill: Skill = {
      id: Date.now().toString(),
      name: '',
      level: 'Intermediate',
      category: ''
    }
    onUpdateSkills([...skills, newSkill])
  }

  const updateSkill = (id: string, field: keyof Skill, value: string) => {
    const updated = skills.map(skill => 
      skill.id === id ? { ...skill, [field]: value } : skill
    )
    onUpdateSkills(updated)
  }

  const removeSkill = (id: string) => {
    onUpdateSkills(skills.filter(skill => skill.id !== id))
  }

  const skillLevels: Skill['level'][] = ['Beginner', 'Intermediate', 'Advanced', 'Expert']

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Add your technical and soft skills. You can organize them by categories like "Programming Languages", "Frameworks", "Tools", etc.
      </div>

      {skills.map((skill, index) => (
        <div key={skill.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-700 transition-colors duration-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Skill {index + 1}
            </h3>
            <button
              onClick={() => removeSkill(skill.id)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm transition-colors"
            >
              Remove
            </button>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Skill Name *
              </label>
              <input
                type="text"
                value={skill.name}
                onChange={(e) => updateSkill(skill.id, 'name', e.target.value)}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black dark:text-white bg-white dark:bg-gray-700 transition-colors duration-200"
                placeholder="JavaScript"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Proficiency Level *
              </label>
              <select
                value={skill.level}
                onChange={(e) => updateSkill(skill.id, 'level', e.target.value)}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black dark:text-white bg-white dark:bg-gray-700 transition-colors duration-200"
                required
              >
                {skillLevels.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <input
                type="text"
                value={skill.category || ''}
                onChange={(e) => updateSkill(skill.id, 'category', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black"
                placeholder="Programming Languages"
              />
            </div>
          </div>
        </div>
      ))}

      <button
        onClick={addSkill}
        className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <span className="text-gray-600">+ Add Skill</span>
      </button>

      {skills.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No skills added yet</p>
          <button
            onClick={addSkill}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors"
          >
            Add Your First Skill
          </button>
        </div>
      )}
    </div>
  )
}
