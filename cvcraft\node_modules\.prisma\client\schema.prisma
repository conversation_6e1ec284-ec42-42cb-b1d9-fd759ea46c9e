// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  resumes Resume[]
}

model Resume {
  id     String @id @default(cuid())
  title  String
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Personal Information
  fullName     String
  email        String
  phone        String?
  address      String?
  profileImage String?
  summary      String?

  // Template
  template String @default("modern")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Related data
  education  Education[]
  experience Experience[]
  skills     Skill[]
  languages  Language[]
}

model Education {
  id       String @id @default(cuid())
  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  institution  String
  degree       String
  fieldOfStudy String?
  startDate    String
  endDate      String?
  current      Boolean @default(false)
  description  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Experience {
  id       String @id @default(cuid())
  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  company     String
  position    String
  location    String?
  startDate   String
  endDate     String?
  current     Boolean @default(false)
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Skill {
  id       String @id @default(cuid())
  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  name  String
  level String? // beginner, intermediate, advanced, expert

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Language {
  id       String @id @default(cuid())
  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  name  String
  level String // native, fluent, conversational, basic

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
