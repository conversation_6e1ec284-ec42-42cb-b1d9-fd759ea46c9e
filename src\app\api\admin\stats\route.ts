import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { withCache, CacheKeys, CacheTTL } from '@/lib/cache'
import { monitoring } from '@/lib/monitoring'

export async function GET() {
  const timer = monitoring.startTimer('api:admin:stats')

  try {
    const session = await getServerSession(authOptions)

    // Simple admin check - in production, you'd want proper role-based access
    if (session?.user?.email !== '<EMAIL>') {
      timer.end({ success: false, error: 'Unauthorized' })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Use cache for admin stats
    const stats = await withCache(
      CacheKeys.adminStats(),
      async () => {

    // Get total users
    const totalUsers = await prisma.user.count()

    // Get premium users
    const premiumUsers = await prisma.user.count({
      where: { isPremium: true }
    })

    // Get total resumes
    const totalResumes = await prisma.resume.count()

    // Get active subscriptions
    const totalSubscriptions = await prisma.subscription.count({
      where: { status: 'active' }
    })

    // Get recent users
    const recentUsers = await prisma.user.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        isPremium: true,
        createdAt: true
      }
    })

        return {
          totalUsers,
          premiumUsers,
          totalResumes,
          totalSubscriptions,
          recentUsers
        }
      },
      CacheTTL.MEDIUM // Cache for 5 minutes
    )

    timer.end({ success: true })
    return NextResponse.json(stats)
  } catch (error) {
    timer.end({ success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    monitoring.logError(error instanceof Error ? error : new Error(String(error)))
    console.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
