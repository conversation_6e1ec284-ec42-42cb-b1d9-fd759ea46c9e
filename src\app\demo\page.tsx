'use client'

import { useState } from 'react'
import { PersonalInfoForm } from '@/components/resume-forms/personal-info-form'
import { ModernTemplate } from '@/components/templates/modern-template'
import { ResumeData } from '@/types/resume'
import { ThemeToggle } from '@/components/theme-toggle'

export default function DemoPage() {
  const [resumeData, setResumeData] = useState<ResumeData>({
    personalInfo: {
      fullName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      website: 'https://johndoe.com',
      linkedin: 'https://linkedin.com/in/johndoe',
      summary: 'Experienced software developer with a passion for creating innovative solutions.',
      photo: undefined
    },
    education: [],
    experience: [],
    skills: [],
    languages: []
  })

  const handlePersonalInfoUpdate = (personalInfo: ResumeData['personalInfo']) => {
    setResumeData(prev => ({
      ...prev,
      personalInfo
    }))
  }

  const downloadPDF = async () => {
    try {
      console.log('Starting PDF download...')

      // Try server-side PDF generation first
      try {
        const response = await fetch('/api/generate-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            resumeData,
            template: 'modern'
          })
        })

        console.log('Response status:', response.status)

        if (response.ok) {
          // Get the PDF blob
          const blob = await response.blob()

          // Validate blob
          if (blob.size === 0) {
            throw new Error('Received empty PDF')
          }

          // Create download link
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${resumeData.personalInfo.fullName.replace(/[^a-zA-Z0-9]/g, '_')}_Resume.pdf`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)

          console.log('PDF downloaded successfully')
          return
        } else {
          const errorText = await response.text()
          console.error('Server PDF generation failed:', errorText)
          throw new Error('Server PDF generation failed')
        }
      } catch (serverError) {
        console.log('Server PDF failed, trying client-side generation...')

        // Fallback to client-side PDF generation using jsPDF
        const { jsPDF } = await import('jspdf')

        const doc = new jsPDF()

        // Add content with proper headers as suggested
        doc.setFontSize(24)
        doc.setTextColor(37, 99, 235)
        doc.text(resumeData.personalInfo.fullName, 105, 30, { align: 'center' })

        doc.setFontSize(12)
        doc.setTextColor(0, 0, 0)
        doc.text(`Email: ${resumeData.personalInfo.email}`, 105, 45, { align: 'center' })
        doc.text(`Phone: ${resumeData.personalInfo.phone}`, 105, 55, { align: 'center' })
        doc.text(`Location: ${resumeData.personalInfo.location}`, 105, 65, { align: 'center' })

        // Add line
        doc.setLineWidth(0.5)
        doc.setDrawColor(37, 99, 235)
        doc.line(20, 75, 190, 75)

        // Add summary if exists
        if (resumeData.personalInfo.summary) {
          doc.setFontSize(16)
          doc.setTextColor(37, 99, 235)
          doc.text('Professional Summary', 20, 90)

          doc.setFontSize(11)
          doc.setTextColor(0, 0, 0)
          const splitSummary = doc.splitTextToSize(resumeData.personalInfo.summary, 170)
          doc.text(splitSummary, 20, 105)
        }

        // Save the PDF with proper filename
        const filename = `${resumeData.personalInfo.fullName.replace(/[^a-zA-Z0-9]/g, '_')}_Resume.pdf`
        doc.save(filename)

        console.log('Client-side PDF generated successfully')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      alert('Error downloading PDF. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black transition-colors duration-300">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-xl font-bold text-blue-600 dark:text-blue-400">CVCraft Demo</h1>
              <p className="text-sm text-gray-600 dark:text-gray-300">Try the photo upload feature</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={downloadPDF}
                className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Download PDF
              </button>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8">
            {/* Left Column - Form */}
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 transition-colors duration-300">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Personal Information
                </h2>
                <PersonalInfoForm
                  personalInfo={resumeData.personalInfo}
                  onUpdatePersonalInfo={handlePersonalInfoUpdate}
                />
              </div>
            </div>

            {/* Right Column - Preview */}
            <div className="mt-8 lg:mt-0">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Live Preview</h3>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
                    <ModernTemplate data={resumeData} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
