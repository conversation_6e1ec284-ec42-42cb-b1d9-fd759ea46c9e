'use client'

import { useState } from 'react'
import { ErrorBoundary } from '@/components/error-boundary'

export default function SimpleDemoPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generateClientPDF = async () => {
    setIsGenerating(true)
    setError(null)
    
    try {
      // Dynamic import to avoid SSR issues
      const { jsPDF } = await import('jspdf')
      
      const doc = new jsPDF()
      
      // Header
      doc.setFontSize(24)
      doc.setTextColor(37, 99, 235)
      doc.text('<PERSON>', 105, 30, { align: 'center' })
      
      // Contact info
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.text('Email: <EMAIL>', 105, 45, { align: 'center' })
      doc.text('Phone: ************', 105, 55, { align: 'center' })
      doc.text('Location: New York, NY', 105, 65, { align: 'center' })
      
      // Line separator
      doc.setLineWidth(0.5)
      doc.setDrawColor(37, 99, 235)
      doc.line(20, 75, 190, 75)
      
      // Summary
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Professional Summary', 20, 90)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      const summary = 'Experienced software developer with expertise in modern web technologies.'
      const splitSummary = doc.splitTextToSize(summary, 170)
      doc.text(splitSummary, 20, 105)
      
      // Experience
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Experience', 20, 130)
      
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.setFont('helvetica', 'bold')
      doc.text('Senior Software Developer', 20, 145)
      
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(10)
      doc.setTextColor(100, 100, 100)
      doc.text('Tech Solutions Inc. • 2022 - Present', 20, 155)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      doc.text('• Led development of web applications', 20, 170)
      doc.text('• Collaborated with cross-functional teams', 20, 180)
      
      // Skills
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Skills', 20, 205)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      doc.text('JavaScript - Expert', 20, 220)
      doc.text('React - Expert', 105, 220)
      doc.text('Node.js - Advanced', 20, 230)
      doc.text('TypeScript - Advanced', 105, 230)
      
      // Save
      doc.save('John_Doe_Resume.pdf')
      
      alert('✅ PDF downloaded successfully!')
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsGenerating(false)
    }
  }

  const testServerPDF = async () => {
    setIsGenerating(true)
    setError(null)
    
    try {
      const response = await fetch('/api/test-pdf', {
        method: 'GET'
      })
      
      if (response.ok) {
        const blob = await response.blob()
        
        if (blob.size === 0) {
          throw new Error('Received empty PDF from server')
        }
        
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'server-test.pdf'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        alert('✅ Server PDF downloaded successfully!')
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Server error' }))
        throw new Error(`Server error: ${errorData.error || response.statusText}`)
      }
    } catch (error) {
      console.error('Server PDF failed:', error)
      setError(error instanceof Error ? error.message : 'Server error')
      
      // Fallback to client PDF
      alert('Server failed, trying client-side PDF...')
      await generateClientPDF()
    } finally {
      setIsGenerating(false)
    }
  }

  const printPage = () => {
    window.print()
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Simple PDF Demo - Error Fixed
            </h1>
            <p className="text-gray-600">
              Multiple PDF generation methods with error handling
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="text-red-400 text-xl mr-3">⚠️</div>
                <div>
                  <h3 className="text-red-800 font-medium">Error occurred</h3>
                  <p className="text-red-700 text-sm mt-1">{error}</p>
                  <button
                    onClick={() => setError(null)}
                    className="text-red-600 hover:text-red-800 text-sm underline mt-2"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Preview */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Resume Preview</h2>
              <div className="border rounded-lg p-6 bg-gray-50">
                <div className="text-center border-b-4 border-blue-600 pb-4 mb-4">
                  <h3 className="text-2xl font-bold text-gray-900">John Doe</h3>
                  <div className="text-gray-600 mt-2 space-y-1">
                    <p>📧 <EMAIL></p>
                    <p>📱 ************</p>
                    <p>📍 New York, NY</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-lg font-semibold text-blue-600 mb-2">Professional Summary</h4>
                    <p className="text-gray-700 text-sm">
                      Experienced software developer with expertise in modern web technologies.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-semibold text-blue-600 mb-2">Experience</h4>
                    <div className="text-sm">
                      <p className="font-medium">Senior Software Developer</p>
                      <p className="text-gray-600">Tech Solutions Inc. • 2022 - Present</p>
                      <ul className="list-disc list-inside text-gray-700 mt-1">
                        <li>Led development of web applications</li>
                        <li>Collaborated with cross-functional teams</li>
                      </ul>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-semibold text-blue-600 mb-2">Skills</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>JavaScript - Expert</div>
                      <div>React - Expert</div>
                      <div>Node.js - Advanced</div>
                      <div>TypeScript - Advanced</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">PDF Generation</h2>
              
              <div className="space-y-4">
                <button
                  onClick={generateClientPDF}
                  disabled={isGenerating}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  {isGenerating ? '⏳ Generating...' : '📄 Download PDF (Client-Side)'}
                </button>

                <button
                  onClick={testServerPDF}
                  disabled={isGenerating}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  {isGenerating ? '⏳ Testing...' : '🧪 Test Server PDF (with Fallback)'}
                </button>

                <button
                  onClick={printPage}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  🖨️ Print Page (Browser PDF)
                </button>

                <div className="bg-green-50 p-4 rounded-md">
                  <h3 className="font-semibold text-green-800 mb-2">✅ Error Fixes Applied:</h3>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Dynamic imports to avoid SSR issues</li>
                    <li>• Error boundaries for React errors</li>
                    <li>• Fallback PDF generation</li>
                    <li>• Proper error handling and display</li>
                    <li>• NextAuth configuration fixed</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  )
}
