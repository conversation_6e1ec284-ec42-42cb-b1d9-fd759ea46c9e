import { ResumeData, TemplateType } from '@/types/resume'
import { ModernPDFTemplate } from './pdf-templates/modern-pdf-template'
import { ElegantPDFTemplate } from './pdf-templates/elegant-pdf-template'
import { MinimalPDFTemplate } from './pdf-templates/minimal-pdf-template'

interface PDFRendererProps {
  data: ResumeData
  template: TemplateType
}

export function PDFRenderer({ data, template }: PDFRendererProps) {
  switch (template) {
    case 'modern':
      return <ModernPDFTemplate data={data} />
    case 'elegant':
      return <ElegantPDFTemplate data={data} />
    case 'minimal':
      return <MinimalPDFTemplate data={data} />
    default:
      return <ModernPDFTemplate data={data} />
  }
}
