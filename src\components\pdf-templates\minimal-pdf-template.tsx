import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer'
import { PDFTemplateProps, formatPDFDateRange } from './base-pdf-template'

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontFamily: 'Helvetica'
  },
  header: {
    textAlign: 'center',
    marginBottom: 40
  },
  name: {
    fontSize: 24,
    fontWeight: 'light',
    color: '#111827',
    marginBottom: 15
  },
  contactInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 20,
    fontSize: 10,
    color: '#6b7280'
  },
  contactItem: {
    marginHorizontal: 10
  },
  summary: {
    textAlign: 'center',
    fontSize: 11,
    color: '#374151',
    lineHeight: 1.5,
    fontStyle: 'italic',
    marginBottom: 35
  },
  section: {
    marginBottom: 35
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'light',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 20,
    textTransform: 'uppercase',
    letterSpacing: 2
  },
  experienceItem: {
    textAlign: 'center',
    marginBottom: 25
  },
  jobTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 3
  },
  company: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 3
  },
  jobDetails: {
    fontSize: 10,
    color: '#9ca3af',
    marginBottom: 10
  },
  description: {
    fontSize: 10,
    color: '#374151',
    lineHeight: 1.4,
    maxWidth: 400,
    marginHorizontal: 'auto'
  },
  educationItem: {
    textAlign: 'center',
    marginBottom: 20
  },
  degree: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 3
  },
  institution: {
    fontSize: 11,
    color: '#6b7280',
    marginBottom: 3
  },
  educationDate: {
    fontSize: 10,
    color: '#9ca3af'
  },
  skillsGrid: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 40
  },
  skillsColumn: {
    alignItems: 'center'
  },
  skillItem: {
    textAlign: 'center',
    marginBottom: 10
  },
  skillName: {
    fontSize: 11,
    color: '#111827',
    marginBottom: 2
  },
  skillLevel: {
    fontSize: 8,
    color: '#9ca3af'
  },
  languageItem: {
    textAlign: 'center',
    marginBottom: 10
  },
  languageName: {
    fontSize: 11,
    color: '#111827',
    marginBottom: 2
  },
  languageLevel: {
    fontSize: 8,
    color: '#9ca3af'
  }
})

export function MinimalPDFTemplate({ data }: PDFTemplateProps) {
  const { personalInfo, education, experience, skills, languages } = data

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.name}>{personalInfo.fullName}</Text>
          <View style={styles.contactInfo}>
            {personalInfo.email && <Text style={styles.contactItem}>{personalInfo.email}</Text>}
            {personalInfo.phone && <Text style={styles.contactItem}>{personalInfo.phone}</Text>}
            {personalInfo.location && <Text style={styles.contactItem}>{personalInfo.location}</Text>}
            {personalInfo.website && <Text style={styles.contactItem}>{personalInfo.website}</Text>}
            {personalInfo.linkedin && <Text style={styles.contactItem}>LinkedIn</Text>}
          </View>
        </View>

        {/* Summary */}
        {personalInfo.summary && (
          <Text style={styles.summary}>{personalInfo.summary}</Text>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Experience</Text>
            {experience.map((exp) => (
              <View key={exp.id} style={styles.experienceItem}>
                <Text style={styles.jobTitle}>{exp.position}</Text>
                <Text style={styles.company}>{exp.company}</Text>
                <Text style={styles.jobDetails}>
                  {formatPDFDateRange(exp.startDate, exp.endDate, exp.current)}
                  {exp.location && ` • ${exp.location}`}
                </Text>
                <Text style={styles.description}>{exp.description}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Education */}
        {education.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Education</Text>
            {education.map((edu) => (
              <View key={edu.id} style={styles.educationItem}>
                <Text style={styles.degree}>{edu.degree} in {edu.field}</Text>
                <Text style={styles.institution}>{edu.institution}</Text>
                <Text style={styles.educationDate}>
                  {formatPDFDateRange(edu.startDate, edu.endDate, edu.current)}
                </Text>
                {edu.description && (
                  <Text style={styles.description}>{edu.description}</Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Skills and Languages */}
        {(skills.length > 0 || languages.length > 0) && (
          <View style={styles.skillsGrid}>
            {skills.length > 0 && (
              <View style={styles.skillsColumn}>
                <Text style={styles.sectionTitle}>Skills</Text>
                {skills.map((skill) => (
                  <View key={skill.id} style={styles.skillItem}>
                    <Text style={styles.skillName}>{skill.name}</Text>
                    <Text style={styles.skillLevel}>{skill.level}</Text>
                  </View>
                ))}
              </View>
            )}

            {languages.length > 0 && (
              <View style={styles.skillsColumn}>
                <Text style={styles.sectionTitle}>Languages</Text>
                {languages.map((lang) => (
                  <View key={lang.id} style={styles.languageItem}>
                    <Text style={styles.languageName}>{lang.name}</Text>
                    <Text style={styles.languageLevel}>{lang.proficiency}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </Page>
    </Document>
  )
}
