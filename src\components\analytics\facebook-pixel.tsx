'use client'

import Script from 'next/script'
import { usePathname } from 'next/navigation'
import { useEffect } from 'react'

const FB_PIXEL_ID = process.env.NEXT_PUBLIC_FB_PIXEL_ID

declare global {
  interface Window {
    fbq: (command: string, ...args: any[]) => void
    _fbq: any
  }
}

export function FacebookPixel() {
  const pathname = usePathname()

  useEffect(() => {
    if (!FB_PIXEL_ID || !window.fbq) return

    window.fbq('track', 'PageView')
  }, [pathname])

  if (!FB_PIXEL_ID) {
    return null
  }

  return (
    <>
      <Script
        id="facebook-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${FB_PIXEL_ID}');
            fbq('track', 'PageView');
          `,
        }}
      />
      <noscript>
        <img
          height="1"
          width="1"
          style={{ display: 'none' }}
          src={`https://www.facebook.com/tr?id=${FB_PIXEL_ID}&ev=PageView&noscript=1`}
          alt=""
        />
      </noscript>
    </>
  )
}

// Facebook Pixel event tracking
export const fbPixel = {
  // Track custom events
  event: (eventName: string, parameters?: Record<string, any>) => {
    if (!window.fbq) return
    window.fbq('track', eventName, parameters)
  },

  // Track user registration
  completeRegistration: () => {
    fbPixel.event('CompleteRegistration')
  },

  // Track subscription purchase
  purchase: (value: number, currency: string = 'USD') => {
    fbPixel.event('Purchase', {
      value,
      currency,
    })
  },

  // Track lead generation (free signup)
  lead: () => {
    fbPixel.event('Lead')
  },

  // Track resume creation
  addToCart: (contentName: string, value?: number) => {
    fbPixel.event('AddToCart', {
      content_name: contentName,
      value,
      currency: 'USD',
    })
  },

  // Track template view
  viewContent: (contentName: string, contentType: string = 'template') => {
    fbPixel.event('ViewContent', {
      content_name: contentName,
      content_type: contentType,
    })
  },

  // Track subscription initiation
  initiateCheckout: (value: number) => {
    fbPixel.event('InitiateCheckout', {
      value,
      currency: 'USD',
    })
  },
}
