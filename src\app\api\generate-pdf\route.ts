import { NextRequest, NextResponse } from 'next/server'
import { ResumeData } from '@/types/resume'
import puppeteer from 'puppeteer'

export async function POST(request: NextRequest) {
  let browser = null

  try {
    console.log('PDF generation API called')

    const body = await request.json()
    console.log('Request body received')

    const { resumeData, template = 'modern' } = body as {
      resumeData: ResumeData
      template?: string
    }

    if (!resumeData || !resumeData.personalInfo) {
      console.log('Invalid resume data provided')
      return NextResponse.json({ error: 'Valid resume data is required' }, { status: 400 })
    }

    console.log('Generating PDF for:', resumeData.personalInfo.fullName)

    // Create HTML content directly
    const htmlContent = generateResumeHTML(resumeData, template)

    console.log('HTML content generated, length:', htmlContent.length)

    // Launch Puppeteer with comprehensive error handling
    console.log('Launching Puppeteer browser...')
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-extensions'
      ]
    })

    console.log('Browser launched successfully')

    const page = await browser.newPage()

    // Set viewport for consistent rendering
    await page.setViewport({ width: 1200, height: 1600 })

    // Set HTML content directly instead of navigating to URL
    try {
      console.log('Setting HTML content...')
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })
      console.log('HTML content set successfully')
    } catch (contentError) {
      console.error('Failed to set HTML content:', contentError)
      await browser.close()
      throw new Error('Failed to set resume content for PDF generation')
    }

    // Generate PDF with proper error handling
    console.log('Generating PDF...')
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      preferCSSPageSize: false
    })

    await browser.close()
    browser = null

    console.log('PDF generated successfully, size:', pdf.length, 'bytes')

    // Validate PDF content
    if (!pdf || pdf.length === 0) {
      throw new Error('Generated PDF is empty')
    }

    // Create safe filename
    const safeFilename = resumeData.personalInfo.fullName
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 50) || 'Resume'

    // Return PDF with proper headers as suggested
    return new NextResponse(pdf, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${safeFilename}_Resume.pdf"`,
        'Content-Length': pdf.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    // Ensure browser is closed on error
    if (browser) {
      try {
        await browser.close()
      } catch (closeError) {
        console.error('Error closing browser:', closeError)
      }
    }

    console.error('Error generating PDF:', error)

    // Return proper error response with headers
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    const errorDetails = error instanceof Error ? error.stack : undefined

    console.error('PDF Generation Error Details:', {
      message: errorMessage,
      stack: errorDetails
    })

    return NextResponse.json(
      {
        error: 'Failed to generate PDF',
        message: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : undefined
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      }
    )
  }
}

function generateResumeHTML(data: ResumeData, template: string): string {
  const { personalInfo, education, experience, skills, languages } = data

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>${personalInfo.fullName} - Resume</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          @media print {
            body {
              margin: 0 !important;
              padding: 0 !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }

            @page {
              size: A4;
              margin: 0.5in;
            }

            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
          }
        </style>
      </head>
      <body class="bg-white">
        <div class="max-w-4xl mx-auto p-8">
          <!-- Header -->
          <div class="border-b-4 border-blue-600 pb-6 mb-6">
            <div class="flex items-start gap-6">
              ${personalInfo.photo ? `
                <div class="flex-shrink-0">
                  <div class="w-24 h-24 rounded-full overflow-hidden border-4 border-blue-600">
                    <img src="${personalInfo.photo}" alt="${personalInfo.fullName}" class="w-full h-full object-cover" />
                  </div>
                </div>
              ` : ''}
              <div class="flex-1">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">
                  ${personalInfo.fullName}
                </h1>
                <div class="flex flex-wrap gap-4 text-gray-600">
                  ${personalInfo.email ? `<span>📧 ${personalInfo.email}</span>` : ''}
                  ${personalInfo.phone ? `<span>📱 ${personalInfo.phone}</span>` : ''}
                  ${personalInfo.location ? `<span>📍 ${personalInfo.location}</span>` : ''}
                  ${personalInfo.website ? `<span>🌐 ${personalInfo.website}</span>` : ''}
                  ${personalInfo.linkedin ? `<span>💼 LinkedIn</span>` : ''}
                </div>
              </div>
            </div>
          </div>

          ${personalInfo.summary ? `
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-blue-600 mb-4 border-b-2 border-gray-200 pb-2">
                Professional Summary
              </h2>
              <p class="text-gray-700">${personalInfo.summary}</p>
            </div>
          ` : ''}

          ${experience.length > 0 ? `
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-blue-600 mb-4 border-b-2 border-gray-200 pb-2">
                Experience
              </h2>
              ${experience.map(exp => `
                <div class="mb-6">
                  <h3 class="text-xl font-semibold text-gray-900">${exp.position}</h3>
                  <div class="text-gray-600 mb-2">
                    <strong>${exp.company}</strong> ${exp.location ? `• ${exp.location}` : ''} •
                    ${formatDateRange(exp.startDate, exp.endDate, exp.current)}
                  </div>
                  ${exp.description ? `<p class="text-gray-700">${exp.description.replace(/\n/g, '<br>')}</p>` : ''}
                </div>
              `).join('')}
            </div>
          ` : ''}

          ${education.length > 0 ? `
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-blue-600 mb-4 border-b-2 border-gray-200 pb-2">
                Education
              </h2>
              ${education.map(edu => `
                <div class="mb-6">
                  <h3 class="text-xl font-semibold text-gray-900">${edu.degree} in ${edu.field}</h3>
                  <div class="text-gray-600 mb-2">
                    <strong>${edu.institution}</strong> • ${formatDateRange(edu.startDate, edu.endDate, edu.current)}
                  </div>
                  ${edu.description ? `<p class="text-gray-700">${edu.description}</p>` : ''}
                </div>
              `).join('')}
            </div>
          ` : ''}

          ${skills.length > 0 ? `
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-blue-600 mb-4 border-b-2 border-gray-200 pb-2">
                Skills
              </h2>
              <div class="grid grid-cols-2 gap-4">
                ${skills.map(skill => `
                  <div class="flex justify-between">
                    <span class="font-medium">${skill.name}</span>
                    <span class="text-gray-600">${skill.level}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          ` : ''}

          ${languages.length > 0 ? `
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-blue-600 mb-4 border-b-2 border-gray-200 pb-2">
                Languages
              </h2>
              <div class="grid grid-cols-2 gap-4">
                ${languages.map(lang => `
                  <div class="flex justify-between">
                    <span class="font-medium">${lang.name}</span>
                    <span class="text-gray-600">${lang.proficiency}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </body>
    </html>
  `
}

function formatDateRange(startDate: string, endDate: string, current: boolean): string {
  const start = new Date(startDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short'
  })

  if (current) {
    return `${start} - Present`
  }

  const end = new Date(endDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short'
  })

  return `${start} - ${end}`
}


