{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token }) => !!token\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    '/dashboard/:path*',\n    '/resume/:path*',\n    '/api/resume/:path*',\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC,CAAC;IAC/B;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;KACD;AACH"}}]}