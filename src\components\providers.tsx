'use client'

import { SessionProvider } from 'next-auth/react'
import { ThemeProvider } from '@/contexts/theme-context'

export function Providers({ children }: { children: React.ReactNode }) {
  try {
    return (
      <SessionProvider>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </SessionProvider>
    )
  } catch (error) {
    console.error('Providers error:', error)
    // Fallback without providers
    return <>{children}</>
  }
}
