'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import PersonalInfoStep from '@/components/resume-form/PersonalInfoStep'
import EducationStep from '@/components/resume-form/EducationStep'
import ExperienceStep from '@/components/resume-form/ExperienceStep'
import SkillsStep from '@/components/resume-form/SkillsStep'
import LanguagesStep from '@/components/resume-form/LanguagesStep'
import TemplateStep from '@/components/resume-form/TemplateStep'

const resumeSchema = z.object({
  // Personal Information
  title: z.string().min(1, 'Resume title is required'),
  fullName: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  address: z.string().optional(),
  profileImage: z.string().optional(),
  summary: z.string().optional(),
  
  // Education
  education: z.array(z.object({
    institution: z.string().min(1, 'Institution is required'),
    degree: z.string().min(1, 'Degree is required'),
    fieldOfStudy: z.string().optional(),
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().optional(),
    current: z.boolean().default(false),
    description: z.string().optional(),
  })).default([]),
  
  // Experience
  experience: z.array(z.object({
    company: z.string().min(1, 'Company is required'),
    position: z.string().min(1, 'Position is required'),
    location: z.string().optional(),
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().optional(),
    current: z.boolean().default(false),
    description: z.string().optional(),
  })).default([]),
  
  // Skills
  skills: z.array(z.object({
    name: z.string().min(1, 'Skill name is required'),
    level: z.string().optional(),
  })).default([]),
  
  // Languages
  languages: z.array(z.object({
    name: z.string().min(1, 'Language name is required'),
    level: z.string().min(1, 'Language level is required'),
  })).default([]),
  
  // Template
  template: z.string().default('modern'),
})

export type ResumeFormData = z.infer<typeof resumeSchema>

const steps = [
  { id: 1, name: 'Personal Info', component: PersonalInfoStep },
  { id: 2, name: 'Education', component: EducationStep },
  { id: 3, name: 'Experience', component: ExperienceStep },
  { id: 4, name: 'Skills', component: SkillsStep },
  { id: 5, name: 'Languages', component: LanguagesStep },
  { id: 6, name: 'Template', component: TemplateStep },
]

export default function CreateResumePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const methods = useForm<ResumeFormData>({
    resolver: zodResolver(resumeSchema),
    defaultValues: {
      title: '',
      fullName: '',
      email: session?.user?.email || '',
      phone: '',
      address: '',
      profileImage: '',
      summary: '',
      education: [],
      experience: [],
      skills: [],
      languages: [],
      template: 'modern',
    },
  })

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!session) {
    router.push('/auth/signin')
    return null
  }

  const handleNext = async () => {
    const currentStepFields = getCurrentStepFields()
    const isValid = await methods.trigger(currentStepFields)
    
    if (isValid) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1)
      } else {
        await handleSubmit()
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const getCurrentStepFields = (): (keyof ResumeFormData)[] => {
    switch (currentStep) {
      case 1:
        return ['title', 'fullName', 'email']
      case 2:
        return ['education']
      case 3:
        return ['experience']
      case 4:
        return ['skills']
      case 5:
        return ['languages']
      case 6:
        return ['template']
      default:
        return []
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      const formData = methods.getValues()
      
      // Create the resume
      const resumeResponse = await fetch('/api/resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          profileImage: formData.profileImage,
          summary: formData.summary,
          template: formData.template,
        }),
      })

      if (!resumeResponse.ok) {
        throw new Error('Failed to create resume')
      }

      const resume = await resumeResponse.json()

      // Add education, experience, skills, and languages
      await Promise.all([
        ...formData.education.map(edu => 
          fetch(`/api/resume/${resume.id}/education`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(edu),
          })
        ),
        ...formData.experience.map(exp => 
          fetch(`/api/resume/${resume.id}/experience`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(exp),
          })
        ),
        ...formData.skills.map(skill => 
          fetch(`/api/resume/${resume.id}/skills`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(skill),
          })
        ),
        ...formData.languages.map(lang => 
          fetch(`/api/resume/${resume.id}/languages`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(lang),
          })
        ),
      ])

      router.push(`/resume/preview/${resume.id}`)
    } catch (error) {
      console.error('Error creating resume:', error)
      alert('Failed to create resume. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const CurrentStepComponent = steps[currentStep - 1].component

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-2xl font-bold text-indigo-600">Create Resume</h1>
            <div className="text-sm text-gray-500">
              Step {currentStep} of {steps.length}
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex items-center">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                      currentStep >= step.id
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-300 text-gray-500'
                    }`}
                  >
                    {step.id}
                  </div>
                  <span
                    className={`ml-2 text-sm font-medium ${
                      currentStep >= step.id ? 'text-indigo-600' : 'text-gray-500'
                    }`}
                  >
                    {step.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div
                      className={`ml-4 w-16 h-0.5 ${
                        currentStep > step.id ? 'bg-indigo-600' : 'bg-gray-300'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <FormProvider {...methods}>
          <form className="space-y-6">
            <CurrentStepComponent />
            
            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                type="button"
                onClick={handleNext}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : currentStep === steps.length ? 'Create Resume' : 'Next'}
              </button>
            </div>
          </form>
        </FormProvider>
      </main>
    </div>
  )
}
