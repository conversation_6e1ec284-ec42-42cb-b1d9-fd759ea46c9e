'use client'

export default function TestPuppeteerPage() {
  const testPuppeteerPDF = async () => {
    try {
      console.log('Testing Puppeteer PDF generation...')
      
      const testData = {
        personalInfo: {
          fullName: '<PERSON>',
          email: '<EMAIL>',
          phone: '************',
          location: 'New York, NY',
          summary: 'Test summary for PDF generation'
        },
        education: [],
        experience: [],
        skills: [],
        languages: []
      }

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeData: testData,
          template: 'modern'
        })
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', response.headers)

      if (response.ok) {
        console.log('PDF generation successful!')
        
        // Get the PDF blob
        const blob = await response.blob()
        console.log('PDF blob size:', blob.size)
        
        // Create download link
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'Test_Resume.pdf'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        alert('PDF downloaded successfully!')
      } else {
        const errorText = await response.text()
        console.error('PDF generation failed:', errorText)
        alert(`PDF generation failed: ${errorText}`)
      }
    } catch (error) {
      console.error('Error:', error)
      alert(`Error: ${error}`)
    }
  }

  const testPrintPage = () => {
    // Test the print page directly
    const printUrl = '/resume/print?template=modern'
    window.open(printUrl, '_blank')
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Puppeteer PDF Test</h1>
        <div className="space-y-4">
          <button
            onClick={testPrintPage}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            🖨️ Test Print Page
          </button>
          <button
            onClick={testPuppeteerPDF}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            📄 Test Puppeteer PDF
          </button>
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Test Steps:</strong><br/>
              1. First test the print page to see if it loads<br/>
              2. Then test Puppeteer PDF generation<br/>
              3. Check browser console for detailed logs
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
