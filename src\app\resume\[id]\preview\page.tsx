'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Navigation } from '@/components/navigation'
import { TemplateRenderer } from '@/components/template-renderer'
import { TemplateSelector } from '@/components/template-selector'
import { Resume, TemplateType } from '@/types/resume'
import { useSession } from 'next-auth/react'

export default function ResumePreviewPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [resume, setResume] = useState<Resume | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType>('modern')

  useEffect(() => {
    fetchResume()
  }, [params.id])

  const fetchResume = async () => {
    try {
      const response = await fetch(`/api/resumes/${params.id}`)
      if (response.ok) {
        const resumeData = await response.json()
        setResume(resumeData)
        setSelectedTemplate(resumeData.template as TemplateType)
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('Error fetching resume:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }

  const updateTemplate = async (template: TemplateType) => {
    if (!resume) return

    setSaving(true)
    try {
      const response = await fetch(`/api/resumes/${resume.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...resume,
          template
        })
      })

      if (response.ok) {
        setSelectedTemplate(template)
        setResume(prev => prev ? { ...prev, template } : null)
      }
    } catch (error) {
      console.error('Error updating template:', error)
    } finally {
      setSaving(false)
    }
  }

  const downloadPDF = async () => {
    try {
      const response = await fetch(`/api/resumes/${params.id}/download`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${resume?.title || 'resume'}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!resume) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900">Resume not found</h1>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{resume.title}</h1>
              <p className="text-gray-600">Preview and download your resume</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => router.push(`/resume/${resume.id}`)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Edit Resume
              </button>
              <button
                onClick={downloadPDF}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Download PDF
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
            {/* Template Selector */}
            <div className="xl:col-span-1 order-2 xl:order-1">
              <div className="bg-white shadow-lg rounded-xl p-6 sticky top-6">
                <TemplateSelector
                  selectedTemplate={selectedTemplate}
                  onTemplateChange={updateTemplate}
                  isPremium={session?.user?.isPremium}
                />
                {saving && (
                  <div className="mt-6 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-600 mt-2">Updating template...</p>
                  </div>
                )}
              </div>
            </div>

            {/* Resume Preview */}
            <div className="xl:col-span-4 order-1 xl:order-2">
              <div className="bg-white shadow-lg rounded-xl overflow-hidden">
                <TemplateRenderer
                  data={resume.data}
                  template={selectedTemplate}
                  className="shadow-none"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
