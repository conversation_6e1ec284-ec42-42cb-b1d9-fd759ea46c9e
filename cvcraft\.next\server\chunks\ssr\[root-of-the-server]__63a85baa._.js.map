{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\n\ninterface Resume {\n  id: string\n  title: string\n  fullName: string\n  template: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport default function DashboardPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [resumes, setResumes] = useState<Resume[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    if (status === 'loading') return\n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n    \n    fetchResumes()\n  }, [session, status, router])\n\n  const fetchResumes = async () => {\n    try {\n      const response = await fetch('/api/resume')\n      if (response.ok) {\n        const data = await response.json()\n        setResumes(data)\n      }\n    } catch (error) {\n      console.error('Error fetching resumes:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteResume = async (resumeId: string) => {\n    if (!confirm('Are you sure you want to delete this resume?')) return\n\n    try {\n      const response = await fetch(`/api/resume/${resumeId}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setResumes(resumes.filter(resume => resume.id !== resumeId))\n      } else {\n        alert('Failed to delete resume')\n      }\n    } catch (error) {\n      console.error('Error deleting resume:', error)\n      alert('Failed to delete resume')\n    }\n  }\n\n  const handleDownloadPDF = async (resumeId: string) => {\n    try {\n      const response = await fetch(`/api/resume/${resumeId}/pdf`)\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `resume-${resumeId}.pdf`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        alert('Failed to download PDF')\n      }\n    } catch (error) {\n      console.error('Error downloading PDF:', error)\n      alert('Failed to download PDF')\n    }\n  }\n\n  if (status === 'loading' || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-2xl font-bold text-indigo-600\">\n                CVCraft\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-gray-700\">Welcome, {session.user.name || session.user.email}</span>\n              <button\n                onClick={() => signOut({ callbackUrl: '/' })}\n                className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">My Resumes</h1>\n            <Link\n              href=\"/resume/create\"\n              className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              Create New Resume\n            </Link>\n          </div>\n\n          {resumes.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <svg\n                className=\"mx-auto h-12 w-12 text-gray-400\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                />\n              </svg>\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No resumes</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">Get started by creating your first resume.</p>\n              <div className=\"mt-6\">\n                <Link\n                  href=\"/resume/create\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                >\n                  Create Resume\n                </Link>\n              </div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n              {resumes.map((resume) => (\n                <div key={resume.id} className=\"bg-white overflow-hidden shadow rounded-lg\">\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                        {resume.title}\n                      </h3>\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800\">\n                        {resume.template}\n                      </span>\n                    </div>\n                    <p className=\"mt-1 text-sm text-gray-500\">{resume.fullName}</p>\n                    <p className=\"mt-1 text-xs text-gray-400\">\n                      Updated {new Date(resume.updatedAt).toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"bg-gray-50 px-6 py-3\">\n                    <div className=\"flex justify-between\">\n                      <div className=\"flex space-x-2\">\n                        <Link\n                          href={`/resume/edit/${resume.id}`}\n                          className=\"text-indigo-600 hover:text-indigo-900 text-sm font-medium\"\n                        >\n                          Edit\n                        </Link>\n                        <Link\n                          href={`/resume/preview/${resume.id}`}\n                          className=\"text-indigo-600 hover:text-indigo-900 text-sm font-medium\"\n                        >\n                          Preview\n                        </Link>\n                        <button\n                          onClick={() => handleDownloadPDF(resume.id)}\n                          className=\"text-indigo-600 hover:text-indigo-900 text-sm font-medium\"\n                        >\n                          Download\n                        </button>\n                      </div>\n                      <button\n                        onClick={() => handleDeleteResume(resume.id)}\n                        className=\"text-red-600 hover:text-red-900 text-sm font-medium\"\n                      >\n                        Delete\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAC1B,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,iDAAiD;QAE9D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE;gBACtD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YACpD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC;YAC1D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC;gBACrC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAqC;;;;;;;;;;;0CAIzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAgB;4CAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK;;;;;;;kDACjF,8OAAC;wCACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gDAAE,aAAa;4CAAI;wCAC1C,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;wBAKF,QAAQ,MAAM,KAAK,kBAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEP,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;8CAGN,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;iDAML,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,8OAAC;4DAAK,WAAU;sEACb,OAAO,QAAQ;;;;;;;;;;;;8DAGpB,8OAAC;oDAAE,WAAU;8DAA8B,OAAO,QAAQ;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;;wDAA6B;wDAC/B,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE;gEACjC,WAAU;0EACX;;;;;;0EAGD,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;gEACpC,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEACC,SAAS,IAAM,kBAAkB,OAAO,EAAE;gEAC1C,WAAU;0EACX;;;;;;;;;;;;kEAIH,8OAAC;wDACC,SAAS,IAAM,mBAAmB,OAAO,EAAE;wDAC3C,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAxCG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDnC", "debugId": null}}]}