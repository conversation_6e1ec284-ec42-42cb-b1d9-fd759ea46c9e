{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/route-modules/app-route/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-route/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-route.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-route.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/route-kind.ts"], "sourcesContent": ["export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n"], "names": ["RouteKind"], "mappings": ";;;AAAO,IAAWA,YAAAA,WAAAA,GAAAA,SAAAA,SAAAA;IAChB;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;IAED;;GAEC,GAAA,SAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,WAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;WAtBeA;MAwBjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/trace/constants.ts"], "sourcesContent": ["/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n"], "names": ["BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "AppRenderSpan", "RouterSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "MiddlewareSpan", "NextVanillaSpanAllowlist", "LogSpanAllowList"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B;;;;;;;;;;;;;;;;AAE5B,IAAKA,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;;;;;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAeL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;WAAAA;EAAAA,sBAAAA,CAAAA;AAKL,IAAKC,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAOL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BH,wDAAwD;;;;;WA5BrDA;EAAAA,sBAAAA,CAAAA;AAmCL,IAAKC,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;WAAAA;EAAAA,mBAAAA,CAAAA;AAIL,IAAKC,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;;;;;WAAAA;EAAAA,cAAAA,CAAAA;AAQL,IAAKC,gBAAAA,WAAAA,GAAAA,SAAAA,aAAAA;;;;;WAAAA;EAAAA,iBAAAA,CAAAA;AAOL,IAAKC,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;WAAAA;EAAAA,cAAAA,CAAAA;AAIL,IAAKC,WAAAA,WAAAA,GAAAA,SAAAA,QAAAA;;WAAAA;EAAAA,YAAAA,CAAAA;AAIL,IAAKC,4BAAAA,WAAAA,GAAAA,SAAAA,yBAAAA;;WAAAA;EAAAA,6BAAAA,CAAAA;AAIL,IAAKC,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;WAAAA;EAAAA,uBAAAA,CAAAA;AAKL,IAAKC,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;WAAAA;EAAAA,kBAAAA,CAAAA;AAmBE,MAAMC,2BAA2B;;;;;;;;;;;;;;;;;CAiBvC,CAAA;AAIM,MAAMC,mBAAmB;;;;CAI/B,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GACD;;;AAAO,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/trace/tracer.ts"], "sourcesContent": ["import type { FetchEventResult } from '../../web/types'\nimport type { TextMapSetter } from '@opentelemetry/api'\nimport type { SpanTypes } from './constants'\nimport { LogSpanAllowList, NextVanillaSpanAllowlist } from './constants'\n\nimport type {\n  ContextAPI,\n  Span,\n  SpanOptions,\n  Tracer,\n  AttributeValue,\n  TextMapGetter,\n} from 'next/dist/compiled/@opentelemetry/api'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\nlet api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n  api = require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n} else {\n  try {\n    api = require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n  } catch (err) {\n    api =\n      require('next/dist/compiled/@opentelemetry/api') as typeof import('next/dist/compiled/@opentelemetry/api')\n  }\n}\n\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } =\n  api\n\nexport class BubbledError extends Error {\n  constructor(\n    public readonly bubble?: boolean,\n    public readonly result?: FetchEventResult\n  ) {\n    super()\n  }\n}\n\nexport function isBubbledError(error: unknown): error is BubbledError {\n  if (typeof error !== 'object' || error === null) return false\n  return error instanceof BubbledError\n}\n\nconst closeSpanWithError = (span: Span, error?: Error) => {\n  if (isBubbledError(error) && error.bubble) {\n    span.setAttribute('next.bubble', true)\n  } else {\n    if (error) {\n      span.recordException(error)\n    }\n    span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message })\n  }\n  span.end()\n}\n\ntype TracerSpanOptions = Omit<SpanOptions, 'attributes'> & {\n  parentSpan?: Span\n  spanName?: string\n  attributes?: Partial<Record<AttributeNames, AttributeValue | undefined>>\n  hideSpan?: boolean\n}\n\ninterface NextTracer {\n  getContext(): ContextAPI\n\n  /**\n   * Instruments a function by automatically creating a span activated on its\n   * scope.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its second parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   *\n   */\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n\n  /**\n   * Wrap a function to automatically create a span activated on its\n   * scope when it's called.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its last parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   */\n  wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n\n  /**\n   * Starts and returns a new Span representing a logical unit of work.\n   *\n   * This method do NOT modify the current Context by default. In result, any inner span will not\n   * automatically set its parent context to the span created by this method unless manually activate\n   * context via `tracer.getContext().with`. `trace`, or `wrap` is generally recommended as it gracefully\n   * handles context activation. (ref: https://github.com/open-telemetry/opentelemetry-js/issues/1923)\n   */\n  startSpan(type: SpanTypes): Span\n  startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n\n  /**\n   * Returns currently activated span if current context is in the scope of the span.\n   * Returns undefined otherwise.\n   */\n  getActiveScopeSpan(): Span | undefined\n\n  /**\n   * Returns trace propagation data for the currently active context. The format is equal to data provided\n   * through the OpenTelemetry propagator API.\n   */\n  getTracePropagationData(): ClientTraceDataEntry[]\n}\n\ntype NextAttributeNames =\n  | 'next.route'\n  | 'next.page'\n  | 'next.rsc'\n  | 'next.segment'\n  | 'next.span_name'\n  | 'next.span_type'\n  | 'next.clientComponentLoadCount'\ntype OTELAttributeNames = `http.${string}` | `net.${string}`\ntype AttributeNames = NextAttributeNames | OTELAttributeNames\n\n/** we use this map to propagate attributes from nested spans to the top span */\nconst rootSpanAttributesStore = new Map<\n  number,\n  Map<AttributeNames, AttributeValue | undefined>\n>()\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId')\nlet lastSpanId = 0\nconst getSpanId = () => lastSpanId++\n\nexport interface ClientTraceDataEntry {\n  key: string\n  value: string\n}\n\nconst clientTraceDataSetter: TextMapSetter<ClientTraceDataEntry[]> = {\n  set(carrier, key, value) {\n    carrier.push({\n      key,\n      value,\n    })\n  },\n}\n\nclass NextTracerImpl implements NextTracer {\n  /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */\n  private getTracerInstance(): Tracer {\n    return trace.getTracer('next.js', '0.0.1')\n  }\n\n  public getContext(): ContextAPI {\n    return context\n  }\n\n  public getTracePropagationData(): ClientTraceDataEntry[] {\n    const activeContext = context.active()\n    const entries: ClientTraceDataEntry[] = []\n    propagation.inject(activeContext, entries, clientTraceDataSetter)\n    return entries\n  }\n\n  public getActiveScopeSpan(): Span | undefined {\n    return trace.getSpan(context?.active())\n  }\n\n  public withPropagatedContext<T, C>(\n    carrier: C,\n    fn: () => T,\n    getter?: TextMapGetter<C>\n  ): T {\n    const activeContext = context.active()\n    if (trace.getSpanContext(activeContext)) {\n      // Active span is already set, too late to propagate.\n      return fn()\n    }\n    const remoteContext = propagation.extract(activeContext, carrier, getter)\n    return context.with(remoteContext, fn)\n  }\n\n  // Trace, wrap implementation is inspired by datadog trace implementation\n  // (https://datadoghq.dev/dd-trace-js/interfaces/tracer.html#trace).\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(...args: Array<any>) {\n    const [type, fnOrOptions, fnOrEmpty] = args\n\n    // coerce options form overload\n    const {\n      fn,\n      options,\n    }: {\n      fn: (span?: Span, done?: (error?: Error) => any) => T | Promise<T>\n      options: TracerSpanOptions\n    } =\n      typeof fnOrOptions === 'function'\n        ? {\n            fn: fnOrOptions,\n            options: {},\n          }\n        : {\n            fn: fnOrEmpty,\n            options: { ...fnOrOptions },\n          }\n\n    const spanName = options.spanName ?? type\n\n    if (\n      (!NextVanillaSpanAllowlist.includes(type) &&\n        process.env.NEXT_OTEL_VERBOSE !== '1') ||\n      options.hideSpan\n    ) {\n      return fn()\n    }\n\n    // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n    let spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    let isRootSpan = false\n\n    if (!spanContext) {\n      spanContext = context?.active() ?? ROOT_CONTEXT\n      isRootSpan = true\n    } else if (trace.getSpanContext(spanContext)?.isRemote) {\n      isRootSpan = true\n    }\n\n    const spanId = getSpanId()\n\n    options.attributes = {\n      'next.span_name': spanName,\n      'next.span_type': type,\n      ...options.attributes,\n    }\n\n    return context.with(spanContext.setValue(rootSpanIdKey, spanId), () =>\n      this.getTracerInstance().startActiveSpan(\n        spanName,\n        options,\n        (span: Span) => {\n          const startTime =\n            'performance' in globalThis && 'measure' in performance\n              ? globalThis.performance.now()\n              : undefined\n\n          const onCleanup = () => {\n            rootSpanAttributesStore.delete(spanId)\n            if (\n              startTime &&\n              process.env.NEXT_OTEL_PERFORMANCE_PREFIX &&\n              LogSpanAllowList.includes(type || ('' as any))\n            ) {\n              performance.measure(\n                `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(\n                  type.split('.').pop() || ''\n                ).replace(\n                  /[A-Z]/g,\n                  (match: string) => '-' + match.toLowerCase()\n                )}`,\n                {\n                  start: startTime,\n                  end: performance.now(),\n                }\n              )\n            }\n          }\n\n          if (isRootSpan) {\n            rootSpanAttributesStore.set(\n              spanId,\n              new Map(\n                Object.entries(options.attributes ?? {}) as [\n                  AttributeNames,\n                  AttributeValue | undefined,\n                ][]\n              )\n            )\n          }\n          try {\n            if (fn.length > 1) {\n              return fn(span, (err) => closeSpanWithError(span, err))\n            }\n\n            const result = fn(span)\n            if (isThenable(result)) {\n              // If there's error make sure it throws\n              return result\n                .then((res) => {\n                  span.end()\n                  // Need to pass down the promise result,\n                  // it could be react stream response with error { error, stream }\n                  return res\n                })\n                .catch((err) => {\n                  closeSpanWithError(span, err)\n                  throw err\n                })\n                .finally(onCleanup)\n            } else {\n              span.end()\n              onCleanup()\n            }\n\n            return result\n          } catch (err: any) {\n            closeSpanWithError(span, err)\n            onCleanup()\n            throw err\n          }\n        }\n      )\n    )\n  }\n\n  public wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap(...args: Array<any>) {\n    const tracer = this\n    const [name, options, fn] =\n      args.length === 3 ? args : [args[0], {}, args[1]]\n\n    if (\n      !NextVanillaSpanAllowlist.includes(name) &&\n      process.env.NEXT_OTEL_VERBOSE !== '1'\n    ) {\n      return fn\n    }\n\n    return function (this: any) {\n      let optionsObj = options\n      if (typeof optionsObj === 'function' && typeof fn === 'function') {\n        optionsObj = optionsObj.apply(this, arguments)\n      }\n\n      const lastArgId = arguments.length - 1\n      const cb = arguments[lastArgId]\n\n      if (typeof cb === 'function') {\n        const scopeBoundCb = tracer.getContext().bind(context.active(), cb)\n        return tracer.trace(name, optionsObj, (_span, done) => {\n          arguments[lastArgId] = function (err: any) {\n            done?.(err)\n            return scopeBoundCb.apply(this, arguments)\n          }\n\n          return fn.apply(this, arguments)\n        })\n      } else {\n        return tracer.trace(name, optionsObj, () => fn.apply(this, arguments))\n      }\n    }\n  }\n\n  public startSpan(type: SpanTypes): Span\n  public startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n  public startSpan(...args: Array<any>): Span {\n    const [type, options]: [string, TracerSpanOptions | undefined] = args as any\n\n    const spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    return this.getTracerInstance().startSpan(type, options, spanContext)\n  }\n\n  private getSpanContext(parentSpan?: Span) {\n    const spanContext = parentSpan\n      ? trace.setSpan(context.active(), parentSpan)\n      : undefined\n\n    return spanContext\n  }\n\n  public getRootSpanAttributes() {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    return rootSpanAttributesStore.get(spanId)\n  }\n\n  public setRootSpanAttribute(key: AttributeNames, value: AttributeValue) {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    const attributes = rootSpanAttributesStore.get(spanId)\n    if (attributes) {\n      attributes.set(key, value)\n    }\n  }\n}\n\nconst getTracer = (() => {\n  const tracer = new NextTracerImpl()\n\n  return () => tracer\n})()\n\nexport { getTracer, SpanStatusCode, SpanKind }\nexport type { NextTracer, Span, SpanOptions, ContextAPI, TracerSpanOptions }\n"], "names": ["LogSpanAllowList", "NextVanillaSpanAllowlist", "isThenable", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "SpanStatusCode", "SpanKind", "ROOT_CONTEXT", "BubbledError", "Error", "constructor", "bubble", "result", "isBubbledError", "error", "closeSpanWithError", "span", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "clientTraceDataSetter", "set", "carrier", "key", "value", "push", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getTracePropagationData", "activeContext", "active", "entries", "inject", "getActiveScopeSpan", "getSpan", "withPropagatedContext", "fn", "getter", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "Object", "length", "then", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get", "setRootSpanAttribute"], "mappings": ";;;;;;;AAGA,SAASA,gBAAgB,EAAEC,wBAAwB,QAAQ,cAAa;AAUxE,SAASC,UAAU,QAAQ,kCAAiC;;;AAE5D,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAI;QACFH,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MACEI,QAAQ;IACZ;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAC3EX;AAEK,MAAMY,qBAAqBC;IAChCC,YACkBC,MAAgB,EAChBC,MAAyB,CACzC;QACA,KAAK,IAAA,IAAA,CAHWD,MAAAA,GAAAA,QAAAA,IAAAA,CACAC,MAAAA,GAAAA;IAGlB;AACF;AAEO,SAASC,eAAeC,KAAc;IAC3C,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM,OAAO;IACxD,OAAOA,iBAAiBN;AAC1B;AAEA,MAAMO,qBAAqB,CAACC,MAAYF;IACtC,IAAID,eAAeC,UAAUA,MAAMH,MAAM,EAAE;QACzCK,KAAKC,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIH,OAAO;YACTE,KAAKE,eAAe,CAACJ;QACvB;QACAE,KAAKG,SAAS,CAAC;YAAEC,MAAMf,eAAegB,KAAK;YAAEC,OAAO,EAAER,SAAAA,OAAAA,KAAAA,IAAAA,MAAOQ,OAAO;QAAC;IACvE;IACAN,KAAKO,GAAG;AACV;AA2GA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB9B,IAAI+B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAOxB,MAAME,wBAA+D;IACnEC,KAAIC,OAAO,EAAEC,GAAG,EAAEC,KAAK;QACrBF,QAAQG,IAAI,CAAC;YACXF;YACAC;QACF;IACF;AACF;AAEA,MAAME;IACJ;;;;GAIC,GACOC,oBAA4B;QAClC,OAAOjC,MAAMkC,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAOrC;IACT;IAEOsC,0BAAkD;QACvD,MAAMC,gBAAgBvC,QAAQwC,MAAM;QACpC,MAAMC,UAAkC,EAAE;QAC1CxC,YAAYyC,MAAM,CAACH,eAAeE,SAASb;QAC3C,OAAOa;IACT;IAEOE,qBAAuC;QAC5C,OAAOzC,MAAM0C,OAAO,CAAC5C,WAAAA,OAAAA,KAAAA,IAAAA,QAASwC,MAAM;IACtC;IAEOK,sBACLf,OAAU,EACVgB,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMR,gBAAgBvC,QAAQwC,MAAM;QACpC,IAAItC,MAAM8C,cAAc,CAACT,gBAAgB;YACvC,qDAAqD;YACrD,OAAOO;QACT;QACA,MAAMG,gBAAgBhD,YAAYiD,OAAO,CAACX,eAAeT,SAASiB;QAClE,OAAO/C,QAAQmD,IAAI,CAACF,eAAeH;IACrC;IAsBO5C,MAAS,GAAGkD,IAAgB,EAAE;YAwCxBlD;QAvCX,MAAM,CAACmD,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJN,EAAE,EACFU,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACER,IAAIQ;YACJE,SAAS,CAAC;QACZ,IACA;YACEV,IAAIS;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACG,8KAAC7D,2BAAAA,CAAyBkE,QAAQ,CAACL,SAClC1D,QAAQC,GAAG,CAAC+D,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOd;QACT;QAEA,mHAAmH;QACnH,IAAIe,cAAc,IAAI,CAACb,cAAc,CACnCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,UAAU,KAAI,IAAI,CAACnB,kBAAkB;QAEhD,IAAIoB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAc7D,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASwC,MAAM,EAAA,KAAMnC;YACnC0D,aAAa;QACf,OAAO,IAAA,CAAI7D,wBAAAA,MAAM8C,cAAc,CAACa,YAAAA,KAAAA,OAAAA,KAAAA,IAArB3D,sBAAmC8D,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAStC;QAEf6B,QAAQU,UAAU,GAAG;YACnB,kBAAkBT;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQU,UAAU;QACvB;QAEA,OAAOlE,QAAQmD,IAAI,CAACU,YAAYM,QAAQ,CAAC3C,eAAeyC,SAAS,IAC/D,IAAI,CAAC9B,iBAAiB,GAAGiC,eAAe,CACtCX,UACAD,SACA,CAAC1C;gBACC,MAAMuD,YACJ,iBAAiBC,cAAc,aAAaC,cACxCD,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChBpD,wBAAwBqD,MAAM,CAACV;oBAC/B,IACEI,aACA1E,QAAQC,GAAG,CAACgF,4BAA4B,IACxCrF,gMAAAA,CAAiBmE,QAAQ,CAACL,QAAS,KACnC;wBACAkB,YAAYM,OAAO,CACjB,GAAGlF,QAAQC,GAAG,CAACgF,4BAA4B,CAAC,MAAM,EAChDvB,CAAAA,KAAKyB,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,KACzC,EACH;4BACEC,OAAOd;4BACPhD,KAAKkD,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdzC,wBAAwBO,GAAG,CACzBoC,QACA,IAAI1C,IACF6D,OAAO3C,OAAO,CAACe,QAAQU,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIpB,GAAGuC,MAAM,GAAG,GAAG;wBACjB,OAAOvC,GAAGhC,MAAM,CAACf,MAAQc,mBAAmBC,MAAMf;oBACpD;oBAEA,MAAMW,SAASoC,GAAGhC;oBAClB,iLAAIrB,aAAAA,EAAWiB,SAAS;wBACtB,uCAAuC;wBACvC,OAAOA,OACJ4E,IAAI,CAAC,CAACC;4BACLzE,KAAKO,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAOkE;wBACT,GACCC,KAAK,CAAC,CAACzF;4BACNc,mBAAmBC,MAAMf;4BACzB,MAAMA;wBACR,GACC0F,OAAO,CAACf;oBACb,OAAO;wBACL5D,KAAKO,GAAG;wBACRqD;oBACF;oBAEA,OAAOhE;gBACT,EAAE,OAAOX,KAAU;oBACjBc,mBAAmBC,MAAMf;oBACzB2E;oBACA,MAAM3E;gBACR;YACF;IAGN;IAaO2F,KAAK,GAAGtC,IAAgB,EAAE;QAC/B,MAAMuC,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMpC,SAASV,GAAG,GACvBM,KAAKiC,MAAM,KAAK,IAAIjC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,8KAAC5D,2BAAAA,CAAyBkE,QAAQ,CAACkC,SACnCjG,QAAQC,GAAG,CAAC+D,iBAAiB,KAAK,KAClC;YACA,OAAOb;QACT;QAEA,OAAO;YACL,IAAI+C,aAAarC;YACjB,IAAI,OAAOqC,eAAe,cAAc,OAAO/C,OAAO,YAAY;gBAChE+C,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUV,MAAM,GAAG;YACrC,MAAMY,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOtD,UAAU,GAAG8D,IAAI,CAACnG,QAAQwC,MAAM,IAAIyD;gBAChE,OAAON,OAAOzF,KAAK,CAAC0F,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUjG,GAAQ;wBACvCsG,QAAAA,OAAAA,KAAAA,IAAAA,KAAOtG;wBACP,OAAOmG,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOjD,GAAGgD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOzF,KAAK,CAAC0F,MAAMC,YAAY,IAAM/C,GAAGgD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGlD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMS,cAAc,IAAI,CAACb,cAAc,CACrCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,UAAU,KAAI,IAAI,CAACnB,kBAAkB;QAEhD,OAAO,IAAI,CAACR,iBAAiB,GAAGmE,SAAS,CAACjD,MAAMG,SAASK;IAC3D;IAEQb,eAAec,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChB5D,MAAMqG,OAAO,CAACvG,QAAQwC,MAAM,IAAIsB,cAChCW;QAEJ,OAAOZ;IACT;IAEO2C,wBAAwB;QAC7B,MAAMvC,SAASjE,QAAQwC,MAAM,GAAGiE,QAAQ,CAACjF;QACzC,OAAOF,wBAAwBoF,GAAG,CAACzC;IACrC;IAEO0C,qBAAqB5E,GAAmB,EAAEC,KAAqB,EAAE;QACtE,MAAMiC,SAASjE,QAAQwC,MAAM,GAAGiE,QAAQ,CAACjF;QACzC,MAAM0C,aAAa5C,wBAAwBoF,GAAG,CAACzC;QAC/C,IAAIC,YAAY;YACdA,WAAWrC,GAAG,CAACE,KAAKC;QACtB;IACF;AACF;AAEA,MAAMI,YAAa,CAAA;IACjB,MAAMuD,SAAS,IAAIzD;IAEnB,OAAO,IAAMyD;AACf,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS =\n  'private-next-rsc-track-dynamic-import'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "ACTION_SUFFIX", "NEXT_DATA_SUFFIX", "NEXT_META_SUFFIX", "NEXT_BODY_SUFFIX", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_RESUME_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_IMPLICIT_TAG_ID", "CACHE_ONE_YEAR", "INFINITE_CACHE", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "INSTRUMENTATION_HOOK_FILENAME", "PAGES_DIR_ALIAS", "DOT_NEXT_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "SERVER_PROPS_EXPORT_ERROR", "GSP_NO_RETURNED_VALUE", "GSSP_NO_RETURNED_VALUE", "UNSTABLE_REVALIDATE_RENAME_ERROR", "GSSP_COMPONENT_MEMBER_ERROR", "NON_STANDARD_NODE_ENV", "SSG_FALLBACK_EXPORT_ERROR", "ESLINT_DEFAULT_DIRS", "SERVER_RUNTIME", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "WEBPACK_LAYERS", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAMA,0BAA0B,OAAM;AACtC,MAAMC,kCAAkC,OAAM;AAE9C,MAAMC,sBAAsB,iBAAgB;AAC5C,MAAMC,8BAA8B,yBAAwB;AAC5D,MAAMC,6CACX,sCAAqC;AAEhC,MAAMC,sBAAsB,gBAAe;AAC3C,MAAMC,0BAA0B,YAAW;AAC3C,MAAMC,qBAAqB,eAAc;AACzC,MAAMC,aAAa,OAAM;AACzB,MAAMC,gBAAgB,UAAS;AAC/B,MAAMC,mBAAmB,QAAO;AAChC,MAAMC,mBAAmB,QAAO;AAChC,MAAMC,mBAAmB,QAAO;AAEhC,MAAMC,yBAAyB,oBAAmB;AAClD,MAAMC,qCAAqC,0BAAyB;AACpE,MAAMC,yCACX,8BAA6B;AAExB,MAAMC,qBAAqB,cAAa;AAIxC,MAAMC,2BAA2B,IAAG;AACpC,MAAMC,4BAA4B,IAAG;AACrC,MAAMC,iCAAiC,KAAI;AAC3C,MAAMC,6BAA6B,QAAO;AAG1C,MAAMC,iBAAiB,SAAQ;AAK/B,MAAMC,iBAAiB,WAAU;AAGjC,MAAMC,sBAAsB,aAAY;AACxC,MAAMC,6BAA6B,CAAC,SAAS,EAAED,qBAAqB,CAAA;AAGpE,MAAME,gCAAgC,kBAAiB;AAIvD,MAAMC,kBAAkB,qBAAoB;AAC5C,MAAMC,iBAAiB,mBAAkB;AACzC,MAAMC,iBAAiB,wBAAuB;AAC9C,MAAMC,gBAAgB,uBAAsB;AAC5C,MAAMC,0BAA0B,iCAAgC;AAChE,MAAMC,4BAA4B,mCAAkC;AACpE,MAAMC,yBAAyB,oCAAmC;AAClE,MAAMC,0BAA0B,iCAAgC;AAChE,MAAMC,mCACX,wCAAuC;AAClC,MAAMC,8BAA8B,qCAAoC;AACxE,MAAMC,kCACX,yCAAwC;AAEnC,MAAMC,iCAAiC,CAAC,6KAA6K,CAAC,CAAA;AAEtN,MAAMC,iCAAiC,CAAC,mGAAmG,CAAC,CAAA;AAE5I,MAAMC,uCAAuC,CAAC,uFAAuF,CAAC,CAAA;AAEtI,MAAMC,4BAA4B,CAAC,sHAAsH,CAAC,CAAA;AAE1J,MAAMC,6CAA6C,CAAC,uGAAuG,CAAC,CAAA;AAE5J,MAAMC,4BAA4B,CAAC,uHAAuH,CAAC,CAAA;AAE3J,MAAMC,wBACX,6FAA4F;AACvF,MAAMC,yBACX,iGAAgG;AAE3F,MAAMC,mCACX,uEACA,mCAAkC;AAE7B,MAAMC,8BAA8B,CAAC,wJAAwJ,CAAC,CAAA;AAE9L,MAAMC,wBAAwB,CAAC,iNAAiN,CAAC,CAAA;AAEjP,MAAMC,4BAA4B,CAAC,wJAAwJ,CAAC,CAAA;AAE5L,MAAMC,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM,CAAA;AAExE,MAAMC,iBAAgD;IAC3DC,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV,EAAC;AAED;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,cAAc;IACd;;GAEC,GACDC,cAAc;AAChB;AAKA,MAAMC,iBAAiB;IACrB,GAAGd,oBAAoB;IACvBe,OAAO;QACLC,cAAc;YACZhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDa,YAAY;YACVjB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDW,eAAe;YACb,YAAY;YACZlB,qBAAqBK,OAAO;YAC5BL,qBAAqBM,OAAO;SAC7B;QACDa,YAAY;YACVnB,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;SACrC;QACDU,SAAS;YACPpB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBC,MAAM;YAC3BD,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDc,UAAU;YACR,+BAA+B;YAC/BrB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMkB,2BAA2B;IAC/BC,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "makeHangingPromise", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;AAAO,SAASA,+BACdC,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAASC,mBACdC,MAAmB,EACnBJ,UAAkB;IAElB,IAAII,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIV,6BAA6BG;IACzD,OAAO;QACL,MAAMQ,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAId,6BAA6BG;YAEnC,IAAIY,mBAAmBX,uBAAuBY,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCT,uBAAuBe,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME"], "mappings": ";;;;;AAAO,MAAMA,yBAAyB,6BAA4B;AAC3D,MAAMC,yBAAyB,6BAA4B;AAC3D,MAAMC,uBAAuB,2BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["scheduleOnNextTick", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r"], "mappings": "AAGA;;;;;CAKC,GACD;;;;;;AAAO,MAAMA,qBAAqB,CAACC;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF,EAAC;AAQM,MAAMS,oBAAoB,CAACT;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLI,aAAaV;IACf;AACF,EAAC;AAOM,SAASW;IACd,OAAO,IAAIV,QAAc,CAACC,UAAYO,kBAAkBP;AAC1D;AAWO,SAASU;IACd,IAAIR,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACY,IAAMH,aAAaG;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    // TODO: it makes no sense to have these work unit store types during a dev render.\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-client' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender-client') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasSuspenseAfterBodyOrHtmlRegex =\n  /\\n\\s+at (?:body|html) \\(<anonymous>\\)[\\s\\S]*?\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseAfterBodyOrHtmlRegex.test(componentStack)) {\n    // This prerender has a Suspense boundary above the body which\n    // effectively opts the page into allowing 100% dynamic rendering\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nfunction logDisallowedDynamicError(workStore: WorkStore, error: Error): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (workStore.invalidDynamicUsageError) {\n    logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n    throw new StaticGenBailoutError()\n  }\n\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n"], "names": ["React", "DynamicServerError", "StaticGenBailoutError", "workUnitAsyncStorage", "workAsyncStorage", "makeHangingPromise", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "scheduleOnNextTick", "hasPostpone", "unstable_postpone", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "createDynamicValidationState", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "getFirstDynamicReason", "trackingState", "expression", "markCurrentScopeAsDynamic", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "trackFallbackParamAccessed", "prerenderStore", "getStore", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "abortOnSynchronousPlatformIOAccess", "errorWithStack", "trackSynchronousPlatformIOAccessInDev", "requestStore", "prerenderPhase", "abortAndThrowOnSynchronousRequestDataAccess", "prerenderSignal", "signal", "aborted", "trackSynchronousRequestDataAccessInDev", "Postpone", "assertPostpone", "createPostponeReason", "isDynamicPostpone", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "isPrerenderInterruptedError", "accessedDynamicData", "length", "consumeDynamicAccess", "serverDynamic", "clientDynamic", "formatDynamicAPIAccesses", "filter", "access", "map", "split", "slice", "line", "join", "createPostponedAbortSignal", "AbortController", "x", "createHangingInputAbortSignal", "cacheSignal", "inputReady", "then", "annotateDynamicAccess", "useDynamicRouteParams", "workStore", "isStaticGeneration", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "hasSuspenseAfterBodyOrHtmlRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "trackAllowedDynamicAccess", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "PreludeState", "logDisallowedDynamicError", "console", "dev", "hasReadableErrorStacks", "throwIfDisallowedDynamic", "prelude", "invalidDynamicUsageError", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAUD,wFAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACxF,OAAOA,WAAW,QAAO;AAEzB,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,QACf,wCAAuC;AAC9C,SAASC,kBAAkB,QAAQ,sBAAqB;;;;;;;;;AAExD,MAAMC,cAAc,OAAOV,kNAAAA,CAAMW,iBAAiB,KAAK;AAwChD,SAASC,2BACdC,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAASC;IACd,OAAO;QACLC,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASC,sBACdC,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASC,0BACdC,KAAgB,EAChBC,aAAuE,EACvEH,UAAkB;IAElB,IAAIG,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,qMAAI7B,wBAAAA,CACR,CAAC,MAAM,EAAEwB,MAAMM,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIG,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1CK,qBACEP,MAAMM,KAAK,EACXR,YACAG,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,gMAAInC,qBAAAA,CACd,CAAC,MAAM,EAAEyB,MAAMM,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAE,MAAMW,uBAAuB,GAAGb;YAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;YAEnC,MAAMH;QACR,OAAO,IACLI,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBf,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAcgB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASC,2BACdlB,KAAgB,EAChBF,UAAkB;IAElB,MAAMqB,qSAAiB1C,uBAAAA,CAAqB2C,QAAQ;IACpD,IAAI,CAACD,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhEK,qBAAqBP,MAAMM,KAAK,EAAER,YAAYqB,eAAeX,eAAe;AAC9E;AAQO,SAASa,iCACdvB,UAAkB,EAClBE,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,gMAAInC,qBAAAA,CACd,CAAC,MAAM,EAAEyB,MAAMM,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAqB,eAAeV,UAAU,GAAG;IAE5BT,MAAMW,uBAAuB,GAAGb;IAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;IAEnC,MAAMH;AACR;AASO,SAASY,gCACdC,MAAiB,EACjBtB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,mFAAmF;QACnF,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,sBACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBf,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAcgB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,SAASO,oCACPlB,KAAa,EACbR,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMM,SAAS,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM4B,QAAQC,gCAAgCF;IAE9CN,eAAeS,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMlB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAAC0C,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfjB,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAI4C,QAAQlB,KAAK,GACjBmB;YACJlC;QACF;IACF;AACF;AAEO,SAASmC,mCACd3B,KAAa,EACbR,UAAkB,EAClBoC,cAAqB,EACrBf,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDgB,oCAAoClB,OAAOR,YAAYqB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;YACtDmB,gBAAgBnB,yBAAyB,GAAG6C;QAC9C;IACF;AACF;AAEO,SAASC,sCACdC,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASC,4CACdhC,KAAa,EACbR,UAAkB,EAClBoC,cAAqB,EACrBf,cAAoC;IAEpC,MAAMoB,kBAAkBpB,eAAeS,UAAU,CAACY,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1BjB,oCAAoClB,OAAOR,YAAYqB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;gBACtDmB,gBAAgBnB,yBAAyB,GAAG6C;YAC9C;QACF;IACF;IACA,MAAMP,gCACJ,CAAC,MAAM,EAAErB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAM4C,yCACXP,sCAAqC;AAShC,SAASQ,SAAS,EAAElB,MAAM,EAAEnB,KAAK,EAAiB;IACvD,MAAMa,qSAAiB1C,uBAAAA,CAAqB2C,QAAQ;IACpD,MAAMZ,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACND,qBAAqBD,OAAOmB,QAAQjB;AACtC;AAEO,SAASD,qBACdD,KAAa,EACbR,UAAkB,EAClBU,eAA4C;IAE5CoC;IACA,IAAIpC,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAAC0C,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfjB,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAI4C,QAAQlB,KAAK,GACjBmB;YACJlC;QACF;IACF;4MAEAxB,UAAAA,CAAMW,iBAAiB,CAAC4D,qBAAqBvC,OAAOR;AACtD;AAEA,SAAS+C,qBAAqBvC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASgD,kBAAkBpC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAYqC,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBtC,IAAYqC,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBvB,MAAc;IAC7C,OACEA,OAAOwB,QAAQ,CACb,sEAEFxB,OAAOwB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBH,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAId,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMmB,6BAA6B;AAEnC,SAASvB,gCAAgCoB,OAAe;IACtD,MAAMrB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMgB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BrB,MAAcyB,MAAM,GAAGD;IACzB,OAAOxB;AACT;AAMO,SAAS0B,4BACd1B,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcyB,MAAM,KAAKD,8BAC1B,UAAUxB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASsB,oBACdjE,eAAqC;IAErC,OAAOA,gBAAgBkE,MAAM,GAAG;AAClC;AAEO,SAASC,qBACdC,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcpE,eAAe,CAAC0C,IAAI,IAAI2B,cAAcrE,eAAe;IACnE,OAAOoE,cAAcpE,eAAe;AACtC;AAEO,SAASsE,yBACdtE,eAAqC;IAErC,OAAOA,gBACJuE,MAAM,CACL,CAACC,SACC,OAAOA,OAAO/C,KAAK,KAAK,YAAY+C,OAAO/C,KAAK,CAACyC,MAAM,GAAG,GAE7DO,GAAG,CAAC,CAAC,EAAE/D,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLiD,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKf,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIe,KAAKf,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIe,KAAKf,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCgB,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEnE,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAAS+B;IACP,IAAI,CAAC5D,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI+C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASmC,2BAA2BzC,MAAc;IACvDmB;IACA,MAAMhB,aAAa,IAAIuC;IACvB,qFAAqF;IACrF,IAAI;gNACF7F,UAAAA,CAAMW,iBAAiB,CAACwC;IAC1B,EAAE,OAAO2C,GAAY;QACnBxC,WAAWC,KAAK,CAACuC;IACnB;IACA,OAAOxC,WAAWY,MAAM;AAC1B;AAOO,SAAS6B,8BACdpE,aAAmC;IAEnC,MAAM2B,aAAa,IAAIuC;IAEvB,IAAIlE,cAAcqE,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCrE,cAAcqE,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1C5C,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1D9C,mLAAAA,EAAmB,IAAM6C,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWY,MAAM;AAC1B;AAEO,SAASiC,sBACd3E,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAAC0C,IAAI,CAAC;YACnCjB,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAI4C,QAAQlB,KAAK,GACjBmB;YACJlC;QACF;IACF;AACF;AAEO,SAAS4E,sBAAsB5E,UAAkB;IACtD,MAAM6E,YAAYjG,uRAAAA,CAAiB0C,QAAQ;IAE3C,IACEuD,aACAA,UAAUC,kBAAkB,IAC5BD,UAAUE,mBAAmB,IAC7BF,UAAUE,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAM7E,oSAAgBxB,uBAAAA,CAAqB2C,QAAQ;QACnD,IAAInB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,oBAAoB;gBAC7C,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;wNACvD5B,UAAAA,CAAMyG,GAAG,KAACpG,sMAAAA,EAAmBsB,cAAc+E,YAAY,EAAElF;YAC3D,OAAO,IAAIG,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9BK,qBACEoE,UAAUrE,KAAK,EACfR,YACAG,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDmB,iCAAiCvB,YAAY6E,WAAW1E;YAC1D;QACF;IACF;AACF;AAEA,MAAMgF,mBAAmB;AACzB,MAAMC,kCACJ;AACF,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,oLAAExG,yBAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAMyG,mBAAmB,IAAID,OAC3B,CAAC,UAAU,oLAAEvG,yBAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAMyG,iBAAiB,IAAIF,OAAO,CAAC,UAAU,oLAAEtG,uBAAAA,CAAqB,QAAQ,CAAC;AAEtE,SAASyG,0BACdZ,SAAoB,EACpBa,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC;IAEnC,IAAI6B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIL,iBAAiBO,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI6F,iBAAiBK,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBhG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIyF,gCAAgCQ,IAAI,CAACF,iBAAiB;QAC/D,8DAA8D;QAC9D,iEAAiE;QACjEC,kBAAkB/F,iBAAiB,GAAG;QACtC+F,kBAAkBlG,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI0F,iBAAiBS,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkB/F,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAI+D,cAAcpE,yBAAyB,EAAE;QAClD,qDAAqD;QACrDoG,kBAAkB9F,aAAa,CAACmC,IAAI,CAClC2B,cAAcpE,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAM0D,UAAU,CAAC,OAAO,EAAE4B,UAAUrE,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMoB,QAAQiE,qCAAqC5C,SAASyC;QAC5DC,kBAAkB9F,aAAa,CAACmC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASiE,qCACP5C,OAAe,EACfyC,cAAsB;IAEtB,MAAMI,aACJ9E,QAAQC,GAAG,CAACC,QAAQ,gCAAK,wNAAgB1C,UAAAA,CAAMuH,iBAAiB,2MAC5DvH,UAAAA,CAAMuH,iBAAiB,KACvB;IAEN,MAAMnE,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMgB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BrB,MAAMb,KAAK,GAAGa,MAAMoE,IAAI,GAAG,OAAO/C,UAAW6C,CAAAA,cAAcJ,cAAa;IACxE,OAAO9D;AACT;AAEO,IAAKqE,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX;AAED,SAASC,0BAA0BrB,SAAoB,EAAEjD,KAAY;IACnEuE,QAAQvE,KAAK,CAACA;IAEd,IAAI,CAACiD,UAAUuB,GAAG,EAAE;QAClB,IAAIvB,UAAUwB,sBAAsB,EAAE;YACpCF,QAAQvE,KAAK,CACX,CAAC,iIAAiI,EAAEiD,UAAUrE,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL2F,QAAQvE,KAAK,CAAC,CAAC;0EACqD,EAAEiD,UAAUrE,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS8F,yBACdzB,SAAoB,EACpB0B,OAAqB,EACrBZ,iBAAyC,EACzCjC,aAAmC;IAEnC,IAAImB,UAAU2B,wBAAwB,EAAE;QACtCN,0BAA0BrB,WAAWA,UAAU2B,wBAAwB;QACvE,MAAM,IAAI9H,yNAAAA;IACZ;IAEA,IAAI6H,YAAAA,GAA+B;QACjC,IAAIZ,kBAAkBlG,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIiE,cAAcnE,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChE2G,0BACErB,WACAnB,cAAcnE,yBAAyB;YAEzC,MAAM,qMAAIb,wBAAAA;QACZ;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMmB,gBAAgB8F,kBAAkB9F,aAAa;QACrD,IAAIA,cAAc2D,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAIiD,IAAI,GAAGA,IAAI5G,cAAc2D,MAAM,EAAEiD,IAAK;gBAC7CP,0BAA0BrB,WAAWhF,aAAa,CAAC4G,EAAE;YACvD;YAEA,MAAM,oMAAI/H,yBAAAA;QACZ;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAIiH,kBAAkBhG,kBAAkB,EAAE;YACxCwG,QAAQvE,KAAK,CACX,CAAC,OAAO,EAAEiD,UAAUrE,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,qMAAI9B,wBAAAA;QACZ;QAEA,IAAI6H,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CJ,QAAQvE,KAAK,CACX,CAAC,OAAO,EAAEiD,UAAUrE,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,qMAAI9B,wBAAAA;QACZ;IACF,OAAO;QACL,IACEiH,kBAAkB/F,iBAAiB,KAAK,SACxC+F,kBAAkBjG,kBAAkB,EACpC;YACAyG,QAAQvE,KAAK,CACX,CAAC,OAAO,EAAEiD,UAAUrE,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,qMAAI9B,wBAAAA;QACZ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/clone-response.ts"], "sourcesContent": ["/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n    // How the original response.url behaves\n    configurable: true,\n    enumerable: true,\n    writable: false,\n  })\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n    // How the original response.url behaves\n    configurable: true,\n    enumerable: true,\n    writable: false,\n  })\n\n  return [cloned1, cloned2]\n}\n"], "names": ["cloneResponse", "original", "body", "body1", "body2", "tee", "cloned1", "Response", "status", "statusText", "headers", "Object", "defineProperty", "value", "url", "configurable", "enumerable", "writable", "cloned2"], "mappings": "AAAA;;;;;;;;;;;CAWC,GACD;;;AAAO,SAASA,cAAcC,QAAkB;IAC9C,6EAA6E;IAC7E,gCAAgC;IAChC,IAAI,CAACA,SAASC,IAAI,EAAE;QAClB,OAAO;YAACD;YAAUA;SAAS;IAC7B;IAEA,MAAM,CAACE,OAAOC,MAAM,GAAGH,SAASC,IAAI,CAACG,GAAG;IAExC,MAAMC,UAAU,IAAIC,SAASJ,OAAO;QAClCK,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACN,SAAS,OAAO;QACpCO,OAAOZ,SAASa,GAAG;QACnB,wCAAwC;QACxCC,cAAc;QACdC,YAAY;QACZC,UAAU;IACZ;IAEA,MAAMC,UAAU,IAAIX,SAASH,OAAO;QAClCI,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACM,SAAS,OAAO;QACpCL,OAAOZ,SAASa,GAAG;QACnB,wCAAwC;QACxCC,cAAc;QACdC,YAAY;QACZC,UAAU;IACZ;IAEA,OAAO;QAACX;QAASY;KAAQ;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;AAAO,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/dedupe-fetch.ts"], "sourcesContent": ["/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */\nimport * as React from 'react'\nimport { cloneResponse } from './clone-response'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n\nfunction generateCacheKey(request: Request): string {\n  // We pick the fields that goes into the key used to dedupe requests.\n  // We don't include the `cache` field, because we end up using whatever\n  // caching resulted from the first request.\n  // Notably we currently don't consider non-standard (or future) options.\n  // This might not be safe. TODO: warn for non-standard extensions differing.\n  // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n  return JSON.stringify([\n    request.method,\n    Array.from(request.headers.entries()),\n    request.mode,\n    request.redirect,\n    request.credentials,\n    request.referrer,\n    request.referrerPolicy,\n    request.integrity,\n  ])\n}\n\ntype CacheEntry = [\n  key: string,\n  promise: Promise<Response>,\n  response: Response | null,\n]\n\nexport function createDedupeFetch(originalFetch: typeof fetch) {\n  const getCacheEntries = React.cache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url: string): CacheEntry[] => []\n  )\n\n  return function dedupeFetch(\n    resource: URL | RequestInfo,\n    options?: RequestInit\n  ): Promise<Response> {\n    if (options && options.signal) {\n      // If we're passed a signal, then we assume that\n      // someone else controls the lifetime of this object and opts out of\n      // caching. It's effectively the opt-out mechanism.\n      // Ideally we should be able to check this on the Request but\n      // it always gets initialized with its own signal so we don't\n      // know if it's supposed to override - unless we also override the\n      // Request constructor.\n      return originalFetch(resource, options)\n    }\n    // Normalize the Request\n    let url: string\n    let cacheKey: string\n    if (typeof resource === 'string' && !options) {\n      // Fast path.\n      cacheKey = simpleCacheKey\n      url = resource\n    } else {\n      // Normalize the request.\n      // if resource is not a string or a URL (its an instance of Request)\n      // then do not instantiate a new Request but instead\n      // reuse the request as to not disturb the body in the event it's a ReadableStream.\n      const request =\n        typeof resource === 'string' || resource instanceof URL\n          ? new Request(resource, options)\n          : resource\n      if (\n        (request.method !== 'GET' && request.method !== 'HEAD') ||\n        request.keepalive\n      ) {\n        // We currently don't dedupe requests that might have side-effects. Those\n        // have to be explicitly cached. We assume that the request doesn't have a\n        // body if it's GET or HEAD.\n        // keepalive gets treated the same as if you passed a custom cache signal.\n        return originalFetch(resource, options)\n      }\n      cacheKey = generateCacheKey(request)\n      url = request.url\n    }\n\n    const cacheEntries = getCacheEntries(url)\n    for (let i = 0, j = cacheEntries.length; i < j; i += 1) {\n      const [key, promise] = cacheEntries[i]\n      if (key === cacheKey) {\n        return promise.then(() => {\n          const response = cacheEntries[i][2]\n          if (!response) throw new InvariantError('No cached response')\n\n          // We're cloning the response using this utility because there exists\n          // a bug in the undici library around response cloning. See the\n          // following pull request for more details:\n          // https://github.com/vercel/next.js/pull/73274\n          const [cloned1, cloned2] = cloneResponse(response)\n          cacheEntries[i][2] = cloned2\n          return cloned1\n        })\n      }\n    }\n\n    // We pass the original arguments here in case normalizing the Request\n    // doesn't include all the options in this environment.\n    const promise = originalFetch(resource, options)\n    const entry: CacheEntry = [cacheKey, promise, null]\n    cacheEntries.push(entry)\n\n    return promise.then((response) => {\n      // We're cloning the response using this utility because there exists\n      // a bug in the undici library around response cloning. See the\n      // following pull request for more details:\n      // https://github.com/vercel/next.js/pull/73274\n      const [cloned1, cloned2] = cloneResponse(response)\n      entry[2] = cloned2\n      return cloned1\n    })\n  }\n}\n"], "names": ["React", "cloneResponse", "InvariantError", "simple<PERSON><PERSON><PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "request", "JSON", "stringify", "method", "Array", "from", "headers", "entries", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "createDedupeFetch", "originalFetch", "getCacheEntries", "cache", "url", "dedupe<PERSON><PERSON>ch", "resource", "options", "signal", "cache<PERSON>ey", "URL", "Request", "keepalive", "cacheEntries", "i", "j", "length", "key", "promise", "then", "response", "cloned1", "cloned2", "entry", "push"], "mappings": "AAAA;;CAEC;;;AACD,YAAYA,WAAW,QAAO;AAC9B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,cAAc,QAAQ,mCAAkC;;;;AAEjE,MAAMC,iBAAiB,+CAA+C,kDAAkD;;AAExH,SAASC,iBAAiBC,OAAgB;IACxC,qEAAqE;IACrE,uEAAuE;IACvE,2CAA2C;IAC3C,wEAAwE;IACxE,4EAA4E;IAC5E,sDAAsD;IACtD,OAAOC,KAAKC,SAAS,CAAC;QACpBF,QAAQG,MAAM;QACdC,MAAMC,IAAI,CAACL,QAAQM,OAAO,CAACC,OAAO;QAClCP,QAAQQ,IAAI;QACZR,QAAQS,QAAQ;QAChBT,QAAQU,WAAW;QACnBV,QAAQW,QAAQ;QAChBX,QAAQY,cAAc;QACtBZ,QAAQa,SAAS;KAClB;AACH;AAQO,SAASC,kBAAkBC,aAA2B;IAC3D,MAAMC,0NAAkBrB,MAAMsB,EAAK,CACjC,AACA,CAACC,MAA8B,EAAE,4EADoD;IAIvF,OAAO,SAASC,YACdC,QAA2B,EAC3BC,OAAqB;QAErB,IAAIA,WAAWA,QAAQC,MAAM,EAAE;YAC7B,gDAAgD;YAChD,oEAAoE;YACpE,mDAAmD;YACnD,6DAA6D;YAC7D,6DAA6D;YAC7D,kEAAkE;YAClE,uBAAuB;YACvB,OAAOP,cAAcK,UAAUC;QACjC;QACA,wBAAwB;QACxB,IAAIH;QACJ,IAAIK;QACJ,IAAI,OAAOH,aAAa,YAAY,CAACC,SAAS;YAC5C,aAAa;YACbE,WAAWzB;YACXoB,MAAME;QACR,OAAO;YACL,yBAAyB;YACzB,oEAAoE;YACpE,oDAAoD;YACpD,mFAAmF;YACnF,MAAMpB,UACJ,OAAOoB,aAAa,YAAYA,oBAAoBI,MAChD,IAAIC,QAAQL,UAAUC,WACtBD;YACN,IACGpB,QAAQG,MAAM,KAAK,SAASH,QAAQG,MAAM,KAAK,UAChDH,QAAQ0B,SAAS,EACjB;gBACA,yEAAyE;gBACzE,0EAA0E;gBAC1E,4BAA4B;gBAC5B,0EAA0E;gBAC1E,OAAOX,cAAcK,UAAUC;YACjC;YACAE,WAAWxB,iBAAiBC;YAC5BkB,MAAMlB,QAAQkB,GAAG;QACnB;QAEA,MAAMS,eAAeX,gBAAgBE;QACrC,IAAK,IAAIU,IAAI,GAAGC,IAAIF,aAAaG,MAAM,EAAEF,IAAIC,GAAGD,KAAK,EAAG;YACtD,MAAM,CAACG,KAAKC,QAAQ,GAAGL,YAAY,CAACC,EAAE;YACtC,IAAIG,QAAQR,UAAU;gBACpB,OAAOS,QAAQC,IAAI,CAAC;oBAClB,MAAMC,WAAWP,YAAY,CAACC,EAAE,CAAC,EAAE;oBACnC,IAAI,CAACM,UAAU,MAAM,OAAA,cAAwC,CAAxC,gLAAIrC,kBAAAA,CAAe,uBAAnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAE5D,qEAAqE;oBACrE,+DAA+D;oBAC/D,2CAA2C;oBAC3C,+CAA+C;oBAC/C,MAAM,CAACsC,SAASC,QAAQ,mLAAGxC,gBAAAA,EAAcsC;oBACzCP,YAAY,CAACC,EAAE,CAAC,EAAE,GAAGQ;oBACrB,OAAOD;gBACT;YACF;QACF;QAEA,sEAAsE;QACtE,uDAAuD;QACvD,MAAMH,UAAUjB,cAAcK,UAAUC;QACxC,MAAMgB,QAAoB;YAACd;YAAUS;YAAS;SAAK;QACnDL,aAAaW,IAAI,CAACD;QAElB,OAAOL,QAAQC,IAAI,CAAC,CAACC;YACnB,qEAAqE;YACrE,+DAA+D;YAC/D,2CAA2C;YAC3C,+CAA+C;YAC/C,MAAM,CAACC,SAASC,QAAQ,mLAAGxC,gBAAAA,EAAcsC;YACzCG,KAAK,CAAC,EAAE,GAAGD;YACX,OAAOD;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/lib/detached-promise.ts"], "sourcesContent": ["/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n"], "names": ["Detached<PERSON>romise", "constructor", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": "AAAA;;;;;CAKC,GACD;;;AAAO,MAAMA;IAKXC,aAAc;QACZ,IAAIC;QACJ,IAAIC;QAEJ,6DAA6D;QAC7D,IAAI,CAACC,OAAO,GAAG,IAAIC,QAAW,CAACC,KAAKC;YAClCL,UAAUI;YACVH,SAASI;QACX;QAEA,yEAAyE;QACzE,iBAAiB;QACjB,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/lib/batcher.ts"], "sourcesContent": ["import type { SchedulerFn } from './scheduler'\n\nimport { DetachedPromise } from './detached-promise'\n\ntype CacheKeyFn<K, C extends string | number | null> = (\n  key: K\n) => PromiseLike<C> | C\n\ntype BatcherOptions<K, C extends string | number | null> = {\n  cacheKeyFn?: CacheKeyFn<K, C>\n  schedulerFn?: SchedulerFn<void>\n}\n\ntype WorkFn<V, C> = (\n  key: C,\n  resolve: (value: V | PromiseLike<V>) => void\n) => Promise<V>\n\n/**\n * A wrapper for a function that will only allow one call to the function to\n * execute at a time.\n */\nexport class Batcher<K, V, C extends string | number | null> {\n  private readonly pending = new Map<C, Promise<V>>()\n\n  protected constructor(\n    private readonly cacheKeyFn?: CacheKeyFn<K, C>,\n    /**\n     * A function that will be called to schedule the wrapped function to be\n     * executed. This defaults to a function that will execute the function\n     * immediately.\n     */\n    private readonly schedulerFn: SchedulerFn<void> = (fn) => fn()\n  ) {}\n\n  /**\n   * Creates a new instance of PendingWrapper. If the key extends a string or\n   * number, the key will be used as the cache key. If the key is an object, a\n   * cache key function must be provided.\n   */\n  public static create<K extends string | number | null, V>(\n    options?: BatcherOptions<K, K>\n  ): Batcher<K, V, K>\n  public static create<K, V, C extends string | number | null>(\n    options: BatcherOptions<K, C> &\n      Required<Pick<BatcherOptions<K, C>, 'cacheKeyFn'>>\n  ): Batcher<K, V, C>\n  public static create<K, V, C extends string | number | null>(\n    options?: BatcherOptions<K, C>\n  ): Batcher<K, V, C> {\n    return new Batcher<K, V, C>(options?.cacheKeyFn, options?.schedulerFn)\n  }\n\n  /**\n   * Wraps a function in a promise that will be resolved or rejected only once\n   * for a given key. This will allow multiple calls to the function to be\n   * made, but only one will be executed at a time. The result of the first\n   * call will be returned to all callers.\n   *\n   * @param key the key to use for the cache\n   * @param fn the function to wrap\n   * @returns a promise that resolves to the result of the function\n   */\n  public async batch(key: K, fn: WorkFn<V, C>): Promise<V> {\n    const cacheKey = (this.cacheKeyFn ? await this.cacheKeyFn(key) : key) as C\n    if (cacheKey === null) {\n      return fn(cacheKey, Promise.resolve)\n    }\n\n    const pending = this.pending.get(cacheKey)\n    if (pending) return pending\n\n    const { promise, resolve, reject } = new DetachedPromise<V>()\n    this.pending.set(cacheKey, promise)\n\n    this.schedulerFn(async () => {\n      try {\n        const result = await fn(cacheKey, resolve)\n\n        // Resolving a promise multiple times is a no-op, so we can safely\n        // resolve all pending promises with the same result.\n        resolve(result)\n      } catch (err) {\n        reject(err)\n      } finally {\n        this.pending.delete(cacheKey)\n      }\n    })\n\n    return promise\n  }\n}\n"], "names": ["Detached<PERSON>romise", "<PERSON><PERSON>", "cacheKeyFn", "schedulerFn", "fn", "pending", "Map", "create", "options", "batch", "key", "cache<PERSON>ey", "Promise", "resolve", "get", "promise", "reject", "set", "result", "err", "delete"], "mappings": ";;;AAEA,SAASA,eAAe,QAAQ,qBAAoB;;AAoB7C,MAAMC;IAGX,YACmBC,UAA6B,EAC9C;;;;KAIC,GACgBC,cAAiC,CAACC,KAAOA,IAAI,CAC9D;aAPiBF,UAAAA,GAAAA;aAMAC,WAAAA,GAAAA;aATFE,OAAAA,GAAU,IAAIC;IAU5B;IAcH,OAAcC,OACZC,OAA8B,EACZ;QAClB,OAAO,IAAIP,QAAiBO,WAAAA,OAAAA,KAAAA,IAAAA,QAASN,UAAU,EAAEM,WAAAA,OAAAA,KAAAA,IAAAA,QAASL,WAAW;IACvE;IAEA;;;;;;;;;GASC,GACD,MAAaM,MAAMC,GAAM,EAAEN,EAAgB,EAAc;QACvD,MAAMO,WAAY,IAAI,CAACT,UAAU,GAAG,MAAM,IAAI,CAACA,UAAU,CAACQ,OAAOA;QACjE,IAAIC,aAAa,MAAM;YACrB,OAAOP,GAAGO,UAAUC,QAAQC,OAAO;QACrC;QAEA,MAAMR,UAAU,IAAI,CAACA,OAAO,CAACS,GAAG,CAACH;QACjC,IAAIN,SAAS,OAAOA;QAEpB,MAAM,EAAEU,OAAO,EAAEF,OAAO,EAAEG,MAAM,EAAE,GAAG,wKAAIhB,kBAAAA;QACzC,IAAI,CAACK,OAAO,CAACY,GAAG,CAACN,UAAUI;QAE3B,IAAI,CAACZ,WAAW,CAAC;YACf,IAAI;gBACF,MAAMe,SAAS,MAAMd,GAAGO,UAAUE;gBAElC,kEAAkE;gBAClE,qDAAqD;gBACrDA,QAAQK;YACV,EAAE,OAAOC,KAAK;gBACZH,OAAOG;YACT,SAAU;gBACR,IAAI,CAACd,OAAO,CAACe,MAAM,CAACT;YACtB;QACF;QAEA,OAAOI;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/response-cache/types.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type RenderResult from '../render-result'\nimport type { CacheControl, Revalidate } from '../lib/cache-control'\nimport type { RouteKind } from '../route-kind'\n\nexport interface ResponseCacheBase {\n  get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalCache\n      /**\n       * This is a hint to the cache to help it determine what kind of route\n       * this is so it knows where to look up the cache entry from. If not\n       * provided it will test the filesystem to check.\n       */\n      routeKind: RouteKind\n\n      /**\n       * True if this is a fallback request.\n       */\n      isFallback?: boolean\n\n      /**\n       * True if the route is enabled for PPR.\n       */\n      isRoutePPREnabled?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null>\n}\n\n// The server components HMR cache might store other data as well in the future,\n// at which point this should be refactored to a discriminated union type.\nexport interface ServerComponentsHmrCache {\n  get(key: string): CachedFetchData | undefined\n  set(key: string, data: CachedFetchData): void\n}\n\nexport type CachedFetchData = {\n  headers: Record<string, string>\n  body: string\n  url: string\n  status?: number\n}\n\nexport const enum CachedRouteKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  REDIRECT = 'REDIRECT',\n  IMAGE = 'IMAGE',\n}\n\nexport interface CachedFetchValue {\n  kind: CachedRouteKind.FETCH\n  data: CachedFetchData\n  // tags are only present with file-system-cache\n  // fetch cache stores tags outside of cache entry\n  tags?: string[]\n  revalidate: number\n}\n\nexport interface CachedRedirectValue {\n  kind: CachedRouteKind.REDIRECT\n  props: Object\n}\n\nexport interface CachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  rscData: Buffer | undefined\n  status: number | undefined\n  postponed: string | undefined\n  headers: OutgoingHttpHeaders | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface CachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  pageData: Object\n  status: number | undefined\n  headers: OutgoingHttpHeaders | undefined\n}\n\nexport interface CachedRouteValue {\n  kind: CachedRouteKind.APP_ROUTE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  body: Buffer\n  status: number\n  headers: OutgoingHttpHeaders\n}\n\nexport interface CachedImageValue {\n  kind: CachedRouteKind.IMAGE\n  etag: string\n  upstreamEtag: string\n  buffer: Buffer\n  extension: string\n  isMiss?: boolean\n  isStale?: boolean\n}\n\nexport interface IncrementalCachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  rscData: Buffer | undefined\n  headers: OutgoingHttpHeaders | undefined\n  postponed: string | undefined\n  status: number | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface IncrementalCachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  pageData: Object\n  headers: OutgoingHttpHeaders | undefined\n  status: number | undefined\n}\n\nexport interface IncrementalResponseCacheEntry {\n  cacheControl?: CacheControl\n  /**\n   * timestamp in milliseconds to revalidate after\n   */\n  revalidateAfter?: Revalidate\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  isMiss?: boolean\n  value: Exclude<IncrementalCacheValue, CachedFetchValue> | null\n}\n\nexport interface IncrementalFetchCacheEntry {\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  value: CachedFetchValue\n}\n\nexport type IncrementalCacheEntry =\n  | IncrementalResponseCacheEntry\n  | IncrementalFetchCacheEntry\n\nexport type IncrementalCacheValue =\n  | CachedRedirectValue\n  | IncrementalCachedPageValue\n  | IncrementalCachedAppPageValue\n  | CachedImageValue\n  | CachedFetchValue\n  | CachedRouteValue\n\nexport type ResponseCacheValue =\n  | CachedRedirectValue\n  | CachedPageValue\n  | CachedAppPageValue\n  | CachedImageValue\n  | CachedRouteValue\n\nexport type ResponseCacheEntry = {\n  cacheControl?: CacheControl\n  value: ResponseCacheValue | null\n  isStale?: boolean | -1\n  isMiss?: boolean\n}\n\n/**\n * @param hasResolved whether the responseGenerator has resolved it's promise\n * @param previousCacheEntry the previous cache entry if it exists or the current\n */\nexport type ResponseGenerator = (state: {\n  hasResolved: boolean\n  previousCacheEntry?: IncrementalResponseCacheEntry | null\n  isRevalidating?: boolean\n  span?: any\n}) => Promise<ResponseCacheEntry | null>\n\nexport const enum IncrementalCacheKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  IMAGE = 'IMAGE',\n}\n\nexport interface GetIncrementalFetchCacheContext {\n  kind: IncrementalCacheKind.FETCH\n  revalidate?: Revalidate\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  softTags?: string[]\n}\n\nexport interface GetIncrementalResponseCacheContext {\n  kind: Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH>\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback: boolean\n}\n\nexport interface SetIncrementalFetchCacheContext {\n  fetchCache: true\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  isImplicitBuildTimeCache?: boolean\n}\n\nexport interface SetIncrementalResponseCacheContext {\n  fetchCache?: false\n  cacheControl?: CacheControl\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback?: boolean\n}\n\nexport interface IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n\nexport interface IncrementalCache extends IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind"], "mappings": ";;;;AA+CO,IAAWA,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;;;;;;WAAAA;MAOjB;AA0IM,IAAWC,uBAAAA,WAAAA,GAAAA,SAAAA,oBAAAA;;;;;;WAAAA;MAMjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/stream-utils/encoded-tags.ts"], "sourcesContent": ["export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n  META: {\n    // Only the match the prefix cause the suffix can be different wether it's xml compatible or not \">\" or \"/>\"\n    // <meta name=\"«nxt-icon»\"\n    // This is a special mark that will be replaced by the icon insertion script tag.\n    ICON_MARK: new Uint8Array([\n      60, 109, 101, 116, 97, 32, 110, 97, 109, 101, 61, 34, 194, 171, 110, 120,\n      116, 45, 105, 99, 111, 110, 194, 187, 34,\n    ]),\n  },\n} as const\n"], "names": ["ENCODED_TAGS", "OPENING", "HTML", "Uint8Array", "BODY", "CLOSED", "HEAD", "BODY_AND_HTML", "META", "ICON_MARK"], "mappings": ";;;AAAO,MAAMA,eAAe;IAC1B,iHAAiH;IACjHC,SAAS;QACP,QAAQ;QACRC,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI;QAC7C,QAAQ;QACRC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;SAAI;IAC9C;IACAE,QAAQ;QACN,UAAU;QACVC,MAAM,IAAIH,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAI;YAAK;SAAG;QACpD,UAAU;QACVC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;SAAG;QACpD,UAAU;QACVD,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAG;QACrD,iBAAiB;QACjBI,eAAe,IAAIJ,WAAW;YAC5B;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAC5D;IACH;IACAK,MAAM;QACJ,4GAA4G;QAC5G,0BAA0B;QAC1B,iFAAiF;QACjFC,WAAW,IAAIN,WAAW;YACxB;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAK;YAAI;YAAK;YAAK;YAAI;YAAI;YAAK;YAAK;YAAK;YACrE;YAAK;YAAI;YAAK;YAAI;YAAK;YAAK;YAAK;YAAK;SACvC;IACH;AACF,EAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/stream-utils/uint8array-helpers.ts"], "sourcesContent": ["/**\n * Find the starting index of Uint8Array `b` within Uint8Array `a`.\n */\nexport function indexOfUint8Array(a: Uint8Array, b: Uint8Array) {\n  if (b.length === 0) return 0\n  if (a.length === 0 || b.length > a.length) return -1\n\n  // start iterating through `a`\n  for (let i = 0; i <= a.length - b.length; i++) {\n    let completeMatch = true\n    // from index `i`, iterate through `b` and check for mismatch\n    for (let j = 0; j < b.length; j++) {\n      // if the values do not match, then this isn't a complete match, exit `b` iteration early and iterate to next index of `a`.\n      if (a[i + j] !== b[j]) {\n        completeMatch = false\n        break\n      }\n    }\n\n    if (completeMatch) {\n      return i\n    }\n  }\n\n  return -1\n}\n\n/**\n * Check if two Uint8Arrays are strictly equivalent.\n */\nexport function isEquivalentUint8Arrays(a: Uint8Array, b: Uint8Array) {\n  if (a.length !== b.length) return false\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Remove Uint8Array `b` from Uint8Array `a`.\n *\n * If `b` is not in `a`, `a` is returned unchanged.\n *\n * Otherwise, the function returns a new Uint8Array instance with size `a.length - b.length`\n */\nexport function removeFromUint8Array(a: Uint8Array, b: Uint8Array) {\n  const tagIndex = indexOfUint8Array(a, b)\n  if (tagIndex === 0) return a.subarray(b.length)\n  if (tagIndex > -1) {\n    const removed = new Uint8Array(a.length - b.length)\n    removed.set(a.slice(0, tagIndex))\n    removed.set(a.slice(tagIndex + b.length), tagIndex)\n    return removed\n  } else {\n    return a\n  }\n}\n"], "names": ["indexOfUint8Array", "a", "b", "length", "i", "completeMatch", "j", "isEquivalentUint8Arrays", "removeFromUint8Array", "tagIndex", "subarray", "removed", "Uint8Array", "set", "slice"], "mappings": "AAAA;;CAEC,GACD;;;;;AAAO,SAASA,kBAAkBC,CAAa,EAAEC,CAAa;IAC5D,IAAIA,EAAEC,MAAM,KAAK,GAAG,OAAO;IAC3B,IAAIF,EAAEE,MAAM,KAAK,KAAKD,EAAEC,MAAM,GAAGF,EAAEE,MAAM,EAAE,OAAO,CAAC;IAEnD,8BAA8B;IAC9B,IAAK,IAAIC,IAAI,GAAGA,KAAKH,EAAEE,MAAM,GAAGD,EAAEC,MAAM,EAAEC,IAAK;QAC7C,IAAIC,gBAAgB;QACpB,6DAA6D;QAC7D,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,EAAEC,MAAM,EAAEG,IAAK;YACjC,2HAA2H;YAC3H,IAAIL,CAAC,CAACG,IAAIE,EAAE,KAAKJ,CAAC,CAACI,EAAE,EAAE;gBACrBD,gBAAgB;gBAChB;YACF;QACF;QAEA,IAAIA,eAAe;YACjB,OAAOD;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAKO,SAASG,wBAAwBN,CAAa,EAAEC,CAAa;IAClE,IAAID,EAAEE,MAAM,KAAKD,EAAEC,MAAM,EAAE,OAAO;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIH,EAAEE,MAAM,EAAEC,IAAK;QACjC,IAAIH,CAAC,CAACG,EAAE,KAAKF,CAAC,CAACE,EAAE,EAAE,OAAO;IAC5B;IAEA,OAAO;AACT;AASO,SAASI,qBAAqBP,CAAa,EAAEC,CAAa;IAC/D,MAAMO,WAAWT,kBAAkBC,GAAGC;IACtC,IAAIO,aAAa,GAAG,OAAOR,EAAES,QAAQ,CAACR,EAAEC,MAAM;IAC9C,IAAIM,WAAW,CAAC,GAAG;QACjB,MAAME,UAAU,IAAIC,WAAWX,EAAEE,MAAM,GAAGD,EAAEC,MAAM;QAClDQ,QAAQE,GAAG,CAACZ,EAAEa,KAAK,CAAC,GAAGL;QACvBE,QAAQE,GAAG,CAACZ,EAAEa,KAAK,CAACL,WAAWP,EAAEC,MAAM,GAAGM;QAC1C,OAAOE;IACT,OAAO;QACL,OAAOV;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;AAAO,MAAMA,0BAA0B,yBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/segment-cache/output-export-prefetch-encoding.ts"], "sourcesContent": ["// In output: export mode, the build id is added to the start of the HTML\n// document, directly after the doctype declaration. During a prefetch, the\n// client performs a range request to get the build id, so it can check whether\n// the target page belongs to the same build.\n//\n// The first 64 bytes of the document are requested. The exact number isn't\n// too important; it must be larger than the build id + doctype + closing and\n// ending comment markers, but it doesn't need to match the end of the\n// comment exactly.\n//\n// Build ids are 21 bytes long in the default implementation, though this\n// can be overridden in the Next.js config. For the purposes of this check,\n// it's OK to only match the start of the id, so we'll truncate it if exceeds\n// a certain length.\n\nconst DOCTYPE_PREFIX = '<!DOCTYPE html>' // 15 bytes\nconst MAX_BUILD_ID_LENGTH = 24\n\n// Request the first 64 bytes. The Range header is inclusive of the end value.\nexport const DOC_PREFETCH_RANGE_HEADER_VALUE = 'bytes=0-63'\n\nfunction escapeBuildId(buildId: string) {\n  // If the build id is longer than the given limit, it's OK for our purposes\n  // to only match the beginning.\n  const truncated = buildId.slice(0, MAX_BUILD_ID_LENGTH)\n  // Replace hyphens with underscores so it doesn't break the HTML comment.\n  // (Unlikely, but if this did happen it would break the whole document.)\n  return truncated.replace(/-/g, '_')\n}\n\nexport function insertBuildIdComment(originalHtml: string, buildId: string) {\n  if (\n    // Skip if the build id contains a closing comment marker.\n    buildId.includes('-->') ||\n    // React always inserts a doctype at the start of the document. Skip if it\n    // isn't present. Shouldn't happen; suggests an issue elsewhere.\n    !originalHtml.startsWith(DOCTYPE_PREFIX)\n  ) {\n    // Return the original HTML unchanged. This means the document will not\n    // be prefetched.\n    // TODO: The build id comment is currently only used during prefetches, but\n    // if we eventually use this mechanism for regular navigations, we may need\n    // to error during build if we fail to insert it for some reason.\n    return originalHtml\n  }\n  // The comment must be inserted after the doctype.\n  return originalHtml.replace(\n    DOCTYPE_PREFIX,\n    DOCTYPE_PREFIX + '<!--' + escapeBuildId(buildId) + '-->'\n  )\n}\n\nexport function doesExportedHtmlMatchBuildId(\n  partialHtmlDocument: string,\n  buildId: string\n) {\n  // Check whether the document starts with the expected buildId.\n  return partialHtmlDocument.startsWith(\n    DOCTYPE_PREFIX + '<!--' + escapeBuildId(buildId) + '-->'\n  )\n}\n"], "names": ["DOCTYPE_PREFIX", "MAX_BUILD_ID_LENGTH", "DOC_PREFETCH_RANGE_HEADER_VALUE", "escapeBuildId", "buildId", "truncated", "slice", "replace", "insertBuildIdComment", "originalHtml", "includes", "startsWith", "doesExportedHtmlMatchBuildId", "partialHtmlDocument"], "mappings": "AAAA,yEAAyE;AACzE,2EAA2E;AAC3E,+EAA+E;AAC/E,6CAA6C;AAC7C,EAAE;AACF,2EAA2E;AAC3E,6EAA6E;AAC7E,sEAAsE;AACtE,mBAAmB;AACnB,EAAE;AACF,yEAAyE;AACzE,2EAA2E;AAC3E,6EAA6E;AAC7E,oBAAoB;;;;;;AAEpB,MAAMA,iBAAiB,kBAAkB,WAAW;;AACpD,MAAMC,sBAAsB;AAGrB,MAAMC,kCAAkC,aAAY;AAE3D,SAASC,cAAcC,OAAe;IACpC,2EAA2E;IAC3E,+BAA+B;IAC/B,MAAMC,YAAYD,QAAQE,KAAK,CAAC,GAAGL;IACnC,yEAAyE;IACzE,wEAAwE;IACxE,OAAOI,UAAUE,OAAO,CAAC,MAAM;AACjC;AAEO,SAASC,qBAAqBC,YAAoB,EAAEL,OAAe;IACxE,IAEEA,AADA,QACQM,QAAQ,CAAC,UACjB,+BAF0D,2CAEgB;IAC1E,gEAAgE;IAChE,CAACD,aAAaE,UAAU,CAACX,iBACzB;QACA,uEAAuE;QACvE,iBAAiB;QACjB,2EAA2E;QAC3E,2EAA2E;QAC3E,iEAAiE;QACjE,OAAOS;IACT;IACA,kDAAkD;IAClD,OAAOA,aAAaF,OAAO,CACzBP,gBACAA,iBAAiB,SAASG,cAAcC,WAAW;AAEvD;AAEO,SAASQ,6BACdC,mBAA2B,EAC3BT,OAAe;IAEf,+DAA+D;IAC/D,OAAOS,oBAAoBF,UAAU,CACnCX,iBAAiB,SAASG,cAAcC,WAAW;AAEvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encoded-tags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\nimport { insertBuildIdComment } from '../../shared/lib/segment-cache/output-export-prefetch-encoding'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nfunction createPrefetchCommentStream(\n  isBuildTimePrerendering: boolean,\n  buildId: string\n): TransformStream<Uint8Array, Uint8Array> {\n  // Insert an extra comment at the beginning of the HTML document. This must\n  // come after the DOCTYPE, which is inserted by React.\n  //\n  // The first chunk sent by React will contain the doctype. After that, we can\n  // pass through the rest of the chunks as-is.\n  let didTransformFirstChunk = false\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (isBuildTimePrerendering && !didTransformFirstChunk) {\n        didTransformFirstChunk = true\n        const decoder = new TextDecoder('utf-8', { fatal: true })\n        const chunkStr = decoder.decode(chunk, {\n          stream: true,\n        })\n        const updatedChunkStr = insertBuildIdComment(chunkStr, buildId)\n        controller.enqueue(encoder.encode(updatedChunkStr))\n        return\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: {\n    renderToReadableStream: typeof import('react-dom/server').renderToReadableStream\n  }\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createMetadataTransformStream(\n  insert: () => Promise<string> | string\n): TransformStream<Uint8Array, Uint8Array> {\n  let chunkIndex = -1\n  let isMarkRemoved = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      let iconMarkIndex = -1\n      let closedHeadIndex = -1\n      chunkIndex++\n\n      if (isMarkRemoved) {\n        controller.enqueue(chunk)\n        return\n      }\n      let iconMarkLength = 0\n      // Only search for the closed head tag once\n      if (iconMarkIndex === -1) {\n        iconMarkIndex = indexOfUint8Array(chunk, ENCODED_TAGS.META.ICON_MARK)\n        if (iconMarkIndex === -1) {\n          controller.enqueue(chunk)\n          return\n        } else {\n          // When we found the `<meta name=\"«nxt-icon»\"` tag prefix, we will remove it from the chunk.\n          // Its close tag could either be `/>` or `>`, checking the next char to ensure we cover both cases.\n          iconMarkLength = ENCODED_TAGS.META.ICON_MARK.length\n          // Check if next char is /, this is for xml mode.\n          if (chunk[iconMarkIndex + iconMarkLength] === 47) {\n            iconMarkLength += 2\n          } else {\n            // The last char is `>`\n            iconMarkLength++\n          }\n        }\n      }\n\n      // Check if icon mark is inside <head> tag in the first chunk.\n      if (chunkIndex === 0) {\n        closedHeadIndex = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        if (iconMarkIndex !== -1) {\n          // The mark icon is located in the 1st chunk before the head tag.\n          // We do not need to insert the script tag in this case because it's in the head.\n          // Just remove the icon mark from the chunk.\n          if (iconMarkIndex < closedHeadIndex) {\n            const replaced = new Uint8Array(chunk.length - iconMarkLength)\n\n            // Remove the icon mark from the chunk.\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex\n            )\n            chunk = replaced\n          } else {\n            // The icon mark is after the head tag, replace and insert the script tag at that position.\n            const insertion = await insert()\n            const encodedInsertion = encoder.encode(insertion)\n            const insertionLength = encodedInsertion.length\n            const replaced = new Uint8Array(\n              chunk.length - iconMarkLength + insertionLength\n            )\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(encodedInsertion, iconMarkIndex)\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex + insertionLength\n            )\n            chunk = replaced\n          }\n          isMarkRemoved = true\n        }\n        // If there's no icon mark located, it will be handled later when if present in the following chunks.\n      } else {\n        // When it's appeared in the following chunks, we'll need to\n        // remove the mark and then insert the script tag at that position.\n        const insertion = await insert()\n        const encodedInsertion = encoder.encode(insertion)\n        const insertionLength = encodedInsertion.length\n        // Replace the icon mark with the hoist script or empty string.\n        const replaced = new Uint8Array(\n          chunk.length - iconMarkLength + insertionLength\n        )\n        // Set the first part of the chunk, before the icon mark.\n        replaced.set(chunk.subarray(0, iconMarkIndex))\n        // Set the insertion after the icon mark.\n        replaced.set(encodedInsertion, iconMarkIndex)\n\n        // Set the rest of the chunk after the icon mark.\n        replaced.set(\n          chunk.subarray(iconMarkIndex + iconMarkLength),\n          iconMarkIndex + insertionLength\n        )\n        chunk = replaced\n        isMarkRemoved = true\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  isBuildTimePrerendering: boolean\n  buildId: string\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    isBuildTimePrerendering,\n    buildId,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Add build id comment to start of the HTML document (in export mode)\n    createPrefetchCommentStream(isBuildTimePrerendering, buildId),\n\n    // Transform metadata\n    createMetadataTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  isBuildTimePrerendering: boolean\n  buildId: string\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    isBuildTimePrerendering,\n    buildId,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Add build id comment to start of the HTML document (in export mode)\n      .pipeThrough(\n        createPrefetchCommentStream(isBuildTimePrerendering, buildId)\n      )\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["getTracer", "AppRenderSpan", "Detached<PERSON>romise", "scheduleImmediate", "atLeastOneTask", "ENCODED_TAGS", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "MISSING_ROOT_TAGS_ERROR", "insertBuildIdComment", "voidCatch", "encoder", "TextEncoder", "chainStreams", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "streamFromString", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "streamFromBuffer", "chunk", "streamToBuffer", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "streamToString", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "createBufferedTransformStream", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "createPrefetchCommentStream", "isBuildTimePrerendering", "buildId", "didTransformFirstChunk", "chunkStr", "updatedChunkStr", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createMetadataTransformStream", "insert", "chunkIndex", "isMarkRemoved", "iconMarkIndex", "closedHeadIndex", "iconMarkLength", "META", "ICON_MARK", "CLOSED", "HEAD", "replaced", "subarray", "insertion", "encodedInsertion", "<PERSON><PERSON><PERSON><PERSON>", "createHeadInsertionTransformStream", "inserted", "hasBytes", "index", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "BODY", "HTML", "createRootLayoutValidatorStream", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "chainTransformers", "transformers", "transformer", "pipeThrough", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "continueDynamicPrerender", "prerenderStream", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "createDocumentClosingStream"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,sBAAqB;AACvE,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SACEC,iBAAiB,EACjBC,uBAAuB,EACvBC,oBAAoB,QACf,uBAAsB;AAC7B,SAASC,uBAAuB,QAAQ,oCAAmC;AAC3E,SAASC,oBAAoB,QAAQ,iEAAgE;;;;;;;;;AAErG,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASC,aACd,GAAGC,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,OAAA,cAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACjB;IAEd,OAAOO;AACT;AAEO,SAASW,iBAAiBC,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,SAASC,iBAAiBC,KAAa;IAC5C,OAAO,IAAIP,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACI;YACnBL,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAeG,eACpBC,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAeO,eACpBV,MAAkC,EAClCW,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMjB,SAASE,OAAQ;QAChC,IAAIW,UAAAA,OAAAA,KAAAA,IAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACnB,OAAO;YAAEE,QAAQ;QAAK;IACjD;IAEAe,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAASG;IAId,IAAIC,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,yDAAyD;QACzD,IAAI4B,SAAS;QAEb,MAAME,WAAW,uKAAI7D,mBAAAA;QACrB2D,UAAUE;sKAEV5D,oBAAAA,EAAkB;YAChB,IAAI;gBACF,MAAMmC,QAAQ,IAAI0B,WAAWJ;gBAC7B,IAAIK,cAAc;gBAElB,IAAK,IAAIzC,IAAI,GAAGA,IAAImC,eAAe3C,MAAM,EAAEQ,IAAK;oBAC9C,MAAM0C,gBAAgBP,cAAc,CAACnC,EAAE;oBACvCc,MAAM6B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5ET,eAAe3C,MAAM,GAAG;gBACxB4C,mBAAmB;gBACnB3B,WAAWC,OAAO,CAACI;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRuB,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,kDAAkD;YAClD0B,eAAeZ,IAAI,CAACT;YACpBsB,oBAAoBtB,MAAM8B,UAAU;YAEpC,sCAAsC;YACtCN,MAAM7B;QACR;QACA6B;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQxC,OAAO;QACxB;IACF;AACF;AAEA,SAASmD,4BACPC,uBAAgC,EAChCC,OAAe;IAEf,2EAA2E;IAC3E,sDAAsD;IACtD,EAAE;IACF,6EAA6E;IAC7E,6CAA6C;IAC7C,IAAIC,yBAAyB;IAC7B,OAAO,IAAIvD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,IAAIwC,2BAA2B,CAACE,wBAAwB;gBACtDA,yBAAyB;gBACzB,MAAMvB,UAAU,IAAIC,YAAY,SAAS;oBAAEC,OAAO;gBAAK;gBACvD,MAAMsB,WAAWxB,QAAQK,MAAM,CAACnB,OAAO;oBACrCE,QAAQ;gBACV;gBACA,MAAMqC,kBAAkBnE,kPAAAA,EAAqBkE,UAAUF;gBACvDzC,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC0C;gBAClC;YACF;YACA5C,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEO,SAASwC,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAOd;IACC,qLAAOjF,YAAAA,IAAYkF,KAAK,8KAACjF,gBAAAA,CAAckF,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,8BACPC,MAAsC;IAEtC,IAAIC,aAAa,CAAC;IAClB,IAAIC,gBAAgB;IAEpB,OAAO,IAAInE,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/B,IAAIuD,gBAAgB,CAAC;YACrB,IAAIC,kBAAkB,CAAC;YACvBH;YAEA,IAAIC,eAAe;gBACjBtD,WAAWC,OAAO,CAACI;gBACnB;YACF;YACA,IAAIoD,iBAAiB;YACrB,2CAA2C;YAC3C,IAAIF,kBAAkB,CAAC,GAAG;gBACxBA,gNAAgBlF,oBAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAasF,IAAI,CAACC,SAAS;gBACpE,IAAIJ,kBAAkB,CAAC,GAAG;oBACxBvD,WAAWC,OAAO,CAACI;oBACnB;gBACF,OAAO;oBACL,4FAA4F;oBAC5F,mGAAmG;oBACnGoD,uMAAiBrF,eAAAA,CAAasF,IAAI,CAACC,SAAS,CAAC5E,MAAM;oBACnD,iDAAiD;oBACjD,IAAIsB,KAAK,CAACkD,gBAAgBE,eAAe,KAAK,IAAI;wBAChDA,kBAAkB;oBACpB,OAAO;wBACL,uBAAuB;wBACvBA;oBACF;gBACF;YACF;YAEA,8DAA8D;YAC9D,IAAIJ,eAAe,GAAG;gBACpBG,kNAAkBnF,oBAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAawF,MAAM,CAACC,IAAI;gBACnE,IAAIN,kBAAkB,CAAC,GAAG;oBACxB,iEAAiE;oBACjE,iFAAiF;oBACjF,4CAA4C;oBAC5C,IAAIA,gBAAgBC,iBAAiB;wBACnC,MAAMM,WAAW,IAAI/B,WAAW1B,MAAMtB,MAAM,GAAG0E;wBAE/C,uCAAuC;wBACvCK,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;wBAC/BO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF;wBAEFlD,QAAQyD;oBACV,OAAO;wBACL,2FAA2F;wBAC3F,MAAME,YAAY,MAAMZ;wBACxB,MAAMa,mBAAmBtF,QAAQuB,MAAM,CAAC8D;wBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;wBAC/C,MAAM+E,WAAW,IAAI/B,WACnB1B,MAAMtB,MAAM,GAAG0E,iBAAiBS;wBAElCJ,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;wBAC/BO,SAAS5B,GAAG,CAAC+B,kBAAkBV;wBAC/BO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF,gBAAgBW;wBAElB7D,QAAQyD;oBACV;oBACAR,gBAAgB;gBAClB;YACA,qGAAqG;YACvG,OAAO;gBACL,4DAA4D;gBAC5D,mEAAmE;gBACnE,MAAMU,YAAY,MAAMZ;gBACxB,MAAMa,mBAAmBtF,QAAQuB,MAAM,CAAC8D;gBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;gBAC/C,+DAA+D;gBAC/D,MAAM+E,WAAW,IAAI/B,WACnB1B,MAAMtB,MAAM,GAAG0E,iBAAiBS;gBAElC,yDAAyD;gBACzDJ,SAAS5B,GAAG,CAAC7B,MAAM0D,QAAQ,CAAC,GAAGR;gBAC/B,yCAAyC;gBACzCO,SAAS5B,GAAG,CAAC+B,kBAAkBV;gBAE/B,iDAAiD;gBACjDO,SAAS5B,GAAG,CACV7B,MAAM0D,QAAQ,CAACR,gBAAgBE,iBAC/BF,gBAAgBW;gBAElB7D,QAAQyD;gBACRR,gBAAgB;YAClB;YACAtD,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA,SAAS8D,mCACPf,MAA6B;IAE7B,IAAIgB,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAIlF,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/BqE,WAAW;YAEX,MAAML,YAAY,MAAMZ;YACxB,IAAIgB,UAAU;gBACZ,IAAIJ,WAAW;oBACb,MAAMC,mBAAmBtF,QAAQuB,MAAM,CAAC8D;oBACxChE,WAAWC,OAAO,CAACgE;gBACrB;gBACAjE,WAAWC,OAAO,CAACI;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAMiE,QAAQjG,oNAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAawF,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIS,UAAU,CAAC,GAAG;oBAChB,IAAIN,WAAW;wBACb,MAAMC,mBAAmBtF,QAAQuB,MAAM,CAAC8D;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAIxC,WAC9B1B,MAAMtB,MAAM,GAAGkF,iBAAiBlF,MAAM;wBAExC,0DAA0D;wBAC1DwF,oBAAoBrC,GAAG,CAAC7B,MAAMmE,KAAK,CAAC,GAAGF;wBACvC,qCAAqC;wBACrCC,oBAAoBrC,GAAG,CAAC+B,kBAAkBK;wBAC1C,+BAA+B;wBAC/BC,oBAAoBrC,GAAG,CACrB7B,MAAMmE,KAAK,CAACF,QACZA,QAAQL,iBAAiBlF,MAAM;wBAEjCiB,WAAWC,OAAO,CAACsE;oBACrB,OAAO;wBACLvE,WAAWC,OAAO,CAACI;oBACrB;oBACA+D,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIJ,WAAW;wBACbhE,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC8D;oBACpC;oBACAhE,WAAWC,OAAO,CAACI;oBACnB+D,WAAW;gBACb;YACF;QACF;QACA,MAAMvC,OAAM7B,UAAU;YACpB,gEAAgE;YAChE,IAAIqE,UAAU;gBACZ,MAAML,YAAY,MAAMZ;gBACxB,IAAIY,WAAW;oBACbhE,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAAC8D;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAI/C;IAEJ,MAAMC,QAAQ,CAAC7B;QACb,MAAM8B,WAAW,wKAAI7D,kBAAAA;QACrB2D,UAAUE;sKAEV5D,oBAAAA,EAAkB;YAChB,IAAI;gBACF8B,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACwE;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACR9C,UAAUQ;gBACVN,SAASO,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIlD,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,wCAAwC;YACxC,IAAIsE,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACV9C,MAAM7B;QACR;QACA6B,OAAM7B,UAAU;YACd,IAAI4B,SAAS,OAAOA,QAAQxC,OAAO;YACnC,IAAIuF,SAAS;YAEb,aAAa;YACb3E,WAAWC,OAAO,CAACtB,QAAQuB,MAAM,CAACwE;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPrE,MAAkC;IAElC,IAAIsE,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa/E,UAA4C;QACtE,IAAI6E,MAAM;YACR;QACF;QAEA,MAAMrE,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,oKAAMtC,iBAAAA;QAEN,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEwC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRmE,cAAc;oBACd;gBACF;gBAEA9E,WAAWC,OAAO,CAACW;YACrB;QACF,EAAE,OAAOoE,KAAK;YACZhF,WAAWiF,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI7F,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzBA,WAAWC,OAAO,CAACI;YAEnB,6DAA6D;YAC7D,IAAI,CAACwE,MAAM;gBACTA,OAAOE,aAAa/E;YACtB;QACF;QACA6B,OAAM7B,UAAU;YACd,IAAI8E,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa/E;QAC9B;IACF;AACF;AAEA,MAAMkF,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIjG,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,IAAIoF,aAAa;gBACf,OAAOpF,WAAWC,OAAO,CAACI;YAC5B;YAEA,MAAMiE,QAAQjG,oNAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAawF,MAAM,CAACyB,aAAa;YACxE,IAAIf,QAAQ,CAAC,GAAG;gBACdc,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAI/E,MAAMtB,MAAM,2LAAKX,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMuG,SAASjF,MAAMmE,KAAK,CAAC,GAAGF;gBAC9BtE,WAAWC,OAAO,CAACqF;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIjF,MAAMtB,MAAM,yLAAGX,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM,GAAGuF,OAAO;oBACnE,uCAAuC;oBACvC,MAAMiB,QAAQlF,MAAMmE,KAAK,CACvBF,8LAAQlG,eAAAA,CAAawF,MAAM,CAACyB,aAAa,CAACtG,MAAM;oBAElDiB,WAAWC,OAAO,CAACsF;gBACrB;YACF,OAAO;gBACLvF,WAAWC,OAAO,CAACI;YACrB;QACF;QACAwB,OAAM7B,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,uLAAC7B,eAAAA,CAAawF,MAAM,CAACyB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIrG,gBAAgB;QACzBmD,WAAUjC,KAAK,EAAEL,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,oMACE1B,0BAAAA,EAAwB+B,6LAAOjC,eAAAA,CAAawF,MAAM,CAACyB,aAAa,qMAChE/G,0BAAAA,EAAwB+B,OAAOjC,qMAAAA,CAAawF,MAAM,CAAC6B,IAAI,qMACvDnH,0BAAAA,EAAwB+B,6LAAOjC,eAAAA,CAAawF,MAAM,CAAC8B,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFrF,wMAAQ9B,uBAAAA,EAAqB8B,6LAAOjC,eAAAA,CAAawF,MAAM,CAAC6B,IAAI;YAC5DpF,SAAQ9B,sNAAAA,EAAqB8B,6LAAOjC,eAAAA,CAAawF,MAAM,CAAC8B,IAAI;YAE5D1F,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAOO,SAASsF;IAId,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI1G,gBAAgB;QACzB,MAAMmD,WAAUjC,KAAK,EAAEL,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC4F,cACDvH,mNAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAa0H,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACDxH,oNAAAA,EAAkBgC,6LAAOjC,eAAAA,CAAa0H,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA7F,WAAWC,OAAO,CAACI;QACrB;QACAwB,OAAM7B,UAAU;YACd,MAAM+F,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYjF,IAAI,CAAC;YACjC,IAAI,CAAC+E,WAAWE,YAAYjF,IAAI,CAAC;YAEjC,IAAI,CAACiF,YAAYhH,MAAM,EAAE;YAEzBiB,WAAWC,OAAO,CAChBtB,QAAQuB,MAAM,CACZ,CAAC;;+CAEoC,EAAE6F,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYhH,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,gLAAEP,0BAAAA,CAAwB;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAAS2H,kBACPlH,QAA2B,EAC3BmH,YAAyD;IAEzD,IAAI7F,SAAStB;IACb,KAAK,MAAMoH,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB9F,SAASA,OAAO+F,WAAW,CAACD;IAC9B;IACA,OAAO9F;AACT;AAgBO,eAAegG,mBACpBC,YAAiC,EACjC,EACE9B,MAAM,EACN+B,iBAAiB,EACjBC,kBAAkB,EAClBlE,uBAAuB,EACvBC,OAAO,EACPkE,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBpC,SAASA,OAAOqC,KAAK,CAAC7B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIwB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOb,kBAAkBK,cAAc;QACrC,qDAAqD;QACrD/E;QAEA,sEAAsE;QACtEc,4BAA4BC,yBAAyBC;QAErD,qBAAqB;QACrBU,8BAA8ByD;QAE9B,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAe/H,MAAM,GAAG,IAC9C0F,2BAA2BqC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB7B,4BAA4B6B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBlB,oCAAoC;QAEzD,kDAAkD;QAClDR;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/EhB,mCAAmCwC;KACpC;AACH;AAOO,eAAeM,yBACpBC,eAA2C,EAC3C,EACEP,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEM,gBACE,qDAAqD;KACpDZ,WAAW,CAAC7E,iCACZ6E,WAAW,CAACd,2CACb,gCAAgC;KAC/Bc,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD;AAEjD;AAUO,eAAeO,wBACpBD,eAA2C,EAC3C,EACET,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACzBpE,uBAAuB,EACvBC,OAAO,EACwB;IAEjC,OACEyE,gBACE,qDAAqD;KACpDZ,WAAW,CAAC7E,iCACb,sEAAsE;KACrE6E,WAAW,CACV/D,4BAA4BC,yBAAyBC,UAEvD,gCAAgC;KAC/B6D,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD,4BAC3C,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,oBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAQO,eAAeiC,0BACpBZ,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,aACE,qDAAqD;KACpDF,WAAW,CAAC7E,iCACb,gCAAgC;KAC/B6E,WAAW,CAACnC,mCAAmCwC,wBAChD,qBAAqB;KACpBL,WAAW,CAACnD,8BAA8ByD,4BAC3C,+EAA+E;KAC9EN,WAAW,CAAC1B,4BAA4B6B,oBACzC,kDAAkD;KACjDH,WAAW,CAACnB;AAEnB;AAEO,SAASkC;IACd,OAAOzH,iBAAiBsF;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/request-meta.ts"], "sourcesContent": ["/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\nimport type { PagesDevOverlayBridgeType } from '../next-devtools/userspace/pages/pages-dev-overlay-setup'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * A search param set by the Next.js client when performing RSC requests.\n   * Because some CDNs do not vary their cache entries on our custom headers,\n   * this search param represents a hash of the header values. For any cached\n   * RSC request, we should verify that the hash matches before responding.\n   * Otherwise this can lead to cache poisoning.\n   * TODO: Consider not using custom request headers at all, and instead encode\n   * everything into the search param.\n   */\n  cacheBustingSearchParam?: string\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the request should render the fallback shell or not.\n   */\n  renderFallbackShell?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n\n  /**\n   * The project dir the server is running in\n   */\n  projectDir?: string\n\n  /**\n   * The dist directory the server is currently using\n   */\n  distDir?: string\n\n  /**\n   * Whether we are generating the fallback version of the page in dev mode\n   */\n  isIsrFallback?: boolean\n\n  /**\n   * The query after resolving routes\n   */\n  query?: ParsedUrlQuery\n\n  /**\n   * The params after resolving routes\n   */\n  params?: ParsedUrlQuery\n\n  /**\n   * The AMP validator to use in development\n   */\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n\n  /**\n   * ErrorOverlay component to use in development for pages router\n   */\n  PagesErrorDebug?: PagesDevOverlayBridgeType\n\n  /**\n   * Whether server is in minimal mode (this will be replaced with more\n   * specific flags in future)\n   */\n  minimalMode?: boolean\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n"], "names": ["NEXT_REQUEST_META", "Symbol", "for", "getRequestMeta", "req", "key", "meta", "setRequestMeta", "addRequestMeta", "request", "value", "removeRequestMeta"], "mappings": "AAAA,+BAA+B,GAW/B,kGAAkG;;;;;;;;AAC3F,MAAMA,oBAAoBC,OAAOC,GAAG,CAAC,2BAA0B;AAoP/D,SAASC,eACdC,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACJ,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOK,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASC,eAAeH,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACJ,kBAAkB,GAAGM;IACzB,OAAOA;AACT;AAUO,SAASE,eACdC,OAA4B,EAC5BJ,GAAM,EACNK,KAAqB;IAErB,MAAMJ,OAAOH,eAAeM;IAC5BH,IAAI,CAACD,IAAI,GAAGK;IACZ,OAAOH,eAAeE,SAASH;AACjC;AASO,SAASK,kBACdF,OAA4B,EAC5BJ,GAAM;IAEN,MAAMC,OAAOH,eAAeM;IAC5B,OAAOH,IAAI,CAACD,IAAI;IAChB,OAAOE,eAAeE,SAASH;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/utils.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n"], "names": ["NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "fromNodeOutgoingHttpHeaders", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "splitCookiesString", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "toNodeOutgoingHttpHeaders", "cookies", "toLowerCase", "validateURL", "url", "String", "URL", "error", "Error", "cause", "normalizeNextQueryParam", "prefixes", "prefix", "startsWith"], "mappings": ";;;;;;;AACA,SACEA,+BAA+B,EAC/BC,uBAAuB,QAClB,sBAAqB;;AAWrB,SAASC,4BACdC,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAYO,SAASY,mBAAmBC,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AASO,SAASc,0BACd5B,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM8B,UAAoB,EAAE;IAC5B,IAAI7B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI4B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQH,IAAI,IAAId,mBAAmBT;gBACnCJ,WAAW,CAACG,IAAI,GAAG2B,QAAQP,MAAM,KAAK,IAAIO,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL9B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASgC,YAAYC,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,OAAA,cAKL,CALK,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM,IAJX,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAMO,SAASG,wBAAwBpC,GAAW;IACjD,MAAMqC,WAAW;kKAAC1C,0BAAAA;kKAAyBD,kCAAAA;KAAgC;IAC3E,KAAK,MAAM4C,UAAUD,SAAU;QAC7B,IAAIrC,QAAQsC,UAAUtC,IAAIuC,UAAU,CAACD,SAAS;YAC5C,OAAOtC,IAAIyB,SAAS,CAACa,OAAOlB,MAAM;QACpC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/i18n/detect-domain-locale.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n"], "names": ["detectDomainLocale", "domainItems", "hostname", "detectedLocale", "toLowerCase", "item", "domainHostname", "domain", "split", "defaultLocale", "locales", "some", "locale"], "mappings": ";;;AAEO,SAASA,mBACdC,WAAqC,EACrCC,QAAiB,EACjBC,cAAuB;IAEvB,IAAI,CAACF,aAAa;IAElB,IAAIE,gBAAgB;QAClBA,iBAAiBA,eAAeC,WAAW;IAC7C;IAEA,KAAK,MAAMC,QAAQJ,YAAa;YAEPI,cAIrBA;QALF,yBAAyB;QACzB,MAAMC,iBAAAA,CAAiBD,eAAAA,KAAKE,MAAM,KAAA,OAAA,KAAA,IAAXF,aAAaG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACJ,WAAW;QAChE,IACEF,aAAaI,kBACbH,mBAAmBE,KAAKI,aAAa,CAACL,WAAW,MAAA,CAAA,CACjDC,gBAAAA,KAAKK,OAAO,KAAA,OAAA,KAAA,IAAZL,cAAcM,IAAI,CAAC,CAACC,SAAWA,OAAOR,WAAW,OAAOD,eAAAA,GACxD;YACA,OAAOE;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GACD;;;AAAO,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GACD;;;AAAO,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["parsePath", "addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AAMjC,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,kMAAGP,YAAAA,EAAUE;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/add-path-suffix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n"], "names": ["parsePath", "addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AAOjC,SAASC,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,kMAAGP,YAAAA,EAAUE;IAC5C,OAAQ,KAAEG,WAAWF,SAASG,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["parsePath", "pathHasPrefix", "path", "prefix", "pathname", "startsWith"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAc;;AASjC,SAASC,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,kMAAGJ,YAAAA,EAAUE;IAC/B,OAAOE,aAAaD,UAAUC,SAASC,UAAU,CAACF,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/add-locale.ts"], "sourcesContent": ["import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n"], "names": ["addPathPrefix", "pathHasPrefix", "addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "lower", "toLowerCase"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,oBAAmB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;;;AAO1C,SAASC,UACdC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB;IAEtB,4EAA4E;IAC5E,sBAAsB;IACtB,IAAI,CAACF,UAAUA,WAAWC,eAAe,OAAOF;IAEhD,MAAMI,QAAQJ,KAAKK,WAAW;IAE9B,2EAA2E;IAC3E,iCAAiC;IACjC,IAAI,CAACF,cAAc;QACjB,2MAAIL,gBAAAA,EAAcM,OAAO,SAAS,OAAOJ;QACzC,2MAAIF,gBAAAA,EAAcM,OAAQ,MAAGH,OAAOI,WAAW,KAAO,OAAOL;IAC/D;IAEA,qCAAqC;IACrC,8MAAOH,gBAAAA,EAAcG,MAAO,MAAGC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/format-next-pathname-info.ts"], "sourcesContent": ["import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n"], "names": ["removeTrailingSlash", "addPathPrefix", "addPathSuffix", "addLocale", "formatNextPathnameInfo", "info", "pathname", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "trailingSlash", "basePath", "endsWith"], "mappings": ";;;AACA,SAASA,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,SAAS,QAAQ,eAAc;;;;;AAOjC,SAASC,uBAAuBC,IAAkB;IACvD,IAAIC,0MAAWH,YAAAA,EACbE,KAAKC,QAAQ,EACbD,KAAKE,MAAM,EACXF,KAAKG,OAAO,GAAGC,YAAYJ,KAAKK,aAAa,EAC7CL,KAAKM,YAAY;IAGnB,IAAIN,KAAKG,OAAO,IAAI,CAACH,KAAKO,aAAa,EAAE;QACvCN,wNAAWN,sBAAAA,EAAoBM;IACjC;IAEA,IAAID,KAAKG,OAAO,EAAE;QAChBF,kNAAWJ,gBAAAA,yMACTD,gBAAAA,EAAcK,UAAW,iBAAcD,KAAKG,OAAO,GACnDH,KAAKC,QAAQ,KAAK,MAAM,eAAe;IAE3C;IAEAA,kNAAWL,gBAAAA,EAAcK,UAAUD,KAAKQ,QAAQ;IAChD,OAAO,CAACR,KAAKG,OAAO,IAAIH,KAAKO,aAAa,GACtC,CAACN,SAASQ,QAAQ,CAAC,8MACjBZ,gBAAAA,EAAcI,UAAU,OACxBA,wNACFN,sBAAAA,EAAoBM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/get-hostname.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n"], "names": ["getHostname", "parsed", "headers", "hostname", "host", "Array", "isArray", "toString", "split", "toLowerCase"], "mappings": "AAEA;;;;;CAK<PERSON>,GACD;;;AAAO,SAASA,YACdC,MAAoC,EACpCC,OAA6B;IAE7B,2EAA2E;IAC3E,YAAY;IACZ,IAAIC;IACJ,IAAID,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASE,IAAI,KAAI,CAACC,MAAMC,OAAO,CAACJ,QAAQE,IAAI,GAAG;QACjDD,WAAWD,QAAQE,IAAI,CAACG,QAAQ,GAAGC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IACrD,OAAO,IAAIP,OAAOE,QAAQ,EAAE;QAC1BA,WAAWF,OAAOE,QAAQ;IAC5B,OAAO;IAEP,OAAOA,SAASM,WAAW;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n"], "names": ["cache", "WeakMap", "normalizeLocalePath", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length"], "mappings": "AAKA;;;;CAIC;;;AACD,MAAMA,QAAQ,IAAIC;AAWX,SAASC,oBACdC,QAAgB,EAChBC,OAA2B;IAE3B,sDAAsD;IACtD,IAAI,CAACA,SAAS,OAAO;QAAED;IAAS;IAEhC,iEAAiE;IACjE,IAAIE,oBAAoBL,MAAMM,GAAG,CAACF;IAClC,IAAI,CAACC,mBAAmB;QACtBA,oBAAoBD,QAAQG,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QAC9DT,MAAMU,GAAG,CAACN,SAASC;IACrB;IAEA,IAAIM;IAEJ,oEAAoE;IACpE,yEAAyE;IACzE,MAAMC,WAAWT,SAASU,KAAK,CAAC,KAAK;IAErC,0EAA0E;IAC1E,UAAU;IACV,IAAI,CAACD,QAAQ,CAAC,EAAE,EAAE,OAAO;QAAET;IAAS;IAEpC,0DAA0D;IAC1D,MAAMW,UAAUF,QAAQ,CAAC,EAAE,CAACH,WAAW;IAEvC,yEAAyE;IACzE,mCAAmC;IACnC,MAAMM,QAAQV,kBAAkBW,OAAO,CAACF;IACxC,IAAIC,QAAQ,GAAG,OAAO;QAAEZ;IAAS;IAEjC,oCAAoC;IACpCQ,iBAAiBP,OAAO,CAACW,MAAM;IAE/B,gDAAgD;IAChDZ,WAAWA,SAASc,KAAK,CAACN,eAAeO,MAAM,GAAG,MAAM;IAExD,OAAO;QAAEf;QAAUQ;IAAe;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/remove-path-prefix.ts"], "sourcesContent": ["import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n"], "names": ["pathHasPrefix", "removePathPrefix", "path", "prefix", "withoutPrefix", "slice", "length", "startsWith"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,oBAAmB;;AAU1C,SAASC,iBAAiBC,IAAY,EAAEC,MAAc;IAC3D,yEAAyE;IACzE,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,IAAI,wMAACH,gBAAAA,EAAcE,MAAMC,SAAS;QAChC,OAAOD;IACT;IAEA,+CAA+C;IAC/C,MAAME,gBAAgBF,KAAKG,KAAK,CAACF,OAAOG,MAAM;IAE9C,2EAA2E;IAC3E,IAAIF,cAAcG,UAAU,CAAC,MAAM;QACjC,OAAOH;IACT;IAEA,4EAA4E;IAC5E,mDAAmD;IACnD,OAAQ,MAAGA;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["normalizeLocalePath", "removePathPrefix", "pathHasPrefix", "getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "locales", "locale", "detectedLocale"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,aAAa,QAAQ,oBAAmB;;;;AAkD1C,SAASC,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,KAAA,OAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYJ,uNAAAA,EAAcQ,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGH,6NAAAA,EAAiBS,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIM,uBAAuBF,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACS,UAAU,CAAC,mBACzBH,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMG,QAAQJ,KAAKN,QAAQ,CACxBW,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBJ,KAAKO,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAW,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAId,QAAQe,SAAS,KAAK,MAAM;YAC9BV,KAAKN,QAAQ,GAAGQ;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIL,MAAM;QACR,IAAIc,SAAShB,QAAQiB,YAAY,GAC7BjB,QAAQiB,YAAY,CAACC,OAAO,CAACb,KAAKN,QAAQ,sMAC1CJ,sBAAAA,EAAoBU,KAAKN,QAAQ,EAAEG,KAAKiB,OAAO;QAEnDd,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACnBL;QAAhBX,KAAKN,QAAQ,GAAGiB,CAAAA,mBAAAA,OAAOjB,QAAQ,KAAA,OAAfiB,mBAAmBX,KAAKN,QAAQ;QAEhD,IAAI,CAACiB,OAAOK,cAAc,IAAIhB,KAAKO,OAAO,EAAE;YAC1CI,SAAShB,QAAQiB,YAAY,GACzBjB,QAAQiB,YAAY,CAACC,OAAO,CAACX,0NAC7BZ,sBAAAA,EAAoBY,sBAAsBL,KAAKiB,OAAO;YAE1D,IAAIH,OAAOK,cAAc,EAAE;gBACzBhB,KAAKe,MAAM,GAAGJ,OAAOK,cAAc;YACrC;QACF;IACF;IACA,OAAOhB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/next-url.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n"], "names": ["detectDomainLocale", "formatNextPathnameInfo", "getHostname", "getNextPathnameInfo", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "Internal", "Symbol", "NextURL", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyze", "info", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "i18nProvider", "hostname", "headers", "domainLocale", "i18n", "domains", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "forceLocale", "undefined", "formatSearch", "search", "locales", "includes", "TypeError", "searchParams", "host", "value", "port", "protocol", "href", "hash", "origin", "password", "username", "startsWith", "toString", "toJSON", "for", "clone"], "mappings": ";;;AAIA,SAASA,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,sBAAsB,QAAQ,0DAAyD;AAChG,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uDAAsD;;;;;AAc1F,MAAMC,2BACJ;AAEF,SAASC,SAASC,GAAiB,EAAEC,IAAmB;IACtD,OAAO,IAAIC,IACTC,OAAOH,KAAKI,OAAO,CAACN,0BAA0B,cAC9CG,QAAQE,OAAOF,MAAMG,OAAO,CAACN,0BAA0B;AAE3D;AAEA,MAAMO,WAAWC,OAAO;AAEjB,MAAMC;IAeXC,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIV;QACJ,IAAIW;QAEJ,IACG,OAAOF,eAAe,YAAY,cAAcA,cACjD,OAAOA,eAAe,UACtB;YACAT,OAAOS;YACPE,UAAUD,QAAQ,CAAC;QACrB,OAAO;YACLC,UAAUD,QAAQD,cAAc,CAAC;QACnC;QAEA,IAAI,CAACL,SAAS,GAAG;YACfL,KAAKD,SAASU,OAAOR,QAAQW,QAAQX,IAAI;YACzCW,SAASA;YACTC,UAAU;QACZ;QAEA,IAAI,CAACC,OAAO;IACd;IAEQA,UAAU;YAcV,wCAAA,mCAKJ,6BACA,yCAAA;QAnBF,MAAMC,wNAAOlB,sBAAAA,EAAoB,IAAI,CAACQ,SAAS,CAACL,GAAG,CAACgB,QAAQ,EAAE;YAC5DC,YAAY,IAAI,CAACZ,SAAS,CAACO,OAAO,CAACK,UAAU;YAC7CC,WAAW,CAACC,QAAQC,GAAG,CAACC,kCAAkC;YAC1DC,cAAc,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY;QACnD;QAEA,MAAMC,WAAW3B,4LAAAA,EACf,IAAI,CAACS,SAAS,CAACL,GAAG,EAClB,IAAI,CAACK,SAAS,CAACO,OAAO,CAACY,OAAO;QAEhC,IAAI,CAACnB,SAAS,CAACoB,YAAY,GAAG,IAAI,CAACpB,SAAS,CAACO,OAAO,CAACU,YAAY,GAC7D,IAAI,CAACjB,SAAS,CAACO,OAAO,CAACU,YAAY,CAAC5B,kBAAkB,CAAC6B,6MACvD7B,qBAAAA,EAAAA,CACE,oCAAA,IAAI,CAACW,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCC,OAAO,EAChDJ;QAGN,MAAMK,gBACJ,CAAA,CAAA,8BAAA,IAAI,CAACvB,SAAS,CAACoB,YAAY,KAAA,OAAA,KAAA,IAA3B,4BAA6BG,aAAa,KAAA,CAAA,CAC1C,qCAAA,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,0CAAA,mCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,wCAAyCE,aAAa;QAExD,IAAI,CAACvB,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAGD,KAAKC,QAAQ;QAC3C,IAAI,CAACX,SAAS,CAACuB,aAAa,GAAGA;QAC/B,IAAI,CAACvB,SAAS,CAACQ,QAAQ,GAAGE,KAAKF,QAAQ,IAAI;QAC3C,IAAI,CAACR,SAAS,CAACwB,OAAO,GAAGd,KAAKc,OAAO;QACrC,IAAI,CAACxB,SAAS,CAACyB,MAAM,GAAGf,KAAKe,MAAM,IAAIF;QACvC,IAAI,CAACvB,SAAS,CAAC0B,aAAa,GAAGhB,KAAKgB,aAAa;IACnD;IAEQC,iBAAiB;QACvB,2NAAOrC,yBAAAA,EAAuB;YAC5BkB,UAAU,IAAI,CAACR,SAAS,CAACQ,QAAQ;YACjCgB,SAAS,IAAI,CAACxB,SAAS,CAACwB,OAAO;YAC/BD,eAAe,CAAC,IAAI,CAACvB,SAAS,CAACO,OAAO,CAACqB,WAAW,GAC9C,IAAI,CAAC5B,SAAS,CAACuB,aAAa,GAC5BM;YACJJ,QAAQ,IAAI,CAACzB,SAAS,CAACyB,MAAM;YAC7Bd,UAAU,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;YACrCe,eAAe,IAAI,CAAC1B,SAAS,CAAC0B,aAAa;QAC7C;IACF;IAEQI,eAAe;QACrB,OAAO,IAAI,CAAC9B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAWP,UAAU;QACnB,OAAO,IAAI,CAACxB,SAAS,CAACwB,OAAO;IAC/B;IAEA,IAAWA,QAAQA,OAA2B,EAAE;QAC9C,IAAI,CAACxB,SAAS,CAACwB,OAAO,GAAGA;IAC3B;IAEA,IAAWC,SAAS;QAClB,OAAO,IAAI,CAACzB,SAAS,CAACyB,MAAM,IAAI;IAClC;IAEA,IAAWA,OAAOA,MAAc,EAAE;YAG7B,wCAAA;QAFH,IACE,CAAC,IAAI,CAACzB,SAAS,CAACyB,MAAM,IACtB,CAAA,CAAA,CAAC,oCAAA,IAAI,CAACzB,SAAS,CAACO,OAAO,CAACK,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCS,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCW,OAAO,CAACC,QAAQ,CAACR,OAAAA,GAC3D;YACA,MAAM,OAAA,cAEL,CAFK,IAAIS,UACR,CAAC,8CAA8C,EAAET,OAAO,CAAC,CAAC,GADtD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACzB,SAAS,CAACyB,MAAM,GAAGA;IAC1B;IAEA,IAAIF,gBAAgB;QAClB,OAAO,IAAI,CAACvB,SAAS,CAACuB,aAAa;IACrC;IAEA,IAAIH,eAAe;QACjB,OAAO,IAAI,CAACpB,SAAS,CAACoB,YAAY;IACpC;IAEA,IAAIe,eAAe;QACjB,OAAO,IAAI,CAACnC,SAAS,CAACL,GAAG,CAACwC,YAAY;IACxC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACpC,SAAS,CAACL,GAAG,CAACyC,IAAI;IAChC;IAEA,IAAIA,KAAKC,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACyC,IAAI,GAAGC;IAC5B;IAEA,IAAInB,WAAW;QACb,OAAO,IAAI,CAAClB,SAAS,CAACL,GAAG,CAACuB,QAAQ;IACpC;IAEA,IAAIA,SAASmB,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACuB,QAAQ,GAAGmB;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACtC,SAAS,CAACL,GAAG,CAAC2C,IAAI;IAChC;IAEA,IAAIA,KAAKD,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC2C,IAAI,GAAGD;IAC5B;IAEA,IAAIE,WAAW;QACb,OAAO,IAAI,CAACvC,SAAS,CAACL,GAAG,CAAC4C,QAAQ;IACpC;IAEA,IAAIA,SAASF,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC4C,QAAQ,GAAGF;IAChC;IAEA,IAAIG,OAAO;QACT,MAAM7B,WAAW,IAAI,CAACgB,cAAc;QACpC,MAAMI,SAAS,IAAI,CAACD,YAAY;QAChC,OAAO,GAAG,IAAI,CAACS,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,GAAGzB,WAAWoB,SAAS,IAAI,CAACU,IAAI,EAAE;IACzE;IAEA,IAAID,KAAK7C,GAAW,EAAE;QACpB,IAAI,CAACK,SAAS,CAACL,GAAG,GAAGD,SAASC;QAC9B,IAAI,CAACc,OAAO;IACd;IAEA,IAAIiC,SAAS;QACX,OAAO,IAAI,CAAC1C,SAAS,CAACL,GAAG,CAAC+C,MAAM;IAClC;IAEA,IAAI/B,WAAW;QACb,OAAO,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;IACpC;IAEA,IAAIA,SAAS0B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAG0B;IAChC;IAEA,IAAII,OAAO;QACT,OAAO,IAAI,CAACzC,SAAS,CAACL,GAAG,CAAC8C,IAAI;IAChC;IAEA,IAAIA,KAAKJ,KAAa,EAAE;QACtB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAAC8C,IAAI,GAAGJ;IAC5B;IAEA,IAAIN,SAAS;QACX,OAAO,IAAI,CAAC/B,SAAS,CAACL,GAAG,CAACoC,MAAM;IAClC;IAEA,IAAIA,OAAOM,KAAa,EAAE;QACxB,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACoC,MAAM,GAAGM;IAC9B;IAEA,IAAIM,WAAW;QACb,OAAO,IAAI,CAAC3C,SAAS,CAACL,GAAG,CAACgD,QAAQ;IACpC;IAEA,IAAIA,SAASN,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACgD,QAAQ,GAAGN;IAChC;IAEA,IAAIO,WAAW;QACb,OAAO,IAAI,CAAC5C,SAAS,CAACL,GAAG,CAACiD,QAAQ;IACpC;IAEA,IAAIA,SAASP,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACL,GAAG,CAACiD,QAAQ,GAAGP;IAChC;IAEA,IAAI7B,WAAW;QACb,OAAO,IAAI,CAACR,SAAS,CAACQ,QAAQ;IAChC;IAEA,IAAIA,SAAS6B,KAAa,EAAE;QAC1B,IAAI,CAACrC,SAAS,CAACQ,QAAQ,GAAG6B,MAAMQ,UAAU,CAAC,OAAOR,QAAQ,CAAC,CAAC,EAAEA,OAAO;IACvE;IAEAS,WAAW;QACT,OAAO,IAAI,CAACN,IAAI;IAClB;IAEAO,SAAS;QACP,OAAO,IAAI,CAACP,IAAI;IAClB;IAEA,CAACvC,OAAO+C,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,MAAM,IAAI,CAACA,IAAI;YACfE,QAAQ,IAAI,CAACA,MAAM;YACnBH,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBP,MAAM,IAAI,CAACA,IAAI;YACflB,UAAU,IAAI,CAACA,QAAQ;YACvBoB,MAAM,IAAI,CAACA,IAAI;YACf3B,UAAU,IAAI,CAACA,QAAQ;YACvBoB,QAAQ,IAAI,CAACA,MAAM;YACnBI,cAAc,IAAI,CAACA,YAAY;YAC/BM,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEAQ,QAAQ;QACN,OAAO,IAAI/C,QAAQJ,OAAO,IAAI,GAAG,IAAI,CAACE,SAAS,CAACO,OAAO;IACzD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/error.ts"], "sourcesContent": ["export class PageSignatureError extends Error {\n  constructor({ page }: { page: string }) {\n    super(`The middleware \"${page}\" accepts an async API directly with the form:\n  \n  export function middleware(request, event) {\n    return NextResponse.redirect('/new-location')\n  }\n  \n  Read more: https://nextjs.org/docs/messages/middleware-new-signature\n  `)\n  }\n}\n\nexport class RemovedPageError extends Error {\n  constructor() {\n    super(`The request.page has been deprecated in favour of \\`URLPattern\\`.\n  Read more: https://nextjs.org/docs/messages/middleware-request-page\n  `)\n  }\n}\n\nexport class RemovedUAError extends Error {\n  constructor() {\n    super(`The request.ua has been removed in favour of \\`userAgent\\` function.\n  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n  `)\n  }\n}\n"], "names": ["PageSignatureError", "Error", "constructor", "page", "RemovedPageError", "RemovedUAError"], "mappings": ";;;;;AAAO,MAAMA,2BAA2BC;IACtCC,YAAY,EAAEC,IAAI,EAAoB,CAAE;QACtC,KAAK,CAAC,CAAC,gBAAgB,EAAEA,KAAK;;;;;;;EAOhC,CAAC;IACD;AACF;AAEO,MAAMC,yBAAyBH;IACpCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF;AAEO,MAAMG,uBAAuBJ;IAClCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/spec-extension/cookies.ts"], "sourcesContent": ["export {\n  RequestCookies,\n  ResponseCookies,\n  stringifyCookie,\n} from 'next/dist/compiled/@edge-runtime/cookies'\n"], "names": ["RequestCookies", "ResponseCookies", "string<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";AAAA,SACEA,cAAc,EACdC,eAAe,EACfC,eAAe,QACV,2CAA0C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/spec-extension/request.ts"], "sourcesContent": ["import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n"], "names": ["NextURL", "toNodeOutgoingHttpHeaders", "validateURL", "RemovedUAError", "RemovedPageError", "RequestCookies", "INTERNALS", "Symbol", "NextRequest", "Request", "constructor", "input", "init", "url", "String", "process", "env", "NEXT_RUNTIME", "body", "duplex", "nextUrl", "headers", "nextConfig", "cookies", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "ua"], "mappings": ";;;;AACA,SAASA,OAAO,QAAQ,cAAa;AACrC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,WAAU;AACjE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,WAAU;AAC3D,SAASC,cAAc,QAAQ,YAAW;;;;;;AAEnC,MAAMC,YAAYC,OAAO,oBAAmB;AAO5C,MAAMC,oBAAoBC;IAO/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;4KAEnET,cAAAA,EAAYW;QAEZ,2DAA2D;QAC3D,oDAAoD;QACpD,sDAAsD;QACtD,oCAAoC;QACpC,IAAIE,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAQ;YACvC,IAAIL,KAAKM,IAAI,IAAIN,KAAKO,MAAM,KAAK,QAAQ;gBACvCP,KAAKO,MAAM,GAAG;YAChB;QACF;QAEA,IAAIR,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAEhB,MAAMQ,UAAU,0KAAIpB,UAAAA,CAAQa,KAAK;YAC/BQ,SAASpB,gMAAAA,EAA0B,IAAI,CAACoB,OAAO;YAC/CC,YAAYV,KAAKU,UAAU;QAC7B;QACA,IAAI,CAAChB,UAAU,GAAG;YAChBiB,SAAS,wLAAIlB,iBAAAA,CAAe,IAAI,CAACgB,OAAO;YACxCD;YACAP,KAAKE,QAAQC,GAAG,CAACQ,0BACbX,QAD+C,kBAE/CO,QAAQK,QAAQ;QACtB;IACF;IAEA,CAAClB,OAAOmB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLH,SAAS,IAAI,CAACA,OAAO;YACrBH,SAAS,IAAI,CAACA,OAAO;YACrBP,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCc,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7BT,SAASU,OAAOC,WAAW,CAAC,IAAI,CAACX,OAAO;YACxCY,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWjB,UAAU;QACnB,OAAO,IAAI,CAACjB,UAAU,CAACiB,OAAO;IAChC;IAEA,IAAWH,UAAU;QACnB,OAAO,IAAI,CAACd,UAAU,CAACc,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWqB,OAAO;QAChB,MAAM,oKAAIrC,mBAAAA;IACZ;IAEA;;;;GAIC,GACD,IAAWsC,KAAK;QACd,MAAM,oKAAIvC,iBAAAA;IACZ;IAEA,IAAWU,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/base-http/helpers.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './'\nimport type { NodeNextRequest, NodeNextResponse } from './node'\nimport type { WebNextRequest, WebNextResponse } from './web'\n\n/**\n * This file provides some helpers that should be used in conjunction with\n * explicit environment checks. When combined with the environment checks, it\n * will ensure that the correct typings are used as well as enable code\n * elimination.\n */\n\n/**\n * Type guard to determine if a request is a WebNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base request is a WebNextRequest.\n */\nexport const isWebNextRequest = (req: BaseNextRequest): req is WebNextRequest =>\n  process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a response is a WebNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base response is a WebNextResponse.\n */\nexport const isWebNextResponse = (\n  res: BaseNextResponse\n): res is WebNextResponse => process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a request is a NodeNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base request is a NodeNextRequest.\n */\nexport const isNodeNextRequest = (\n  req: BaseNextRequest\n): req is NodeNextRequest => process.env.NEXT_RUNTIME !== 'edge'\n\n/**\n * Type guard to determine if a response is a NodeNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base response is a NodeNextResponse.\n */\nexport const isNodeNextResponse = (\n  res: BaseNextResponse\n): res is NodeNextResponse => process.env.NEXT_RUNTIME !== 'edge'\n"], "names": ["isWebNextRequest", "req", "process", "env", "NEXT_RUNTIME", "isWebNextResponse", "res", "isNodeNextRequest", "isNodeNextResponse"], "mappings": "AAIA;;;;;CAK<PERSON>,GAED;;;;;CAKC,GACD;;;;;;AAAO,MAAMA,mBAAmB,CAACC,MAC/BC,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQ9B,MAAMC,oBAAoB,CAC/BC,MAC2BJ,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQzD,MAAMG,oBAAoB,CAC/BN,MAC2BC,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM;AAQzD,MAAMI,qBAAqB,CAChCF,MAC4BJ,QAAQC,GAAG,CAACC,YAAY,uBAAK,OAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/spec-extension/adapters/next-request.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n"], "names": ["getRequestMeta", "fromNodeOutgoingHttpHeaders", "NextRequest", "isNodeNextRequest", "isWebNextRequest", "ResponseAbortedName", "ResponseAborted", "Error", "name", "createAbortController", "response", "controller", "AbortController", "once", "writableFinished", "abort", "signalFromNodeResponse", "errored", "destroyed", "AbortSignal", "signal", "NextRequestAdapter", "fromBaseNextRequest", "request", "process", "env", "NEXT_RUNTIME", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "headers", "duplex", "aborted"], "mappings": ";;;;;;;AAKA,SAASA,cAAc,QAAQ,wBAAuB;AACtD,SAASC,2BAA2B,QAAQ,cAAa;AACzD,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,6BAA4B;;;;;AAEzE,MAAMC,sBAAsB,kBAAiB;AAC7C,MAAMC,wBAAwBC;;QAA9B,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AASO,SAASI,sBAAsBC,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIT;IACvB;IAEA,OAAOK;AACT;AAUO,SAASK,uBAAuBN,QAAkB;IACvD,MAAM,EAAEO,OAAO,EAAEC,SAAS,EAAE,GAAGR;IAC/B,IAAIO,WAAWC,WAAW;QACxB,OAAOC,YAAYJ,KAAK,CAACE,WAAW,IAAIX;IAC1C;IAEA,MAAM,EAAEc,MAAM,EAAE,GAAGX,sBAAsBC;IACzC,OAAOU;AACT;AAEO,MAAMC;IACX,OAAcC,oBACZC,OAAwB,EACxBH,MAAmB,EACN;QACb,IAEE,AADA,6DAC6D,QADQ;QAErEI,QAAQC,GAAG,CAACC,YAAY,uBAAK,yLAC7BtB,mBAAAA,EAAiBmB,UACjB;;aAEK,IACL,AACA,6DAA6D,QADQ;QAErEC,QAAQC,GAAG,CAACC,YAAY,uBAAK,WAC7BvB,kMAAAA,EAAkBoB,UAClB;YACA,OAAOF,mBAAmBO,mBAAmB,CAACL,SAASH;QACzD,OAAO;YACL,MAAM,OAAA,cAAoD,CAApD,IAAIb,MAAM,4CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D;IACF;IAEA,OAAcqB,oBACZL,OAAwB,EACxBH,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIS,OAAwB;QAC5B,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,UAAUP,QAAQM,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAON,QAAQM,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIR,QAAQQ,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,8KAAOlC,iBAAAA,EAAeuB,SAAS;YACrC,IAAI,CAACW,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,2LAAIhC,cAAAA,CAAY6B,KAAK;YAC1BD,QAAQP,QAAQO,MAAM;YACtBK,UAASlC,iMAAAA,EAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOiB,OAAO,GACd,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;IAEA,OAAcF,mBAAmBJ,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIM,OAA8B;QAClC,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,QAAQ;YACzDD,OAAON,QAAQM,IAAI;QACrB;QAEA,OAAO,2LAAI3B,cAAAA,CAAYqB,QAAQQ,GAAG,EAAE;YAClCD,QAAQP,QAAQO,MAAM;YACtBK,6KAASlC,8BAAAA,EAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB,QAAQG,QAAQA,OAAO,CAACH,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIG,QAAQA,OAAO,CAACH,MAAM,CAACiB,OAAO,GAC9B,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/client-component-renderer-logger.ts"], "sourcesContent": ["import type { AppPageModule } from './route-modules/app-page/module'\n\n// Combined load times for loading client components\nlet clientComponentLoadStart = 0\nlet clientComponentLoadTimes = 0\nlet clientComponentLoadCount = 0\n\nexport function wrapClientComponentLoader(\n  ComponentMod: AppPageModule\n): AppPageModule['__next_app__'] {\n  if (!('performance' in globalThis)) {\n    return ComponentMod.__next_app__\n  }\n\n  return {\n    require: (...args) => {\n      const startTime = performance.now()\n\n      if (clientComponentLoadStart === 0) {\n        clientComponentLoadStart = startTime\n      }\n\n      try {\n        clientComponentLoadCount += 1\n        return ComponentMod.__next_app__.require(...args)\n      } finally {\n        clientComponentLoadTimes += performance.now() - startTime\n      }\n    },\n    loadChunk: (...args) => {\n      const startTime = performance.now()\n      const result = ComponentMod.__next_app__.loadChunk(...args)\n      // Avoid wrapping `loadChunk`'s result in an extra promise in case something like React depends on its identity.\n      // We only need to know when it's settled.\n      result.finally(() => {\n        clientComponentLoadTimes += performance.now() - startTime\n      })\n      return result\n    },\n  }\n}\n\nexport function getClientComponentLoaderMetrics(\n  options: { reset?: boolean } = {}\n) {\n  const metrics =\n    clientComponentLoadStart === 0\n      ? undefined\n      : {\n          clientComponentLoadStart,\n          clientComponentLoadTimes,\n          clientComponentLoadCount,\n        }\n\n  if (options.reset) {\n    clientComponentLoadStart = 0\n    clientComponentLoadTimes = 0\n    clientComponentLoadCount = 0\n  }\n\n  return metrics\n}\n"], "names": ["clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "wrapClientComponentLoader", "ComponentMod", "globalThis", "__next_app__", "require", "args", "startTime", "performance", "now", "loadChunk", "result", "finally", "getClientComponentLoaderMetrics", "options", "metrics", "undefined", "reset"], "mappings": "AAEA,oDAAoD;;;;;AACpD,IAAIA,2BAA2B;AAC/B,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAExB,SAASC,0BACdC,YAA2B;IAE3B,IAAI,CAAE,CAAA,iBAAiBC,UAAS,GAAI;QAClC,OAAOD,aAAaE,YAAY;IAClC;IAEA,OAAO;QACLC,SAAS,CAAC,GAAGC;YACX,MAAMC,YAAYC,YAAYC,GAAG;YAEjC,IAAIX,6BAA6B,GAAG;gBAClCA,2BAA2BS;YAC7B;YAEA,IAAI;gBACFP,4BAA4B;gBAC5B,OAAOE,aAAaE,YAAY,CAACC,OAAO,IAAIC;YAC9C,SAAU;gBACRP,4BAA4BS,YAAYC,GAAG,KAAKF;YAClD;QACF;QACAG,WAAW,CAAC,GAAGJ;YACb,MAAMC,YAAYC,YAAYC,GAAG;YACjC,MAAME,SAAST,aAAaE,YAAY,CAACM,SAAS,IAAIJ;YACtD,gHAAgH;YAChH,0CAA0C;YAC1CK,OAAOC,OAAO,CAAC;gBACbb,4BAA4BS,YAAYC,GAAG,KAAKF;YAClD;YACA,OAAOI;QACT;IACF;AACF;AAEO,SAASE,gCACdC,UAA+B,CAAC,CAAC;IAEjC,MAAMC,UACJjB,6BAA6B,IACzBkB,YACA;QACElB;QACAC;QACAC;IACF;IAEN,IAAIc,QAAQG,KAAK,EAAE;QACjBnB,2BAA2B;QAC3BC,2BAA2B;QAC3BC,2BAA2B;IAC7B;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/pipe-readable.ts"], "sourcesContent": ["import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n"], "names": ["ResponseAbortedName", "createAbortController", "Detached<PERSON>romise", "getTracer", "NextNodeServerSpan", "getClientComponentLoaderMetrics", "isAbortError", "e", "name", "createWriterFromResponse", "res", "waitUntilForEnd", "started", "drained", "onDrain", "resolve", "on", "once", "off", "finished", "WritableStream", "write", "chunk", "globalThis", "process", "env", "NEXT_OTEL_PERFORMANCE_PREFIX", "metrics", "performance", "measure", "start", "clientComponentLoadStart", "end", "clientComponentLoadTimes", "flushHeaders", "trace", "startResponse", "spanName", "undefined", "ok", "flush", "promise", "err", "Error", "cause", "abort", "writableFinished", "destroy", "close", "pipeToNodeResponse", "readable", "errored", "destroyed", "controller", "writer", "pipeTo", "signal"], "mappings": ";;;;AAEA,SACEA,mBAAmB,EACnBC,qBAAqB,QAChB,6CAA4C;AACnD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,+BAA+B,QAAQ,qCAAoC;;;;;;AAE7E,SAASC,aAAaC,CAAM;IACjC,OAAOA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,MAAK,gBAAgBD,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,iNAAKR,sBAAAA;AACjD;AAEA,SAASS,yBACPC,GAAmB,EACnBC,eAAkC;IAElC,IAAIC,UAAU;IAEd,wEAAwE;IACxE,wDAAwD;IACxD,IAAIC,UAAU,wKAAIX,kBAAAA;IAClB,SAASY;QACPD,QAAQE,OAAO;IACjB;IACAL,IAAIM,EAAE,CAAC,SAASF;IAEhB,0EAA0E;IAC1E,eAAe;IACfJ,IAAIO,IAAI,CAAC,SAAS;QAChBP,IAAIQ,GAAG,CAAC,SAASJ;QACjBD,QAAQE,OAAO;IACjB;IAEA,yEAAyE;IACzE,kDAAkD;IAClD,MAAMI,WAAW,wKAAIjB,kBAAAA;IACrBQ,IAAIO,IAAI,CAAC,UAAU;QACjBE,SAASJ,OAAO;IAClB;IAEA,4DAA4D;IAC5D,OAAO,IAAIK,eAA2B;QACpCC,OAAO,OAAOC;YACZ,0EAA0E;YAC1E,wEAAwE;YACxE,0BAA0B;YAC1B,IAAI,CAACV,SAAS;gBACZA,UAAU;gBAEV,IACE,iBAAiBW,cACjBC,QAAQC,GAAG,CAACC,4BAA4B,EACxC;oBACA,MAAMC,2MAAUtB,kCAAAA;oBAChB,IAAIsB,SAAS;wBACXC,YAAYC,OAAO,CACjB,GAAGL,QAAQC,GAAG,CAACC,4BAA4B,CAAC,8BAA8B,CAAC,EAC3E;4BACEI,OAAOH,QAAQI,wBAAwB;4BACvCC,KACEL,QAAQI,wBAAwB,GAChCJ,QAAQM,wBAAwB;wBACpC;oBAEJ;gBACF;gBAEAvB,IAAIwB,YAAY;iBAChB/B,yLAAAA,IAAYgC,KAAK,8KACf/B,qBAAAA,CAAmBgC,aAAa,EAChC;oBACEC,UAAU;gBACZ,GACA,IAAMC;YAEV;YAEA,IAAI;gBACF,MAAMC,KAAK7B,IAAIW,KAAK,CAACC;gBAErB,sEAAsE;gBACtE,yDAAyD;gBACzD,IAAI,WAAWZ,OAAO,OAAOA,IAAI8B,KAAK,KAAK,YAAY;oBACrD9B,IAAI8B,KAAK;gBACX;gBAEA,qEAAqE;gBACrE,8CAA8C;gBAC9C,IAAI,CAACD,IAAI;oBACP,MAAM1B,QAAQ4B,OAAO;oBAErB,0EAA0E;oBAC1E5B,UAAU,wKAAIX,kBAAAA;gBAChB;YACF,EAAE,OAAOwC,KAAK;gBACZhC,IAAIsB,GAAG;gBACP,MAAM,OAAA,cAA8D,CAA9D,IAAIW,MAAM,qCAAqC;oBAAEC,OAAOF;gBAAI,IAA5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACAG,OAAO,CAACH;YACN,IAAIhC,IAAIoC,gBAAgB,EAAE;YAE1BpC,IAAIqC,OAAO,CAACL;QACd;QACAM,OAAO;YACL,mEAAmE;YACnE,uBAAuB;YACvB,IAAIrC,iBAAiB;gBACnB,MAAMA;YACR;YAEA,IAAID,IAAIoC,gBAAgB,EAAE;YAE1BpC,IAAIsB,GAAG;YACP,OAAOb,SAASsB,OAAO;QACzB;IACF;AACF;AAEO,eAAeQ,mBACpBC,QAAoC,EACpCxC,GAAmB,EACnBC,eAAkC;IAElC,IAAI;QACF,6DAA6D;QAC7D,MAAM,EAAEwC,OAAO,EAAEC,SAAS,EAAE,GAAG1C;QAC/B,IAAIyC,WAAWC,WAAW;QAE1B,wEAAwE;QACxE,sBAAsB;QACtB,MAAMC,aAAapD,uOAAAA,EAAsBS;QAEzC,MAAM4C,SAAS7C,yBAAyBC,KAAKC;QAE7C,MAAMuC,SAASK,MAAM,CAACD,QAAQ;YAAEE,QAAQH,WAAWG,MAAM;QAAC;IAC5D,EAAE,OAAOd,KAAU;QACjB,wDAAwD;QACxD,IAAIpC,aAAaoC,MAAM;QAEvB,MAAM,OAAA,cAAoD,CAApD,IAAIC,MAAM,2BAA2B;YAAEC,OAAOF;QAAI,IAAlD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/render-result.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  statusCode?: number\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the resume data cache is warmed up before the render. This\n   * is attached to the metadata so that it can be used during the render. When\n   * prerendering, the filled resume data cache is also attached to the metadata\n   * so that it can be used when prerendering matching fallback shells.\n   */\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n"], "names": ["chainStreams", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "isAbortError", "pipeToNodeResponse", "RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "Error", "readable", "<PERSON><PERSON><PERSON>", "from", "toUnchunkedString", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "chain", "responses", "push", "pipeTo", "writable", "preventClose", "close", "err", "abort", "res"], "mappings": ";;;AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;;;AAkEnD,MAAMC;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAsB,EAAE;QAC/C,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAIOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOnB,4NAAAA,EAAe,IAAI,CAACoB,QAAQ;QACrC;QAEA,OAAOC,OAAOC,IAAI,CAAC,IAAI,CAACb,QAAQ;IAClC;IAWOc,kBAAkBL,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,WAAOlB,wNAAAA,EAAe,IAAI,CAACmB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACX,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYW,WAAuC;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAyD,CAAzD,IAAIU,MAAM,iDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwD;QAChE;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,OAAA,cAA2D,CAA3D,IAAIU,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAIE,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YAClC,kNAAOX,mBAAAA,EAAiB,IAAI,CAACW,QAAQ;QACvC;QAEA,oEAAoE;QACpE,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YAChC,kNAAOZ,eAAAA,KAAgB,IAAI,CAACY,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACMkB,MAAMP,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAkE,CAAlE,IAAIU,MAAM,0DAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiE;QACzE;QAEA,mEAAmE;QACnE,IAAIS;QACJ,IAAI,OAAO,IAAI,CAACnB,QAAQ,KAAK,UAAU;YACrCmB,YAAY;iBAAC7B,6NAAAA,EAAiB,IAAI,CAACU,QAAQ;aAAE;QAC/C,OAAO,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YACvCmB,YAAY,IAAI,CAACnB,QAAQ;QAC3B,OAAO,IAAIY,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YACzCmB,YAAY;2NAAC9B,mBAAAA,EAAiB,IAAI,CAACW,QAAQ;aAAE;QAC/C,OAAO;YACLmB,YAAY;gBAAC,IAAI,CAACnB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCmB,UAAUC,IAAI,CAACT;QAEf,uBAAuB;QACvB,IAAI,CAACX,QAAQ,GAAGmB;IAClB;IAEA;;;;;;GAMC,GACD,MAAaE,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACX,QAAQ,CAACU,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACrB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMoB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,KAAIhC,sLAAAA,EAAagC,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASI,KAAK,CAACD;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAa/B,mBAAmBiC,GAAmB,EAAE;QACnD,8KAAMjC,qBAAAA,EAAmB,IAAI,CAACiB,QAAQ,EAAEgB,KAAK,IAAI,CAACzB,SAAS;IAC7D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/response-cache/utils.ts"], "sourcesContent": ["import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "RenderResult", "RouteKind", "fromResponseCacheEntry", "cacheEntry", "value", "kind", "PAGES", "html", "toUnchunkedString", "pageData", "headers", "status", "APP_PAGE", "postponed", "rscData", "segmentData", "toResponseCacheEntry", "response", "isMiss", "isStale", "cacheControl", "fromStatic", "routeKindToIncrementalCacheKind", "routeKind", "IMAGE", "APP_ROUTE", "Error"], "mappings": ";;;;;AAAA,SACEA,eAAe,EACfC,oBAAoB,QAKf,UAAS;AAEhB,OAAOC,kBAAkB,mBAAkB;AAC3C,SAASC,SAAS,QAAQ,gBAAe;;;;AAElC,eAAeC,uBACpBC,UAA8B;QAK1BA,mBAQIA;IAXR,OAAO;QACL,GAAGA,UAAU;QACbC,OACED,CAAAA,CAAAA,oBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,kBAAkBE,IAAI,oLAAKP,kBAAAA,CAAgBQ,KAAK,GAC5C;YACED,oLAAMP,kBAAAA,CAAgBQ,KAAK;YAC3BC,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDC,UAAUN,WAAWC,KAAK,CAACK,QAAQ;YACnCC,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;QACjC,IACAR,CAAAA,CAAAA,qBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,mBAAkBE,IAAI,oLAAKP,kBAAAA,CAAgBc,QAAQ,GACjD;YACEP,oLAAMP,kBAAAA,CAAgBc,QAAQ;YAC9BL,MAAM,MAAMJ,WAAWC,KAAK,CAACG,IAAI,CAACC,iBAAiB,CAAC;YACpDK,WAAWV,WAAWC,KAAK,CAACS,SAAS;YACrCC,SAASX,WAAWC,KAAK,CAACU,OAAO;YACjCJ,SAASP,WAAWC,KAAK,CAACM,OAAO;YACjCC,QAAQR,WAAWC,KAAK,CAACO,MAAM;YAC/BI,aAAaZ,WAAWC,KAAK,CAACW,WAAW;QAC3C,IACAZ,WAAWC,KAAK;IAC1B;AACF;AAEO,eAAeY,qBACpBC,QAA8C;QAS1CA,iBAQIA;IAfR,IAAI,CAACA,UAAU,OAAO;IAEtB,OAAO;QACLC,QAAQD,SAASC,MAAM;QACvBC,SAASF,SAASE,OAAO;QACzBC,cAAcH,SAASG,YAAY;QACnChB,OACEa,CAAAA,CAAAA,kBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,gBAAgBZ,IAAI,oLAAKP,kBAAAA,CAAgBQ,KAAK,GACzC;YACCD,oLAAMP,kBAAAA,CAAgBQ,KAAK;YAC3BC,0KAAMP,UAAAA,CAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDE,UAAUQ,SAASb,KAAK,CAACK,QAAQ;YACjCC,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;QAC/B,IACAM,CAAAA,CAAAA,mBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,iBAAgBZ,IAAI,oLAAKP,kBAAAA,CAAgBc,QAAQ,GAC9C;YACCP,oLAAMP,kBAAAA,CAAgBc,QAAQ;YAC9BL,0KAAMP,UAAAA,CAAaqB,UAAU,CAACJ,SAASb,KAAK,CAACG,IAAI;YACjDO,SAASG,SAASb,KAAK,CAACU,OAAO;YAC/BJ,SAASO,SAASb,KAAK,CAACM,OAAO;YAC/BC,QAAQM,SAASb,KAAK,CAACO,MAAM;YAC7BE,WAAWI,SAASb,KAAK,CAACS,SAAS;YACnCE,aAAaE,SAASb,KAAK,CAACW,WAAW;QACzC,IACAE,SAASb,KAAK;IACxB;AACF;AAEO,SAASkB,gCACdC,SAAoB;IAEpB,OAAQA;QACN,sKAAKtB,YAAAA,CAAUK,KAAK;YAClB,qLAAOP,uBAAAA,CAAqBO,KAAK;QACnC,sKAAKL,YAAAA,CAAUW,QAAQ;YACrB,qLAAOb,uBAAAA,CAAqBa,QAAQ;QACtC,sKAAKX,YAAAA,CAAUuB,KAAK;YAClB,qLAAOzB,uBAAAA,CAAqByB,KAAK;QACnC,sKAAKvB,YAAAA,CAAUwB,SAAS;YACtB,qLAAO1B,uBAAAA,CAAqB0B,SAAS;QACvC;YACE,MAAM,OAAA,cAA+C,CAA/C,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,WAAW,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/response-cache/index.ts"], "sourcesContent": ["import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  // we don't use minimal_mode name here as this.minimal_mode is\n  // statically replace for server runtimes but we need it to\n  // be dynamic here\n  private minimal_mode?: boolean\n\n  constructor(minimal_mode: boolean) {\n    this.minimal_mode = minimal_mode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n      waitUntil?: (prom: Promise<any>) => void\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n      waitUntil,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      (cacheKey, resolve) => {\n        const prom = (async () => {\n          // We keep the previous cache entry around to leverage when the\n          // incremental cache is disabled in minimal mode.\n          if (\n            this.minimal_mode &&\n            this.previousCacheItem?.key === cacheKey &&\n            this.previousCacheItem.expiresAt > Date.now()\n          ) {\n            return this.previousCacheItem.entry\n          }\n\n          // Coerce the kindHint into a given kind for the incremental cache.\n          const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n          let resolved = false\n          let cachedResponse: IncrementalResponseCacheEntry | null = null\n          try {\n            cachedResponse = !this.minimal_mode\n              ? await incrementalCache.get(key, {\n                  kind,\n                  isRoutePPREnabled: context.isRoutePPREnabled,\n                  isFallback,\n                })\n              : null\n\n            if (cachedResponse && !isOnDemandRevalidate) {\n              resolve(cachedResponse)\n              resolved = true\n\n              if (!cachedResponse.isStale || context.isPrefetch) {\n                // The cached value is still valid, so we don't need\n                // to update it yet.\n                return null\n              }\n            }\n\n            const cacheEntry = await responseGenerator({\n              hasResolved: resolved,\n              previousCacheEntry: cachedResponse,\n              isRevalidating: true,\n            })\n\n            // If the cache entry couldn't be generated, we don't want to cache\n            // the result.\n            if (!cacheEntry) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            const resolveValue = await fromResponseCacheEntry({\n              ...cacheEntry,\n              isMiss: !cachedResponse,\n            })\n            if (!resolveValue) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            // For on-demand revalidate wait to resolve until cache is set.\n            // Otherwise resolve now.\n            if (!isOnDemandRevalidate && !resolved) {\n              resolve(resolveValue)\n              resolved = true\n            }\n\n            // We want to persist the result only if it has a cache control value\n            // defined.\n            if (resolveValue.cacheControl) {\n              if (this.minimal_mode) {\n                this.previousCacheItem = {\n                  key: cacheKey,\n                  entry: resolveValue,\n                  expiresAt: Date.now() + 1000,\n                }\n              } else {\n                await incrementalCache.set(key, resolveValue.value, {\n                  cacheControl: resolveValue.cacheControl,\n                  isRoutePPREnabled,\n                  isFallback,\n                })\n              }\n            }\n\n            return resolveValue\n          } catch (err) {\n            // When a path is erroring we automatically re-set the existing cache\n            // with new revalidate and expire times to prevent non-stop retrying.\n            if (cachedResponse?.cacheControl) {\n              const newRevalidate = Math.min(\n                Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n                30\n              )\n\n              const newExpire =\n                cachedResponse.cacheControl.expire === undefined\n                  ? undefined\n                  : Math.max(\n                      newRevalidate + 3,\n                      cachedResponse.cacheControl.expire\n                    )\n\n              await incrementalCache.set(key, cachedResponse.value, {\n                cacheControl: { revalidate: newRevalidate, expire: newExpire },\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n\n            // While revalidating in the background we can't reject as we already\n            // resolved the cache entry so log the error here.\n            if (resolved) {\n              console.error(err)\n              return null\n            }\n\n            // We haven't resolved yet, so let's throw to indicate an error.\n            throw err\n          }\n        })()\n\n        // we need to ensure background revalidates are\n        // passed to waitUntil\n        if (waitUntil) {\n          waitUntil(prom)\n        }\n        return prom\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n"], "names": ["<PERSON><PERSON>", "scheduleOnNextTick", "fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "ResponseCache", "constructor", "minimal_mode", "batcher", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "get", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "waitUntil", "response", "batch", "cache<PERSON>ey", "resolve", "prom", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kind", "routeKind", "resolved", "cachedResponse", "isStale", "isPrefetch", "cacheEntry", "isRevalidating", "undefined", "resolveValue", "isMiss", "cacheControl", "set", "value", "err", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "error"], "mappings": ";;;AAQA,SAASA,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SACEC,sBAAsB,EACtBC,+BAA+B,EAC/BC,oBAAoB,QACf,UAAS;AAGhB,cAAc,UAAS;;;;;AAER,MAAMC;IA2BnBC,YAAYC,YAAqB,CAAE;aA1BlBC,OAAAA,2JAAUR,UAAAA,CAAQS,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,GAAGD,IAAI,CAAC,EAAEC,uBAAuB,MAAM,KAAK;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,uKAAaZ,qBAAAA;QACf;QAcE,IAAI,CAACM,YAAY,GAAGA;IACtB;IAEA,MAAaO,IACXH,GAAkB,EAClBI,iBAAoC,EACpCC,OAQC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACL,KAAK;YACR,OAAOI,kBAAkB;gBAAEE,aAAa;gBAAOC,oBAAoB;YAAK;QAC1E;QAEA,MAAM,EACJC,gBAAgB,EAChBP,uBAAuB,KAAK,EAC5BQ,aAAa,KAAK,EAClBC,oBAAoB,KAAK,EACzBC,SAAS,EACV,GAAGN;QAEJ,MAAMO,WAAW,MAAM,IAAI,CAACf,OAAO,CAACgB,KAAK,CACvC;YAAEb;YAAKC;QAAqB,GAC5B,CAACa,UAAUC;YACT,MAAMC,OAAQ,CAAA;oBAKV;gBAJF,+DAA+D;gBAC/D,iDAAiD;gBACjD,IACE,IAAI,CAACpB,YAAY,IACjB,CAAA,CAAA,0BAAA,IAAI,CAACqB,iBAAiB,KAAA,OAAA,KAAA,IAAtB,wBAAwBjB,GAAG,MAAKc,YAChC,IAAI,CAACG,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;oBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;gBACrC;gBAEA,mEAAmE;gBACnE,MAAMC,wLAAO9B,mCAAAA,EAAgCa,QAAQkB,SAAS;gBAE9D,IAAIC,WAAW;gBACf,IAAIC,iBAAuD;gBAC3D,IAAI;oBACFA,iBAAiB,CAAC,IAAI,CAAC7B,YAAY,GAC/B,MAAMY,iBAAiBL,GAAG,CAACH,KAAK;wBAC9BsB;wBACAZ,mBAAmBL,QAAQK,iBAAiB;wBAC5CD;oBACF,KACA;oBAEJ,IAAIgB,kBAAkB,CAACxB,sBAAsB;wBAC3Cc,QAAQU;wBACRD,WAAW;wBAEX,IAAI,CAACC,eAAeC,OAAO,IAAIrB,QAAQsB,UAAU,EAAE;4BACjD,oDAAoD;4BACpD,oBAAoB;4BACpB,OAAO;wBACT;oBACF;oBAEA,MAAMC,aAAa,MAAMxB,kBAAkB;wBACzCE,aAAakB;wBACbjB,oBAAoBkB;wBACpBI,gBAAgB;oBAClB;oBAEA,mEAAmE;oBACnE,cAAc;oBACd,IAAI,CAACD,YAAY;wBACf,+CAA+C;wBAC/C,IAAI,IAAI,CAAChC,YAAY,EAAE,IAAI,CAACqB,iBAAiB,GAAGa;wBAChD,OAAO;oBACT;oBAEA,MAAMC,eAAe,MAAMxC,2MAAAA,EAAuB;wBAChD,GAAGqC,UAAU;wBACbI,QAAQ,CAACP;oBACX;oBACA,IAAI,CAACM,cAAc;wBACjB,+CAA+C;wBAC/C,IAAI,IAAI,CAACnC,YAAY,EAAE,IAAI,CAACqB,iBAAiB,GAAGa;wBAChD,OAAO;oBACT;oBAEA,+DAA+D;oBAC/D,yBAAyB;oBACzB,IAAI,CAAC7B,wBAAwB,CAACuB,UAAU;wBACtCT,QAAQgB;wBACRP,WAAW;oBACb;oBAEA,qEAAqE;oBACrE,WAAW;oBACX,IAAIO,aAAaE,YAAY,EAAE;wBAC7B,IAAI,IAAI,CAACrC,YAAY,EAAE;4BACrB,IAAI,CAACqB,iBAAiB,GAAG;gCACvBjB,KAAKc;gCACLO,OAAOU;gCACPb,WAAWC,KAAKC,GAAG,KAAK;4BAC1B;wBACF,OAAO;4BACL,MAAMZ,iBAAiB0B,GAAG,CAAClC,KAAK+B,aAAaI,KAAK,EAAE;gCAClDF,cAAcF,aAAaE,YAAY;gCACvCvB;gCACAD;4BACF;wBACF;oBACF;oBAEA,OAAOsB;gBACT,EAAE,OAAOK,KAAK;oBACZ,qEAAqE;oBACrE,qEAAqE;oBACrE,IAAIX,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBQ,YAAY,EAAE;wBAChC,MAAMI,gBAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAACf,eAAeQ,YAAY,CAACQ,UAAU,IAAI,GAAG,IACtD;wBAGF,MAAMC,YACJjB,eAAeQ,YAAY,CAACU,MAAM,KAAKb,YACnCA,YACAQ,KAAKE,GAAG,CACNH,gBAAgB,GAChBZ,eAAeQ,YAAY,CAACU,MAAM;wBAG1C,MAAMnC,iBAAiB0B,GAAG,CAAClC,KAAKyB,eAAeU,KAAK,EAAE;4BACpDF,cAAc;gCAAEQ,YAAYJ;gCAAeM,QAAQD;4BAAU;4BAC7DhC;4BACAD;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,kDAAkD;oBAClD,IAAIe,UAAU;wBACZoB,QAAQC,KAAK,CAACT;wBACd,OAAO;oBACT;oBAEA,gEAAgE;oBAChE,MAAMA;gBACR;YACF,CAAA;YAEA,+CAA+C;YAC/C,sBAAsB;YACtB,IAAIzB,WAAW;gBACbA,UAAUK;YACZ;YACA,OAAOA;QACT;QAGF,yLAAOvB,uBAAAA,EAAqBmB;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3960, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/patch-fetch.ts"], "sourcesContent": ["import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function.\n  const patched = async function fetch(\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ): Promise<Response> {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        const originalFetchRevalidate = getNextField('revalidate')\n        let currentFetchRevalidate = originalFetchRevalidate\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            // TODO: stop accumulating tags in client prerender\n            workUnitStore.type === 'prerender-client' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          hasExplicitFetchCacheOptOut ||\n          noFetchConfigAndForceDynamic\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n\n        let autoNoCache = Boolean(\n          (hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore?.revalidate === 0\n        )\n\n        let isImplicitBuildTimeCache = false\n\n        if (!autoNoCache && hasNoExplicitCacheConfig) {\n          // We don't enable automatic no-cache behavior during build-time\n          // prerendering so that we can still leverage the fetch cache between\n          // export workers.\n          if (workStore.isBuildTimePrerendering) {\n            isImplicitBuildTimeCache = true\n          } else {\n            autoNoCache = true\n          }\n        }\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          (workUnitStore.type === 'prerender' ||\n            // While we don't want to do caching in the client scope\n            // we know the fetch will be dynamic for dynamicIO so we\n            // may as well avoid the call here\n            workUnitStore.type === 'prerender-client')\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    'fetch()'\n                  )\n                default:\n                // fallthrough\n              }\n            }\n\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `revalidate: 0 fetch ${input} ${workStore.route}`\n            )\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e.\n          // originalFetchRevalidate.\n          if (revalidateStore && originalFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock: () => Promise<void> | void = () => {}\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (\n                  workUnitStore &&\n                  (workUnitStore.type === 'prerender' ||\n                    workUnitStore.type === 'prerender-client')\n                ) {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    {\n                      fetchCache: true,\n                      fetchUrl,\n                      fetchIdx,\n                      tags,\n                      isImplicitBuildTimeCache,\n                    }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  const cacheSetPromise = cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          {\n                            fetchCache: true,\n                            fetchUrl,\n                            fetchIdx,\n                            tags,\n                            isImplicitBuildTimeCache,\n                          }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  const pendingRevalidateKey = `cache-set-${cacheKey}`\n                  workStore.pendingRevalidates ??= {}\n                  if (pendingRevalidateKey in workStore.pendingRevalidates) {\n                    // there is already a pending revalidate entry that\n                    // we need to await to avoid race conditions\n                    await workStore.pendingRevalidates[pendingRevalidateKey]\n                  }\n                  workStore.pendingRevalidates[pendingRevalidateKey] =\n                    cacheSetPromise.finally(() => {\n                      // If the pending revalidate is not present in the store, then\n                      // we have nothing to delete.\n                      if (\n                        !workStore.pendingRevalidates?.[pendingRevalidateKey]\n                      ) {\n                        return\n                      }\n\n                      delete workStore.pendingRevalidates[pendingRevalidateKey]\n                    })\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (\n                workUnitStore &&\n                (workUnitStore.type === 'prerender' ||\n                  workUnitStore.type === 'prerender-client')\n              ) {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    'fetch()'\n                  )\n                default:\n                // fallthrough\n              }\n            }\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `no-store fetch ${input} ${workStore.route}`\n            )\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore) {\n                switch (workUnitStore.type) {\n                  case 'prerender':\n                  case 'prerender-client':\n                    return makeHangingPromise<Response>(\n                      workUnitStore.renderSignal,\n                      'fetch()'\n                    )\n                  default:\n                  // fallthrough\n                }\n              }\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  // Assign the function name also as a name property, so that it's preserved\n  // even when mangling is enabled.\n  Object.defineProperty(patched, 'name', { value: 'fetch', writable: false })\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n"], "names": ["AppRenderSpan", "NextNodeServerSpan", "getTracer", "SpanKind", "CACHE_ONE_YEAR", "INFINITE_CACHE", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "markCurrentScopeAsDynamic", "makeHangingPromise", "createDedupeFetch", "CachedRouteKind", "IncrementalCacheKind", "waitAtLeastOneReactRenderTask", "cloneResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "NEXT_PATCH_SYMBOL", "Symbol", "for", "isFetchPatched", "globalThis", "validateRevalidate", "revalidateVal", "route", "normalizedRevalidate", "undefined", "isNaN", "Error", "err", "message", "includes", "validateTags", "tags", "description", "validTags", "invalidTags", "i", "length", "tag", "push", "reason", "console", "warn", "slice", "join", "log", "trackFetchMetric", "workStore", "ctx", "requestEndedState", "ended", "isDebugBuild", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "isDevelopment", "NODE_ENV", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "idx", "nextFetchId", "createPatchedFetcher", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "fetch", "input", "init", "url", "URL", "Request", "username", "password", "fetchUrl", "href", "method", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "type", "beginRead", "result", "trace", "internalFetch", "kind", "CLIENT", "spanName", "filter", "Boolean", "attributes", "hostname", "port", "getRequestMeta", "isDraftMode", "isRequestInput", "field", "value", "finalRevalidate", "getNextField", "originalFetchRevalidate", "currentFetchRevalidate", "toString", "revalidateStore", "Array", "isArray", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "get", "Headers", "hasUnCacheableHeader", "isUnCacheableMethod", "toLowerCase", "hasNoExplicitCacheConfig", "autoNoCache", "revalidate", "isImplicitBuildTimeCache", "isBuildTimePrerendering", "endRead", "renderSignal", "forceStatic", "isCacheableRevalidate", "cache<PERSON>ey", "incrementalCache", "useCacheOrRequestStore", "serverComponentsHmrCache", "generate<PERSON>ache<PERSON>ey", "error", "fetchIdx", "handleUnlock", "doOriginalFetch", "isStale", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "body", "_ogBody", "signal", "otherInput", "clonedInit", "fetchType", "then", "res", "start", "cacheStatus", "status", "bodyBuffer", "arrayBuffer", "fetchedData", "headers", "Object", "fromEntries", "entries", "<PERSON><PERSON><PERSON>", "from", "set", "FETCH", "data", "Response", "statusText", "cloned1", "cloned2", "cacheSetPromise", "catch", "finally", "pendingRevalidateKey", "pendingRevalidates", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "entry", "isOnDemandRevalidate", "softTags", "isRevalidate", "pendingRevalidate", "response", "defineProperty", "cache", "hasNextConfig", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "writable", "patchFetch", "options", "original"], "mappings": ";;;;;;;AAKA,SAASA,aAAa,EAAEC,kBAAkB,QAAQ,oBAAmB;AACrE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,iBAAgB;AACpD,SACEC,cAAc,EACdC,cAAc,EACdC,wBAAwB,EACxBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,iBAAiB,QAAQ,iBAAgB;AAElD,SACEC,eAAe,EACfC,oBAAoB,QAEf,oBAAmB;;AAC1B,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SAASC,aAAa,QAAQ,mBAAkB;;;;;;;;;;AAEhD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAU5C,MAAMC,oBAAoBC,OAAOC,GAAG,CAAC,cAAa;AAEzD,SAASC;IACP,OAAQC,UAAsC,CAACJ,kBAAkB,KAAK;AACxE;AAEO,SAASK,mBACdC,aAAsB,EACtBC,KAAa;IAEb,IAAI;QACF,IAAIC,uBAA2CC;QAE/C,IAAIH,kBAAkB,OAAO;YAC3BE,iLAAuBtB,iBAAAA;QACzB,OAAO,IACL,OAAOoB,kBAAkB,YACzB,CAACI,MAAMJ,kBACPA,gBAAgB,CAAC,GACjB;YACAE,uBAAuBF;QACzB,OAAO,IAAI,OAAOA,kBAAkB,aAAa;YAC/C,MAAM,OAAA,cAEL,CAFK,IAAIK,MACR,CAAC,0BAA0B,EAAEL,cAAc,MAAM,EAAEC,MAAM,yCAAyC,CAAC,GAD/F,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOC;IACT,EAAE,OAAOI,KAAU;QACjB,0EAA0E;QAC1E,IAAIA,eAAeD,SAASC,IAAIC,OAAO,CAACC,QAAQ,CAAC,uBAAuB;YACtE,MAAMF;QACR;QACA,OAAOH;IACT;AACF;AAEO,SAASM,aAAaC,IAAW,EAAEC,WAAmB;IAC3D,MAAMC,YAAsB,EAAE;IAC9B,MAAMC,cAGD,EAAE;IAEP,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,KAAKK,MAAM,EAAED,IAAK;QACpC,MAAME,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,OAAOE,QAAQ,UAAU;YAC3BH,YAAYI,IAAI,CAAC;gBAAED;gBAAKE,QAAQ;YAAiC;QACnE,OAAO,IAAIF,IAAID,MAAM,GAAGjC,sLAAAA,EAA2B;YACjD+B,YAAYI,IAAI,CAAC;gBACfD;gBACAE,QAAQ,CAAC,uBAAuB,4JAAEpC,4BAAAA,EAA2B;YAC/D;QACF,OAAO;YACL8B,UAAUK,IAAI,CAACD;QACjB;QAEA,IAAIJ,UAAUG,MAAM,6JAAGlC,2BAAAA,EAA0B;YAC/CsC,QAAQC,IAAI,CACV,CAAC,oCAAoC,EAAET,YAAY,eAAe,CAAC,EACnED,KAAKW,KAAK,CAACP,GAAGQ,IAAI,CAAC;YAErB;QACF;IACF;IAEA,IAAIT,YAAYE,MAAM,GAAG,GAAG;QAC1BI,QAAQC,IAAI,CAAC,CAAC,gCAAgC,EAAET,YAAY,EAAE,CAAC;QAE/D,KAAK,MAAM,EAAEK,GAAG,EAAEE,MAAM,EAAE,IAAIL,YAAa;YACzCM,QAAQI,GAAG,CAAC,CAAC,MAAM,EAAEP,IAAI,EAAE,EAAEE,QAAQ;QACvC;IACF;IACA,OAAON;AACT;AAEA,SAASY,iBACPC,SAAoB,EACpBC,GAAqC;QAIjCD;IAFJ,4EAA4E;IAC5E,IAAI,CAACA,WAAW;IAChB,IAAA,CAAIA,+BAAAA,UAAUE,iBAAiB,KAAA,OAAA,KAAA,IAA3BF,6BAA6BG,KAAK,EAAE;IAExC,MAAMC,eACH,CAAA,CAAC,CAACtC,QAAQC,GAAG,CAACsC,gBAAgB,IAC7BvC,QAAQC,GAAG,CAACuC,sBAAsB,KAAK,GAAE,KAC3CN,UAAUO,kBAAkB;IAC9B,MAAMC,gBAAgB1C,QAAQC,GAAG,CAAC0C,QAAQ,gCAAK;IAE/C,IACE,8EAA8E;;IAQhFT,UAAUU,YAAY,KAAK,EAAE;IAE7BV,UAAUU,YAAY,CAAClB,IAAI,CAAC;QAC1B,GAAGS,GAAG;QACNU,KAAKC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAC7CC,KAAKf,UAAUgB,WAAW,IAAI;IAChC;AACF;AAOO,SAASC,qBACdC,WAAoB,EACpB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAmB;IAE3D,qCAAqC;IACrC,MAAMC,UAAU,eAAeC,MAC7BC,KAAwB,EACxBC,IAA6B;YAYdA,cAIKA;QAdpB,IAAIC;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIH,iBAAiBI,UAAUJ,MAAME,GAAG,GAAGF;YACrDE,IAAIG,QAAQ,GAAG;YACfH,IAAII,QAAQ,GAAG;QACjB,EAAE,OAAM;YACN,kEAAkE;YAClEJ,MAAM/C;QACR;QACA,MAAMoD,WAAWL,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKM,IAAI,KAAI;QAC9B,MAAMC,SAASR,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,eAAAA,KAAMQ,MAAM,KAAA,OAAA,KAAA,IAAZR,aAAcS,WAAW,EAAA,KAAM;QAE9C,yDAAyD;QACzD,oBAAoB;QACpB,MAAMC,aAAa,CAACV,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,aAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,WAAoBY,QAAQ,MAAK;QACrD,MAAMC,WAAWvE,QAAQC,GAAG,CAACuE,wBAAwB,KAAK;QAC1D,oDAAoD;QACpD,2EAA2E;QAC3E,mEAAmE;QACnE,6DAA6D;QAC7D,MAAMC,aAAiCL,aACnCxD,YACAkC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAE5C,MAAMd,YAAYmB,iBAAiBqB,QAAQ;QAC3C,MAAMC,gBAAgBrB,qBAAqBoB,QAAQ;QAEnD,sFAAsF;QACtF,IAAIE,cACFD,iBAAiBA,cAAcE,IAAI,KAAK,cACpCF,cAAcC,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QAEA,MAAMC,uLAAS7F,YAAAA,IAAY8F,KAAK,CAC9BZ,yLAAanF,sBAAAA,CAAmBgG,aAAa,gLAAGjG,gBAAAA,CAAcwE,KAAK,EACnE;YACEe;YACAW,gLAAM/F,WAAAA,CAASgG,MAAM;YACrBC,UAAU;gBAAC;gBAASlB;gBAAQF;aAAS,CAACqB,MAAM,CAACC,SAASvD,IAAI,CAAC;YAC3DwD,YAAY;gBACV,YAAYvB;gBACZ,eAAeE;gBACf,eAAe,EAAEP,OAAAA,OAAAA,KAAAA,IAAAA,IAAK6B,QAAQ;gBAC9B,iBAAiB7B,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAK8B,IAAI,KAAI7E;YAChC;QACF,GACA;gBAkKI8E;YAjKF,wEAAwE;YACxE,IAAItB,YAAY;gBACd,OAAOhB,YAAYK,OAAOC;YAC5B;YAEA,qDAAqD;YACrD,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,CAACxB,WAAW;gBACd,OAAOkB,YAAYK,OAAOC;YAC5B;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,IAAIxB,UAAUyD,WAAW,EAAE;gBACzB,OAAOvC,YAAYK,OAAOC;YAC5B;YAEA,MAAMkC,iBACJnC,SACA,OAAOA,UAAU,YACjB,OAAQA,MAAkBS,MAAM,KAAK;YAEvC,MAAMwB,iBAAiB,CAACG;gBACtB,0EAA0E;gBAC1E,MAAMC,QAASpC,QAAAA,OAAAA,KAAAA,IAAAA,IAAc,CAACmC,MAAM;gBACpC,OAAOC,SAAUF,CAAAA,iBAAkBnC,KAAa,CAACoC,MAAM,GAAG,IAAG;YAC/D;YAEA,IAAIE,kBAAsCnF;YAC1C,MAAMoF,eAAe,CAACH;oBACNnC,YACVA,aAEE;gBAHN,OAAO,OAAA,CAAOA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,aAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,UAAY,CAACmC,MAAM,MAAK,cAClCnC,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,cAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,WAAY,CAACmC,MAAM,GACnBD,iBAAAA,CACE,cAACnC,MAAcY,IAAI,KAAA,OAAA,KAAA,IAAnB,WAAqB,CAACwB,MAAM,GAC5BjF;YACR;YACA,0DAA0D;YAC1D,0CAA0C;YAC1C,MAAMqF,0BAA0BD,aAAa;YAC7C,IAAIE,yBAAyBD;YAC7B,MAAM9E,OAAiBD,aACrB8E,aAAa,WAAW,EAAE,EAC1B,CAAC,MAAM,EAAEvC,MAAM0C,QAAQ,IAAI;YAG7B,MAAMC,kBACJzB,iBACCA,CAAAA,cAAcE,IAAI,KAAK,WACtBF,cAAcE,IAAI,KAAK,eACvB,mDAAmD;YACnDF,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,kBAAiB,IACtCF,gBACA/D;YAEN,IAAIwF,iBAAiB;gBACnB,IAAIC,MAAMC,OAAO,CAACnF,OAAO;oBACvB,wDAAwD;oBACxD,MAAMoF,gBACJH,gBAAgBjF,IAAI,IAAKiF,CAAAA,gBAAgBjF,IAAI,GAAG,EAAC;oBACnD,KAAK,MAAMM,OAAON,KAAM;wBACtB,IAAI,CAACoF,cAActF,QAAQ,CAACQ,MAAM;4BAChC8E,cAAc7E,IAAI,CAACD;wBACrB;oBACF;gBACF;YACF;YAEA,MAAM+E,eAAe7B,iBAAAA,OAAAA,KAAAA,IAAAA,cAAe6B,YAAY;YAEhD,sEAAsE;YACtE,QAAQ;YACR,MAAMC,qBACJ9B,iBAAiBA,cAAcE,IAAI,KAAK,mBACpC,mBACA3C,UAAUwE,UAAU;YAE1B,MAAMC,iBAAiB,CAAC,CAACzE,UAAU0E,iBAAiB;YAEpD,IAAIC,0BAA0BnB,eAAe;YAC7C,IAAIoB,cAAc;YAClB,IAAIC;YAEJ,IACE,OAAOF,4BAA4B,YACnC,OAAOX,2BAA2B,aAClC;gBACA,oHAAoH;gBACpH,MAAMc,0BAEJ,AADA,AACCH,4BAA4B,WADU,MAErCX,2BAA2B,KAC7B,0DAA0D;gBACzDW,4BAA4B,cAC1BX,CAAAA,yBAAyB,KAAKA,2BAA2B,KAAI;gBAElE,IAAIc,yBAAyB;oBAC3BD,eAAe,CAAC,kBAAkB,EAAEF,wBAAwB,mBAAmB,EAAEX,uBAAuB,gCAAgC,CAAC;oBACzIW,0BAA0BjG;oBAC1BsF,yBAAyBtF;gBAC3B;YACF;YAEA,MAAMqG,8BACJ,AACAJ,4BAA4B,cAC5BA,CAF2C,2BAEf,cAC5B,6FAA6F;YAC7F,gFAAgF;YAChFJ,uBAAuB,oBACvBA,uBAAuB;YAEzB,gFAAgF;YAChF,+EAA+E;YAC/E,sFAAsF;YACtF,wFAAwF;YACxF,wBAAwB;YACxB,MAAMS,+BACJ,CAACT,sBACD,CAACI,2BACD,CAACX,0BACDhE,UAAUiF,YAAY;YAExB,IACE,AACA,gDAAgD,6CAD6C;YAE7FN,4BAA4B,iBAC5B,OAAOX,2BAA2B,aAClC;gBACAA,yBAAyB;YAC3B,OAAO,IACLe,+BACAC,8BACA;gBACAhB,yBAAyB;YAC3B;YAEA,IACEW,4BAA4B,cAC5BA,4BAA4B,YAC5B;gBACAC,cAAc,CAAC,OAAO,EAAED,yBAAyB;YACnD;YAEAd,kBAAkBvF,mBAChB0F,wBACAhE,UAAUxB,KAAK;YAGjB,MAAM0G,WAAW1B,eAAe;YAChC,MAAM2B,cACJ,OAAA,CAAOD,YAAAA,OAAAA,KAAAA,IAAAA,SAAUE,GAAG,MAAK,aACrBF,WACA,IAAIG,QAAQH,YAAY,CAAC;YAE/B,MAAMI,uBACJH,YAAYC,GAAG,CAAC,oBAAoBD,YAAYC,GAAG,CAAC;YAEtD,MAAMG,sBAAsB,CAAC;gBAAC;gBAAO;aAAO,CAACxG,QAAQ,CACnDyE,CAAAA,CAAAA,kBAAAA,eAAe,SAAA,KAAA,OAAA,KAAA,IAAfA,gBAA0BgC,WAAW,EAAA,KAAM;YAG7C;;;;;;;;;SASC,GACD,MAAMC,2BACJ,AACAlB,sBAAsB7F,YADY,CAElC,kCAAkC;YACjCiG,CAAAA,2BAA2BjG,aAC1B,+EAA+E;YAC/E,yEAAyE;YACzEiG,4BAA4B,SAAQ,KACtC,kCAAkC;YAClCX,0BAA0BtF;YAE5B,IAAIgH,cAActC,QACfkC,CAAAA,wBAAwBC,mBAAkB,KACzCrB,CAAAA,mBAAAA,OAAAA,KAAAA,IAAAA,gBAAiByB,UAAU,MAAK;YAGpC,IAAIC,2BAA2B;YAE/B,IAAI,CAACF,eAAeD,0BAA0B;gBAC5C,gEAAgE;gBAChE,qEAAqE;gBACrE,kBAAkB;gBAClB,IAAIzF,UAAU6F,uBAAuB,EAAE;oBACrCD,2BAA2B;gBAC7B,OAAO;oBACLF,cAAc;gBAChB;YACF;YAEA,IACED,4BACAhD,kBAAkB/D,aACjB+D,CAAAA,cAAcE,IAAI,KAAK,eACtB,wDAAwD;YACxD,wDAAwD;YACxD,kCAAkC;YAClCF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;gBACA,8FAA8F;gBAC9F,4CAA4C;gBAC5C,IAAID,aAAa;oBACfA,YAAYoD,OAAO;oBACnBpD,cAAc;gBAChB;gBACA,4LAAOnF,qBAAAA,EACLkF,cAAcsD,YAAY,EAC1B;YAEJ;YAEA,OAAQxB;gBACN,KAAK;oBAAkB;wBACrBK,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAiB;wBACpB,IACED,4BAA4B,iBAC3B,OAAOd,oBAAoB,eAAeA,kBAAkB,GAC7D;4BACA,MAAM,OAAA,cAEL,CAFK,IAAIjF,MACR,CAAC,uCAAuC,EAAEkD,SAAS,gDAAgD,CAAC,GADhG,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA8C,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAc;wBACjB,IAAID,4BAA4B,YAAY;4BAC1C,MAAM,OAAA,cAEL,CAFK,IAAI/F,MACR,CAAC,oCAAoC,EAAEkD,SAAS,6CAA6C,CAAC,GAD1F,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAe;wBAClB,IACE,OAAOkC,2BAA2B,eAClCA,2BAA2B,GAC3B;4BACAY,cAAc;4BACdf,kBAAkB1G,2KAAAA;wBACpB;wBACA;oBACF;gBACA;YAKF;YAEA,IAAI,OAAO0G,oBAAoB,aAAa;gBAC1C,IAAIU,uBAAuB,mBAAmB,CAACE,gBAAgB;oBAC7DZ,4KAAkB1G,iBAAAA;oBAClByH,cAAc;gBAChB,OAAO,IAAIL,uBAAuB,oBAAoB;oBACpDV,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIH,gBAAgB;oBACzBZ,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIc,aAAa;oBACtB7B,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO;oBACL,mDAAmD;oBACnDA,cAAc;oBACdf,kBAAkBK,kBACdA,gBAAgByB,UAAU,6JAC1BxI,iBAAAA;gBACN;YACF,OAAO,IAAI,CAACyH,aAAa;gBACvBA,cAAc,CAAC,YAAY,EAAEf,iBAAiB;YAChD;YAEA,IACE,AACA,yBAAyB,4BAD4B;YAErD,CAAE7D,CAAAA,UAAUgG,WAAW,IAAInC,oBAAoB,CAAA,KAC/C,6DAA6D;YAC7D,CAAC6B,eACD,mEAAmE;YACnE,qEAAqE;YACrE,SAAS;YACTxB,mBACAL,kBAAkBK,gBAAgByB,UAAU,EAC5C;gBACA,iEAAiE;gBACjE,0BAA0B;gBAC1B,IAAI9B,oBAAoB,GAAG;oBACzB,IAAIpB,eAAe;wBACjB,OAAQA,cAAcE,IAAI;4BACxB,KAAK;4BACL,KAAK;gCACH,IAAID,aAAa;oCACfA,YAAYoD,OAAO;oCACnBpD,cAAc;gCAChB;gCACA,4LAAOnF,qBAAAA,EACLkF,cAAcsD,YAAY,EAC1B;4BAEJ;wBAEF;oBACF;oBAEAzI,yNAAAA,EACE0C,WACAyC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEvB,UAAUxB,KAAK,EAAE;gBAErD;gBAEA,mEAAmE;gBACnE,8CAA8C;gBAC9C,2BAA2B;gBAC3B,IAAI0F,mBAAmBH,4BAA4BF,iBAAiB;oBAClEK,gBAAgByB,UAAU,GAAG9B;gBAC/B;YACF;YAEA,MAAMoC,wBACJ,OAAOpC,oBAAoB,YAAYA,kBAAkB;YAE3D,IAAIqC;YACJ,MAAM,EAAEC,gBAAgB,EAAE,GAAGnG;YAE7B,MAAMoG,yBACJ3D,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeE,IAAI,MAAK,aAAaF,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeE,IAAI,MAAK,UACzDF,gBACA/D;YAEN,IACEyH,oBACCF,CAAAA,yBAAAA,CACCG,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;gBACA,IAAI;oBACFH,WAAW,MAAMC,iBAAiBG,gBAAgB,CAChDxE,UACA4B,iBAAkBnC,QAAwBC;gBAE9C,EAAE,OAAO3C,KAAK;oBACZa,QAAQ6G,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEhF;gBACpD;YACF;YAEA,MAAMiF,WAAWxG,UAAUgB,WAAW,IAAI;YAC1ChB,UAAUgB,WAAW,GAAGwF,WAAW;YAEnC,IAAIC,eAA2C,KAAO;YAEtD,MAAMC,kBAAkB,OACtBC,SACAC;gBAEA,MAAMC,qBAAqB;oBACzB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,8CAA8C;uBAC1CF,UAAU,EAAE,GAAG;wBAAC;qBAAS;iBAC9B;gBAED,IAAIjD,gBAAgB;oBAClB,MAAMoD,WAAoBvF;oBAC1B,MAAMwF,aAA0B;wBAC9BC,MAAOF,SAAiBG,OAAO,IAAIH,SAASE,IAAI;oBAClD;oBAEA,KAAK,MAAMrD,SAASkD,mBAAoB;wBACtC,iCAAiC;wBACjCE,UAAU,CAACpD,MAAM,GAAGmD,QAAQ,CAACnD,MAAM;oBACrC;oBACApC,QAAQ,IAAII,QAAQmF,SAASrF,GAAG,EAAEsF;gBACpC,OAAO,IAAIvF,MAAM;oBACf,MAAM,EAAEyF,OAAO,EAAED,IAAI,EAAEE,MAAM,EAAE,GAAGC,YAAY,GAC5C3F;oBACFA,OAAO;wBACL,GAAG2F,UAAU;wBACbH,MAAMC,WAAWD;wBACjBE,QAAQP,UAAUjI,YAAYwI;oBAChC;gBACF;gBAEA,oDAAoD;gBACpD,MAAME,aAAa;oBACjB,GAAG5F,IAAI;oBACPW,MAAM;2BAAKX,QAAAA,OAAAA,KAAAA,IAAAA,KAAMW,IAAT;wBAAekF,WAAW;wBAAUb;oBAAS;gBACvD;gBAEA,OAAOtF,YAAYK,OAAO6F,YACvBE,IAAI,CAAC,OAAOC;oBACX,IAAI,CAACZ,WAAWpE,YAAY;wBAC1BxC,iBAAiBC,WAAW;4BAC1BwH,OAAOjF;4BACPd,KAAKK;4BACL8C,aAAagC,uBAAuBhC;4BACpC6C,aACE5D,oBAAoB,KAAK+C,sBACrB,SACA;4BACN/B;4BACA6C,QAAQH,IAAIG,MAAM;4BAClB1F,QAAQoF,WAAWpF,MAAM,IAAI;wBAC/B;oBACF;oBACA,IACEuF,IAAIG,MAAM,KAAK,OACfvB,oBACAD,YACCD,CAAAA,yBAAAA,CACCG,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;wBACA,MAAM5H,uBACJoF,6KAAmB1G,iBAAAA,6JACfD,iBAAAA,GACA2G;wBAEN,IACEpB,iBACCA,CAAAA,cAAcE,IAAI,KAAK,eACtBF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;4BACA,oFAAoF;4BACpF,wEAAwE;4BACxE,MAAMgF,aAAa,MAAMJ,IAAIK,WAAW;4BAExC,MAAMC,cAAc;gCAClBC,SAASC,OAAOC,WAAW,CAACT,IAAIO,OAAO,CAACG,OAAO;gCAC/CjB,MAAMkB,OAAOC,IAAI,CAACR,YAAY1D,QAAQ,CAAC;gCACvCyD,QAAQH,IAAIG,MAAM;gCAClBjG,KAAK8F,IAAI9F,GAAG;4BACd;4BAEA,yEAAyE;4BACzE,YAAY;4BAEZ,MAAM0E,iBAAiBiC,GAAG,CACxBlC,UACA;gCACElD,oLAAMvF,kBAAAA,CAAgB4K,KAAK;gCAC3BC,MAAMT;gCACNlC,YAAYlH;4BACd,GACA;gCACE+F,YAAY;gCACZ1C;gCACA0E;gCACAvH;gCACA2G;4BACF;4BAEF,MAAMa;4BAEN,0CAA0C;4BAC1C,OAAO,IAAI8B,SAASZ,YAAY;gCAC9BG,SAASP,IAAIO,OAAO;gCACpBJ,QAAQH,IAAIG,MAAM;gCAClBc,YAAYjB,IAAIiB,UAAU;4BAC5B;wBACF,OAAO;4BACL,8DAA8D;4BAC9D,8DAA8D;4BAC9D,mDAAmD;4BACnD,+CAA+C;4BAE/C,MAAM,CAACC,SAASC,QAAQ,mLAAG9K,gBAAAA,EAAc2J;4BAEzC,qEAAqE;4BACrE,yEAAyE;4BACzE,yBAAyB;4BACzB,MAAMoB,kBAAkBF,QACrBb,WAAW,GACXN,IAAI,CAAC,OAAOM;oCAUXxB;gCATA,MAAMuB,aAAaO,OAAOC,IAAI,CAACP;gCAE/B,MAAMC,cAAc;oCAClBC,SAASC,OAAOC,WAAW,CAACS,QAAQX,OAAO,CAACG,OAAO;oCACnDjB,MAAMW,WAAW1D,QAAQ,CAAC;oCAC1ByD,QAAQe,QAAQf,MAAM;oCACtBjG,KAAKgH,QAAQhH,GAAG;gCAClB;gCAEA2E,0BAAAA,OAAAA,KAAAA,IAAAA,CAAAA,mDAAAA,uBAAwBC,wBAAwB,KAAA,OAAA,KAAA,IAAhDD,iDAAkDgC,GAAG,CACnDlC,UACA2B;gCAGF,IAAI5B,uBAAuB;oCACzB,MAAME,iBAAiBiC,GAAG,CACxBlC,UACA;wCACElD,oLAAMvF,kBAAAA,CAAgB4K,KAAK;wCAC3BC,MAAMT;wCACNlC,YAAYlH;oCACd,GACA;wCACE+F,YAAY;wCACZ1C;wCACA0E;wCACAvH;wCACA2G;oCACF;gCAEJ;4BACF,GACCgD,KAAK,CAAC,CAACrC,QACN7G,QAAQC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE4B,OAAOgF,QAElDsC,OAAO,CAACpC;4BAEX,MAAMqC,uBAAuB,CAAC,UAAU,EAAE5C,UAAU;4BACpDlG,UAAU+I,kBAAkB,KAAK,CAAC;4BAClC,IAAID,wBAAwB9I,UAAU+I,kBAAkB,EAAE;gCACxD,mDAAmD;gCACnD,4CAA4C;gCAC5C,MAAM/I,UAAU+I,kBAAkB,CAACD,qBAAqB;4BAC1D;4BACA9I,UAAU+I,kBAAkB,CAACD,qBAAqB,GAChDH,gBAAgBE,OAAO,CAAC;oCAInB7I;gCAHH,8DAA8D;gCAC9D,6BAA6B;gCAC7B,IACE,CAAA,CAAA,CAACA,gCAAAA,UAAU+I,kBAAkB,KAAA,OAAA,KAAA,IAA5B/I,6BAA8B,CAAC8I,qBAAqB,GACrD;oCACA;gCACF;gCAEA,OAAO9I,UAAU+I,kBAAkB,CAACD,qBAAqB;4BAC3D;4BAEF,OAAOJ;wBACT;oBACF;oBAEA,yEAAyE;oBACzE,4EAA4E;oBAC5E,MAAMjC;oBAEN,OAAOc;gBACT,GACCqB,KAAK,CAAC,CAACrC;oBACNE;oBACA,MAAMF;gBACR;YACJ;YAEA,IAAIK;YACJ,IAAIoC,yBAAyB;YAC7B,IAAIC,oBAAoB;YAExB,IAAI/C,YAAYC,kBAAkB;gBAChC,IAAI+C;gBAEJ,IACE9C,CAAAA,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwB+C,YAAY,KACpC/C,uBAAuBC,wBAAwB,EAC/C;oBACA6C,kBACE9C,uBAAuBC,wBAAwB,CAACjB,GAAG,CAACc;oBAEtD+C,oBAAoB;gBACtB;gBAEA,IAAIhD,yBAAyB,CAACiD,iBAAiB;oBAC7CzC,eAAe,MAAMN,iBAAiBiD,IAAI,CAAClD;oBAC3C,MAAMmD,QAAQrJ,UAAUsJ,oBAAoB,GACxC,OACA,MAAMnD,iBAAiBf,GAAG,CAACc,UAAU;wBACnClD,MAAMtF,qMAAAA,CAAqB2K,KAAK;wBAChC1C,YAAY9B;wBACZ/B;wBACA0E;wBACAvH;wBACAsK,QAAQ,EAAEjF,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcrF,IAAI;oBAC9B;oBAEJ,IAAIwG,0BAA0B;wBAC5B,yFAAyF;wBACzF,+FAA+F;wBAC/F,oDAAoD;wBACpD,IACEhD,iBACCA,CAAAA,cAAcE,IAAI,KAAK,eACtBF,cAAcE,IAAI,KAAK,kBAAiB,GAC1C;4BACA,oKAAMhF,gCAAAA;wBACR;oBACF;oBAEA,IAAI0L,OAAO;wBACT,MAAM5C;oBACR,OAAO;wBACL,4HAA4H;wBAC5HG,sBAAsB;oBACxB;oBAEA,IAAIyC,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOzF,KAAK,KAAIyF,MAAMzF,KAAK,CAACZ,IAAI,mLAAKvF,kBAAAA,CAAgB4K,KAAK,EAAE;wBAC9D,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAIrI,UAAUwJ,YAAY,IAAIH,MAAM1C,OAAO,EAAE;4BAC3CqC,yBAAyB;wBAC3B,OAAO;4BACL,IAAIK,MAAM1C,OAAO,EAAE;gCACjB3G,UAAU+I,kBAAkB,KAAK,CAAC;gCAClC,IAAI,CAAC/I,UAAU+I,kBAAkB,CAAC7C,SAAS,EAAE;oCAC3C,MAAMuD,oBAAoB/C,gBAAgB,MACvCY,IAAI,CAAC,OAAOoC,WAAc,CAAA;4CACzB1C,MAAM,MAAM0C,SAAS9B,WAAW;4CAChCE,SAAS4B,SAAS5B,OAAO;4CACzBJ,QAAQgC,SAAShC,MAAM;4CACvBc,YAAYkB,SAASlB,UAAU;wCACjC,CAAA,GACCK,OAAO,CAAC;wCACP7I,UAAU+I,kBAAkB,KAAK,CAAC;wCAClC,OAAO/I,UAAU+I,kBAAkB,CAAC7C,YAAY,GAAG;oCACrD;oCAEF,2DAA2D;oCAC3D,8BAA8B;oCAC9BuD,kBAAkBb,KAAK,CAAClJ,QAAQ6G,KAAK;oCAErCvG,UAAU+I,kBAAkB,CAAC7C,SAAS,GAAGuD;gCAC3C;4BACF;4BAEAP,kBAAkBG,MAAMzF,KAAK,CAAC0E,IAAI;wBACpC;oBACF;gBACF;gBAEA,IAAIY,iBAAiB;oBACnB,IAAI3G,YAAY;wBACdxC,iBAAiBC,WAAW;4BAC1BwH,OAAOjF;4BACPd,KAAKK;4BACL8C;4BACA6C,aAAawB,oBAAoB,QAAQ;4BACzCpE;4BACA6C,QAAQwB,gBAAgBxB,MAAM,IAAI;4BAClC1F,QAAQR,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMQ,MAAM,KAAI;wBAC1B;oBACF;oBAEA,MAAM0H,WAAW,IAAInB,SACnBL,OAAOC,IAAI,CAACe,gBAAgBlC,IAAI,EAAE,WAClC;wBACEc,SAASoB,gBAAgBpB,OAAO;wBAChCJ,QAAQwB,gBAAgBxB,MAAM;oBAChC;oBAGFK,OAAO4B,cAAc,CAACD,UAAU,OAAO;wBACrC9F,OAAOsF,gBAAgBzH,GAAG;oBAC5B;oBAEA,OAAOiI;gBACT;YACF;YAEA,IAAI1J,UAAUO,kBAAkB,IAAIiB,QAAQ,OAAOA,SAAS,UAAU;gBACpE,MAAM,EAAEoI,KAAK,EAAE,GAAGpI;gBAElB,oEAAoE;gBACpE,IAAI3D,eAAe,OAAO2D,KAAKoI,KAAK;;gBAEpC,IAAIA,UAAU,YAAY;oBACxB,uDAAuD;oBACvD,IAAInH,eAAe;wBACjB,OAAQA,cAAcE,IAAI;4BACxB,KAAK;4BACL,KAAK;gCACH,IAAID,aAAa;oCACfA,YAAYoD,OAAO;oCACnBpD,cAAc;gCAChB;gCACA,4LAAOnF,qBAAAA,EACLkF,cAAcsD,YAAY,EAC1B;4BAEJ;wBAEF;oBACF;gNACAzI,6BAAAA,EACE0C,WACAyC,eACA,CAAC,eAAe,EAAElB,MAAM,CAAC,EAAEvB,UAAUxB,KAAK,EAAE;gBAEhD;gBAEA,MAAMqL,gBAAgB,UAAUrI;gBAChC,MAAM,EAAEW,OAAO,CAAC,CAAC,EAAE,GAAGX;gBACtB,IACE,OAAOW,KAAKwD,UAAU,KAAK,YAC3BzB,mBACA/B,KAAKwD,UAAU,GAAGzB,gBAAgByB,UAAU,EAC5C;oBACA,IAAIxD,KAAKwD,UAAU,KAAK,GAAG;wBACzB,uDAAuD;wBACvD,IAAIlD,eAAe;4BACjB,OAAQA,cAAcE,IAAI;gCACxB,KAAK;gCACL,KAAK;oCACH,4LAAOpF,qBAAAA,EACLkF,cAAcsD,YAAY,EAC1B;gCAEJ;4BAEF;wBACF;wBACAzI,yNAAAA,EACE0C,WACAyC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEvB,UAAUxB,KAAK,EAAE;oBAErD;oBAEA,IAAI,CAACwB,UAAUgG,WAAW,IAAI7D,KAAKwD,UAAU,KAAK,GAAG;wBACnDzB,gBAAgByB,UAAU,GAAGxD,KAAKwD,UAAU;oBAC9C;gBACF;gBACA,IAAIkE,eAAe,OAAOrI,KAAKW,IAAI;YACrC;YAEA,kEAAkE;YAClE,6DAA6D;YAC7D,wCAAwC;YACxC,IAAI+D,YAAY8C,wBAAwB;gBACtC,MAAMF,uBAAuB5C;gBAC7BlG,UAAU+I,kBAAkB,KAAK,CAAC;gBAClC,IAAIU,oBACFzJ,UAAU+I,kBAAkB,CAACD,qBAAqB;gBAEpD,IAAIW,mBAAmB;oBACrB,MAAMK,oBAKF,MAAML;oBACV,OAAO,IAAIlB,SAASuB,kBAAkB9C,IAAI,EAAE;wBAC1Cc,SAASgC,kBAAkBhC,OAAO;wBAClCJ,QAAQoC,kBAAkBpC,MAAM;wBAChCc,YAAYsB,kBAAkBtB,UAAU;oBAC1C;gBACF;gBAEA,gEAAgE;gBAChE,sEAAsE;gBACtE,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnE,iEAAiE;gBACjE,uCAAuC;gBACvC,MAAMuB,kBAAkBrD,gBAAgB,MAAME,qBAC5C,8DAA8D;gBAC9D,8DAA8D;gBAC9D,mDAAmD;gBACnD,+CAA+C;iBAC9CU,IAAI,6KAAC1J,gBAAAA;gBAER6L,oBAAoBM,gBACjBzC,IAAI,CAAC,OAAO0C;oBACX,MAAMN,WAAWM,SAAS,CAAC,EAAE;oBAC7B,OAAO;wBACLhD,MAAM,MAAM0C,SAAS9B,WAAW;wBAChCE,SAAS4B,SAAS5B,OAAO;wBACzBJ,QAAQgC,SAAShC,MAAM;wBACvBc,YAAYkB,SAASlB,UAAU;oBACjC;gBACF,GACCK,OAAO,CAAC;wBAGF7I;oBAFL,8DAA8D;oBAC9D,6BAA6B;oBAC7B,IAAI,CAAA,CAAA,CAACA,gCAAAA,UAAU+I,kBAAkB,KAAA,OAAA,KAAA,IAA5B/I,6BAA8B,CAAC8I,qBAAqB,GAAE;wBACzD;oBACF;oBAEA,OAAO9I,UAAU+I,kBAAkB,CAACD,qBAAqB;gBAC3D;gBAEF,mEAAmE;gBACnE,qBAAqB;gBACrBW,kBAAkBb,KAAK,CAAC,KAAO;gBAE/B5I,UAAU+I,kBAAkB,CAACD,qBAAqB,GAAGW;gBAErD,OAAOM,gBAAgBzC,IAAI,CAAC,CAAC0C,YAAcA,SAAS,CAAC,EAAE;YACzD,OAAO;gBACL,OAAOtD,gBAAgB,OAAOE;YAChC;QACF;QAGF,IAAIlE,aAAa;YACf,IAAI;gBACF,OAAO,MAAMG;YACf,SAAU;gBACR,IAAIH,aAAa;oBACfA,YAAYoD,OAAO;gBACrB;YACF;QACF;QACA,OAAOjD;IACT;IAEA,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,WAAW;IACXxB,QAAQ4I,aAAa,GAAG;IACxB5I,QAAQ6I,oBAAoB,GAAG,IAAM/I;IACrCE,QAAQ8I,kBAAkB,GAAGjJ;IAC3B7C,UAAsC,CAACJ,kBAAkB,GAAG;IAE9D,2EAA2E;IAC3E,iCAAiC;IACjC8J,OAAO4B,cAAc,CAACtI,SAAS,QAAQ;QAAEuC,OAAO;QAASwG,UAAU;IAAM;IAEzE,OAAO/I;AACT;AAGO,SAASgJ,WAAWC,OAAwB;IACjD,gEAAgE;IAChE,IAAIlM,kBAAkB;IAEtB,0EAA0E;IAC1E,8BAA8B;IAC9B,MAAMmM,yLAAW/M,oBAAAA,EAAkBa,WAAWiD,KAAK;IAEnD,6CAA6C;IAC7CjD,WAAWiD,KAAK,GAAGL,qBAAqBsJ,UAAUD;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["isGroupSegment", "segment", "endsWith", "isParallelRouteSegment", "startsWith", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "includes", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;AAEO,SAASA,eAAeC,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASC,uBAAuBF,OAAe;IACpD,OAAOA,QAAQG,UAAU,CAAC,QAAQH,YAAY;AAChD;AAEO,SAASI,6BACdJ,OAAgB,EAChBK,YAA2D;IAE3D,MAAMC,gBAAgBN,QAAQO,QAAQ,CAACC;IAEvC,IAAIF,eAAe;QACjB,MAAMG,mBAAmBC,KAAKC,SAAS,CAACN;QACxC,OAAOI,qBAAqB,OACxBD,mBAAmB,MAAMC,mBACzBD;IACN;IAEA,OAAOR;AACT;AAEO,MAAMQ,mBAAmB,WAAU;AACnC,MAAMI,sBAAsB,cAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,gNAAOH,qBAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,0KAAIL,iBAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;AAAO,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/web/spec-extension/adapters/headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n"], "names": ["ReflectAdapter", "ReadonlyHeadersError", "Error", "constructor", "callable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headers", "headers", "Proxy", "get", "target", "prop", "receiver", "lowercased", "toLowerCase", "original", "Object", "keys", "find", "o", "set", "value", "has", "deleteProperty", "seal", "merge", "Array", "isArray", "join", "from", "append", "name", "existing", "push", "delete", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "call", "key", "values", "Symbol", "iterator"], "mappings": ";;;;AAEA,SAASA,cAAc,QAAQ,YAAW;;AAKnC,MAAMC,6BAA6BC;IACxCC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAUO,MAAMI,uBAAuBC;IAGlCH,YAAYI,OAA4B,CAAE;QACxC,2EAA2E;QAC3E,2EAA2E;QAC3E,KAAK;QAEL,IAAI,CAACA,OAAO,GAAG,IAAIC,MAAMD,SAAS;YAChCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,sEAAsE;gBACtE,sEAAsE;gBACtE,cAAc;gBACd,IAAI,OAAOD,SAAS,UAAU;oBAC5B,0MAAOX,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC1C;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,0DAA0D;gBAC1D,IAAI,OAAOE,aAAa,aAAa;gBAErC,mDAAmD;gBACnD,0MAAOf,iBAAAA,CAAeS,GAAG,CAACC,QAAQK,UAAUH;YAC9C;YACAQ,KAAIV,MAAM,EAAEC,IAAI,EAAEU,KAAK,EAAET,QAAQ;gBAC/B,IAAI,OAAOD,SAAS,UAAU;oBAC5B,0MAAOX,iBAAAA,CAAeoB,GAAG,CAACV,QAAQC,MAAMU,OAAOT;gBACjD;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,iEAAiE;gBACjE,0MAAOb,iBAAAA,CAAeoB,GAAG,CAACV,QAAQK,YAAYJ,MAAMU,OAAOT;YAC7D;YACAU,KAAIZ,MAAM,EAAEC,IAAI;gBACd,IAAI,OAAOA,SAAS,UAAU,0MAAOX,iBAAAA,CAAesB,GAAG,CAACZ,QAAQC;gBAEhE,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,sDAAsD;gBACtD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,8CAA8C;gBAC9C,0MAAOf,iBAAAA,CAAesB,GAAG,CAACZ,QAAQK;YACpC;YACAQ,gBAAeb,MAAM,EAAEC,IAAI;gBACzB,IAAI,OAAOA,SAAS,UAClB,0MAAOX,iBAAAA,CAAeuB,cAAc,CAACb,QAAQC;gBAE/C,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,qDAAqD;gBACrD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,sDAAsD;gBACtD,0MAAOf,iBAAAA,CAAeuB,cAAc,CAACb,QAAQK;YAC/C;QACF;IACF;IAEA;;;GAGC,GACD,OAAcS,KAAKjB,OAAgB,EAAmB;QACpD,OAAO,IAAIC,MAAuBD,SAAS;YACzCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOV,qBAAqBG,QAAQ;oBACtC;wBACE,0MAAOJ,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACOa,MAAMJ,KAAwB,EAAU;QAC9C,IAAIK,MAAMC,OAAO,CAACN,QAAQ,OAAOA,MAAMO,IAAI,CAAC;QAE5C,OAAOP;IACT;IAEA;;;;;GAKC,GACD,OAAcQ,KAAKtB,OAAsC,EAAW;QAClE,IAAIA,mBAAmBD,SAAS,OAAOC;QAEvC,OAAO,IAAIF,eAAeE;IAC5B;IAEOuB,OAAOC,IAAY,EAAEV,KAAa,EAAQ;QAC/C,MAAMW,WAAW,IAAI,CAACzB,OAAO,CAACwB,KAAK;QACnC,IAAI,OAAOC,aAAa,UAAU;YAChC,IAAI,CAACzB,OAAO,CAACwB,KAAK,GAAG;gBAACC;gBAAUX;aAAM;QACxC,OAAO,IAAIK,MAAMC,OAAO,CAACK,WAAW;YAClCA,SAASC,IAAI,CAACZ;QAChB,OAAO;YACL,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;QACvB;IACF;IAEOa,OAAOH,IAAY,EAAQ;QAChC,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK;IAC3B;IAEOtB,IAAIsB,IAAY,EAAiB;QACtC,MAAMV,QAAQ,IAAI,CAACd,OAAO,CAACwB,KAAK;QAChC,IAAI,OAAOV,UAAU,aAAa,OAAO,IAAI,CAACI,KAAK,CAACJ;QAEpD,OAAO;IACT;IAEOC,IAAIS,IAAY,EAAW;QAChC,OAAO,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK,KAAK;IACvC;IAEOX,IAAIW,IAAY,EAAEV,KAAa,EAAQ;QAC5C,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;IACvB;IAEOc,QACLC,UAAkE,EAClEC,OAAa,EACP;QACN,KAAK,MAAM,CAACN,MAAMV,MAAM,IAAI,IAAI,CAACiB,OAAO,GAAI;YAC1CF,WAAWG,IAAI,CAACF,SAAShB,OAAOU,MAAM,IAAI;QAC5C;IACF;IAEA,CAAQO,UAA6C;QACnD,KAAK,MAAME,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMO,QAAQ,IAAI,CAACZ,GAAG,CAACsB;YAEvB,MAAM;gBAACA;gBAAMV;aAAM;QACrB;IACF;IAEA,CAAQJ,OAAgC;QACtC,KAAK,MAAMuB,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,MAAMiB;QACR;IACF;IAEA,CAAQU,SAAkC;QACxC,KAAK,MAAMD,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMc,QAAQ,IAAI,CAACZ,GAAG,CAAC+B;YAEvB,MAAMnB;QACR;IACF;IAEO,CAACqB,OAAOC,QAAQ,CAAC,GAAsC;QAC5D,OAAO,IAAI,CAACL,OAAO;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/api-utils/index.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "getTracer", "NodeSpan", "wrapApiHandler", "page", "handler", "args", "setRootSpanAttribute", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "sendStatusCode", "res", "statusCode", "redirect", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "checkIsOnDemandRevalidate", "req", "previewProps", "headers", "from", "previewModeId", "get", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_PREVIEW_DATA", "Symbol", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "ApiError", "constructor", "message", "sendError", "statusMessage", "setLazyProp", "prop", "getter", "opts", "configurable", "optsReset", "writable", "set"], "mappings": ";;;;;;;;;;;;;;;AAKA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,2BAA2B,EAC3BC,0CAA0C,QACrC,sBAAqB;AAC5B,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;;;;;AAW1C,SAASC,eACdC,IAAY,EACZC,OAAU;IAEV,OAAQ,CAAC,GAAGC;sLACVL,YAAAA,IAAYM,oBAAoB,CAAC,cAAcH;QAC/C,wBAAwB;QACxB,OAAOH,0LAAAA,IAAYO,KAAK,8KACtBN,WAAAA,CAASO,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAEN,MAAM;QACjD,GACA,IAAMC,WAAWC;IAErB;AACF;AAOO,SAASK,eACdC,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASE,SACdF,GAAoB,EACpBG,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,qKAAqK,CAAC,GADnK,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAL,IAAIM,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CJ,IAAIQ,KAAK,CAACJ;IACVJ,IAAIS,GAAG;IACP,OAAOT;AACT;AAEO,SAASU,0BACdC,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,6MAAU3B,iBAAAA,CAAe4B,IAAI,CAACH,IAAIE,OAAO;IAE/C,MAAME,gBAAgBF,QAAQG,GAAG,2JAAC7B,8BAAAA;IAClC,MAAM8B,uBAAuBF,kBAAkBH,aAAaG,aAAa;IAEzE,MAAMG,0BAA0BL,QAAQM,GAAG,2JACzC/B,6CAAAA;IAGF,OAAO;QAAE6B;QAAsBC;IAAwB;AACzD;AAEO,MAAME,+BAA+B,CAAC,kBAAkB,CAAC,CAAA;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC,CAAA;AAExD,MAAMC,yBAAyB,IAAI,OAAO,KAAI;AAE9C,MAAMC,sBAAsBC,OAAOH,4BAA2B;AAC9D,MAAMI,yBAAyBD,OAAOJ,8BAA6B;AAEnE,SAASM,iBACd1B,GAAuB,EACvB2B,UAEI,CAAC,CAAC;IAEN,IAAIF,0BAA0BzB,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAE4B,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW9B,IAAI+B,SAAS,CAAC;IAC/B/B,IAAIgC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACZA,WACA,EAAE;QACRF,UAAUR,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEe,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAUP,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEc,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAAC9C,KAAKyB,wBAAwB;QACjDsB,OAAO;QACPC,YAAY;IACd;IACA,OAAOhD;AACT;AAKO,MAAMiD,iBAAiB5C;IAG5B6C,YAAYjD,UAAkB,EAAEkD,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAClD,UAAU,GAAGA;IACpB;AACF;AAQO,SAASmD,UACdpD,GAAoB,EACpBC,UAAkB,EAClBkD,OAAe;IAEfnD,IAAIC,UAAU,GAAGA;IACjBD,IAAIqD,aAAa,GAAGF;IACpBnD,IAAIS,GAAG,CAAC0C;AACV;AAYO,SAASG,YACd,EAAE3C,GAAG,EAAa,EAClB4C,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMV,YAAY;IAAK;IACpD,MAAMW,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5Cf,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;QAC/B,GAAGE,IAAI;QACPzC,KAAK;YACH,MAAM+B,QAAQS;YACd,8DAA8D;YAC9DX,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;YACvD,OAAOA;QACT;QACAc,KAAK,CAACd;YACJF,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;QACzD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/api-utils/get-cookie-parser.ts"], "sourcesContent": ["import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } =\n      require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "parse<PERSON><PERSON><PERSON>", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join"], "mappings": "AAEA;;;CAGC,GAED;;;AAAO,SAASA,gBAAgBC,OAE/B;IACC,OAAO,SAASC;QACd,MAAM,EAAEC,MAAM,EAAE,GAAGF;QAEnB,IAAI,CAACE,QAAQ;YACX,OAAO,CAAC;QACV;QAEA,MAAM,EAAEC,OAAOC,aAAa,EAAE,GAC5BC,QAAQ;QACV,OAAOD,cAAcE,MAAMC,OAAO,CAACL,UAAUA,OAAOM,IAAI,CAAC,QAAQN;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/base-http/index.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'http'\nimport type { I18NConfig } from '../config-shared'\n\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport type { NextApiRequestCookies } from '../api-utils'\nimport { getCookieParser } from '../api-utils/get-cookie-parser'\n\nexport interface BaseNextRequestConfig {\n  basePath: string | undefined\n  i18n?: I18NConfig\n  trailingSlash?: boolean | undefined\n}\n\nexport type FetchMetric = {\n  url: string\n  idx: number\n  end: number\n  start: number\n  method: string\n  status: number\n  cacheReason: string\n  cacheStatus: 'hit' | 'miss' | 'skip' | 'hmr'\n  cacheWarning?: string\n}\n\nexport type FetchMetrics = Array<FetchMetric>\n\nexport abstract class BaseNextRequest<Body = any> {\n  protected _cookies: NextApiRequestCookies | undefined\n  public abstract headers: IncomingHttpHeaders\n  public abstract fetchMetrics: FetchMetric[] | undefined\n\n  constructor(\n    public method: string,\n    public url: string,\n    public body: Body\n  ) {}\n\n  // Utils implemented using the abstract methods above\n\n  public get cookies() {\n    if (this._cookies) return this._cookies\n    return (this._cookies = getCookieParser(this.headers)())\n  }\n}\n\nexport abstract class BaseNextResponse<Destination = any> {\n  abstract statusCode: number | undefined\n  abstract statusMessage: string | undefined\n  abstract get sent(): boolean\n\n  constructor(public destination: Destination) {}\n\n  /**\n   * Sets a value for the header overwriting existing values\n   */\n  abstract setHeader(name: string, value: string | string[]): this\n\n  /**\n   * Removes a header\n   */\n  abstract removeHeader(name: string): this\n\n  /**\n   * Appends value for the given header name\n   */\n  abstract appendHeader(name: string, value: string): this\n\n  /**\n   * Get all values for a header as an array or undefined if no value is present\n   */\n  abstract getHeaderValues(name: string): string[] | undefined\n\n  abstract hasHeader(name: string): boolean\n\n  /**\n   * Get values for a header concatenated using `,` or undefined if no value is present\n   */\n  abstract getHeader(name: string): string | undefined\n\n  abstract getHeaders(): OutgoingHttpHeaders\n\n  abstract body(value: string): this\n\n  abstract send(): void\n\n  abstract onClose(callback: () => void): void\n\n  // Utils implemented using the abstract methods above\n\n  public redirect(destination: string, statusCode: number) {\n    this.setHeader('Location', destination)\n    this.statusCode = statusCode\n\n    // Since IE11 doesn't support the 308 header add backwards\n    // compatibility using refresh header\n    if (statusCode === RedirectStatusCode.PermanentRedirect) {\n      this.setHeader('Refresh', `0;url=${destination}`)\n    }\n\n    return this\n  }\n}\n"], "names": ["RedirectStatusCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "PermanentRedirect"], "mappings": ";;;;AAGA,SAASA,kBAAkB,QAAQ,+CAA8C;AAEjF,SAASC,eAAe,QAAQ,iCAAgC;;;AAsBzD,MAAeC;IAKpBC,YACSC,MAAc,EACdC,GAAW,EACXC,IAAU,CACjB;aAHOF,MAAAA,GAAAA;aACAC,GAAAA,GAAAA;aACAC,IAAAA,GAAAA;IACN;IAEH,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,kMAAGP,kBAAAA,EAAgB,IAAI,CAACQ,OAAO;IACtD;AACF;AAEO,MAAeC;IAKpBP,YAAmBQ,WAAwB,CAAE;aAA1BA,WAAAA,GAAAA;IAA2B;IAqC9C,qDAAqD;IAE9CC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QACvD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,2MAAeb,qBAAAA,CAAmBe,iBAAiB,EAAE;YACvD,IAAI,CAACD,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,aAAa;QAClD;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/base-http/node.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport type { Writable, Readable } from 'stream'\n\nimport { SYMBOL_CLEARED_COOKIES } from '../api-utils'\nimport type { NextApiRequestCookies } from '../api-utils'\n\nimport { NEXT_REQUEST_META } from '../request-meta'\nimport type { RequestMeta } from '../request-meta'\n\nimport { BaseNextRequest, BaseNextResponse, type FetchMetric } from './index'\nimport type { OutgoingHttpHeaders } from 'node:http'\n\ntype Req = IncomingMessage & {\n  [NEXT_REQUEST_META]?: RequestMeta\n  cookies?: NextApiRequestCookies\n  fetchMetrics?: FetchMetric[]\n}\n\nexport class NodeNextRequest extends BaseNextRequest<Readable> {\n  public headers = this._req.headers\n  public fetchMetrics: FetchMetric[] | undefined = this._req?.fetchMetrics;\n\n  [NEXT_REQUEST_META]: RequestMeta = this._req[NEXT_REQUEST_META] || {}\n\n  constructor(private _req: Req) {\n    super(_req.method!.toUpperCase(), _req.url!, _req)\n  }\n\n  get originalRequest() {\n    // Need to mimic these changes to the original req object for places where we use it:\n    // render.tsx, api/ssg requests\n    this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META]\n    this._req.url = this.url\n    this._req.cookies = this.cookies\n    return this._req\n  }\n\n  set originalRequest(value: Req) {\n    this._req = value\n  }\n\n  private streaming = false\n\n  /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */\n  public stream() {\n    if (this.streaming) {\n      throw new Error(\n        'Invariant: NodeNextRequest.stream() can only be called once'\n      )\n    }\n    this.streaming = true\n\n    return new ReadableStream({\n      start: (controller) => {\n        this._req.on('data', (chunk) => {\n          controller.enqueue(new Uint8Array(chunk))\n        })\n        this._req.on('end', () => {\n          controller.close()\n        })\n        this._req.on('error', (err) => {\n          controller.error(err)\n        })\n      },\n    })\n  }\n}\n\nexport class NodeNextResponse extends BaseNextResponse<Writable> {\n  private textBody: string | undefined = undefined\n\n  public [SYMBOL_CLEARED_COOKIES]?: boolean\n\n  get originalResponse() {\n    if (SYMBOL_CLEARED_COOKIES in this) {\n      this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES]\n    }\n\n    return this._res\n  }\n\n  constructor(\n    private _res: ServerResponse & { [SYMBOL_CLEARED_COOKIES]?: boolean }\n  ) {\n    super(_res)\n  }\n\n  get sent() {\n    return this._res.finished || this._res.headersSent\n  }\n\n  get statusCode() {\n    return this._res.statusCode\n  }\n\n  set statusCode(value: number) {\n    this._res.statusCode = value\n  }\n\n  get statusMessage() {\n    return this._res.statusMessage\n  }\n\n  set statusMessage(value: string) {\n    this._res.statusMessage = value\n  }\n\n  setHeader(name: string, value: string | string[]): this {\n    this._res.setHeader(name, value)\n    return this\n  }\n\n  removeHeader(name: string): this {\n    this._res.removeHeader(name)\n    return this\n  }\n\n  getHeaderValues(name: string): string[] | undefined {\n    const values = this._res.getHeader(name)\n\n    if (values === undefined) return undefined\n\n    return (Array.isArray(values) ? values : [values]).map((value) =>\n      value.toString()\n    )\n  }\n\n  hasHeader(name: string): boolean {\n    return this._res.hasHeader(name)\n  }\n\n  getHeader(name: string): string | undefined {\n    const values = this.getHeaderValues(name)\n    return Array.isArray(values) ? values.join(',') : undefined\n  }\n\n  getHeaders(): OutgoingHttpHeaders {\n    return this._res.getHeaders()\n  }\n\n  appendHeader(name: string, value: string): this {\n    const currentValues = this.getHeaderValues(name) ?? []\n\n    if (!currentValues.includes(value)) {\n      this._res.setHeader(name, [...currentValues, value])\n    }\n\n    return this\n  }\n\n  body(value: string) {\n    this.textBody = value\n    return this\n  }\n\n  send() {\n    this._res.end(this.textBody)\n  }\n\n  public onClose(callback: () => void) {\n    this.originalResponse.on('close', callback)\n  }\n}\n"], "names": ["SYMBOL_CLEARED_COOKIES", "NEXT_REQUEST_META", "BaseNextRequest", "BaseNextResponse", "NodeNextRequest", "constructor", "_req", "method", "toUpperCase", "url", "headers", "fetchMetrics", "streaming", "originalRequest", "cookies", "value", "stream", "Error", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "err", "error", "NodeNextResponse", "originalResponse", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end", "onClose", "callback"], "mappings": ";;;;AAGA,SAASA,sBAAsB,QAAQ,eAAc;AAGrD,SAASC,iBAAiB,QAAQ,kBAAiB;AAGnD,SAASC,eAAe,EAAEC,gBAAgB,QAA0B,UAAS;;;;;AAStE,MAAMC,iMAAwBF,kBAAAA;uBAIlCD,wLAAAA,oBAAAA,CAAAA;IAEDI,YAAoBC,IAAS,CAAE;YAJkB;QAK/C,KAAK,CAACA,KAAKC,MAAM,CAAEC,WAAW,IAAIF,KAAKG,GAAG,EAAGH,OAAAA,IAAAA,CAD3BA,IAAAA,GAAAA,MAAAA,IAAAA,CALbI,OAAAA,GAAU,IAAI,CAACJ,IAAI,CAACI,OAAO,EAAA,IAAA,CAC3BC,YAAAA,GAAAA,CAA0C,aAAA,IAAI,CAACL,IAAI,KAAA,OAAA,KAAA,IAAT,WAAWK,YAAY,EAAA,IAExE,CAACV,mBAAkB,GAAgB,IAAI,CAACK,IAAI,oKAACL,oBAAAA,CAAkB,IAAI,CAAC,GAAA,IAAA,CAmB5DW,SAAAA,GAAY;IAfpB;IAEA,IAAIC,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACP,IAAI,oKAACL,oBAAAA,CAAkB,GAAG,IAAI,oKAACA,oBAAAA,CAAkB;QACtD,IAAI,CAACK,IAAI,CAACG,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACH,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACR,IAAI;IAClB;IAEA,IAAIO,gBAAgBE,KAAU,EAAE;QAC9B,IAAI,CAACT,IAAI,GAAGS;IACd;IAIA;;;;;;GAMC,GACMC,SAAS;QACd,IAAI,IAAI,CAACJ,SAAS,EAAE;YAClB,MAAM,OAAA,cAEL,CAFK,IAAIK,MACR,gEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,CAACL,SAAS,GAAG;QAEjB,OAAO,IAAIM,eAAe;YACxBC,OAAO,CAACC;gBACN,IAAI,CAACd,IAAI,CAACe,EAAE,CAAC,QAAQ,CAACC;oBACpBF,WAAWG,OAAO,CAAC,IAAIC,WAAWF;gBACpC;gBACA,IAAI,CAAChB,IAAI,CAACe,EAAE,CAAC,OAAO;oBAClBD,WAAWK,KAAK;gBAClB;gBACA,IAAI,CAACnB,IAAI,CAACe,EAAE,CAAC,SAAS,CAACK;oBACrBN,WAAWO,KAAK,CAACD;gBACnB;YACF;QACF;IACF;AACF;AAEO,MAAME,kMAAyBzB,mBAAAA;IAKpC,IAAI0B,mBAAmB;QACrB,6KAAI7B,yBAAAA,IAA0B,IAAI,EAAE;YAClC,IAAI,CAAC8B,IAAI,0KAAC9B,yBAAAA,CAAuB,GAAG,IAAI,0KAACA,yBAAAA,CAAuB;QAClE;QAEA,OAAO,IAAI,CAAC8B,IAAI;IAClB;IAEAzB,YACUyB,IAA6D,CACrE;QACA,KAAK,CAACA,OAAAA,IAAAA,CAFEA,IAAAA,GAAAA,MAAAA,IAAAA,CAbFC,QAAAA,GAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWrB,KAAa,EAAE;QAC5B,IAAI,CAACe,IAAI,CAACM,UAAU,GAAGrB;IACzB;IAEA,IAAIsB,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAActB,KAAa,EAAE;QAC/B,IAAI,CAACe,IAAI,CAACO,aAAa,GAAGtB;IAC5B;IAEAuB,UAAUC,IAAY,EAAExB,KAAwB,EAAQ;QACtD,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAMxB;QAC1B,OAAO,IAAI;IACb;IAEAyB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAQY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAM,EAAGI,GAAG,CAAC,CAAC/B,QACtDA,MAAMgC,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAExB,KAAa,EAAQ;QAC9C,MAAMqC,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAACtC,QAAQ;YAClC,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAerC;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAuC,KAAKvC,KAAa,EAAE;QAClB,IAAI,CAACgB,QAAQ,GAAGhB;QAChB,OAAO,IAAI;IACb;IAEAwC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;IAEO0B,QAAQC,QAAoB,EAAE;QACnC,IAAI,CAAC7B,gBAAgB,CAACR,EAAE,CAAC,SAASqC;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/instrumentation/utils.ts"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined"], "mappings": ";;;AAAO,SAASA,oBAAoBC,MAGnC;IACC,IAAIA,OAAOC,oBAAoB,EAAE;QAC/B,OAAO;IACT;IACA,IAAID,OAAOE,YAAY,EAAE;QACvB,OAAO;IACT;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/send-response.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport { isNodeNextResponse } from './base-http/helpers'\n\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { splitCookiesString } from './web/utils'\n\n/**\n * Sends the response on the underlying next response object.\n *\n * @param req the underlying request object\n * @param res the underlying response object\n * @param response the response to send\n */\nexport async function sendResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  response: Response,\n  waitUntil?: Promise<unknown>\n): Promise<void> {\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextResponse(res)\n  ) {\n    // Copy over the response status.\n    res.statusCode = response.status\n    res.statusMessage = response.statusText\n\n    // TODO: this is not spec-compliant behavior and we should not restrict\n    // headers that are allowed to appear many times.\n    //\n    // See:\n    // https://github.com/vercel/next.js/pull/70127\n    const headersWithMultipleValuesAllowed = [\n      // can add more headers to this list if needed\n      'set-cookie',\n      'www-authenticate',\n      'proxy-authenticate',\n      'vary',\n    ]\n\n    // Copy over the response headers.\n    response.headers?.forEach((value, name) => {\n      // `x-middleware-set-cookie` is an internal header not needed for the response\n      if (name.toLowerCase() === 'x-middleware-set-cookie') {\n        return\n      }\n\n      // The append handling is special cased for `set-cookie`.\n      if (name.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          res.appendHeader(name, cookie)\n        }\n      } else {\n        // only append the header if it is either not present in the outbound response\n        // or if the header supports multiple values\n        const isHeaderPresent = typeof res.getHeader(name) !== 'undefined'\n        if (\n          headersWithMultipleValuesAllowed.includes(name.toLowerCase()) ||\n          !isHeaderPresent\n        ) {\n          res.appendHeader(name, value)\n        }\n      }\n    })\n\n    /**\n     * The response can't be directly piped to the underlying response. The\n     * following is duplicated from the edge runtime handler.\n     *\n     * See packages/next/server/next-server.ts\n     */\n\n    const { originalResponse } = res\n\n    // A response body must not be sent for HEAD requests. See https://httpwg.org/specs/rfc9110.html#HEAD\n    if (response.body && req.method !== 'HEAD') {\n      await pipeToNodeResponse(response.body, originalResponse, waitUntil)\n    } else {\n      originalResponse.end()\n    }\n  }\n}\n"], "names": ["isNodeNextResponse", "pipeToNodeResponse", "splitCookiesString", "sendResponse", "req", "res", "response", "waitUntil", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headersWithMultipleValuesAllowed", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "append<PERSON><PERSON>er", "isHeaderPresent", "<PERSON><PERSON><PERSON><PERSON>", "includes", "originalResponse", "body", "method", "end"], "mappings": ";;;AACA,SAASA,kBAAkB,QAAQ,sBAAqB;AAExD,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,kBAAkB,QAAQ,cAAa;;;;AASzC,eAAeC,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB,EAClBC,SAA4B;IAE5B,IACE,AACA,6DAA6D,QADQ;IAErEC,QAAQC,GAAG,CAACC,YAAY,uBAAK,yLAC7BV,qBAAAA,EAAmBK,MACnB;YAkBA,AACAC,kCADkC;QAjBlC,iCAAiC;QACjCD,IAAIM,UAAU,GAAGL,SAASM,MAAM;QAChCP,IAAIQ,aAAa,GAAGP,SAASQ,UAAU;QAEvC,uEAAuE;QACvE,iDAAiD;QACjD,EAAE;QACF,OAAO;QACP,+CAA+C;QAC/C,MAAMC,mCAAmC;YACvC,8CAA8C;YAC9C;YACA;YACA;YACA;SACD;SAGDT,oBAAAA,SAASU,OAAO,KAAA,OAAA,KAAA,IAAhBV,kBAAkBW,OAAO,CAAC,CAACC,OAAOC;YAChC,8EAA8E;YAC9E,IAAIA,KAAKC,WAAW,OAAO,2BAA2B;gBACpD;YACF;YAEA,yDAAyD;YACzD,IAAID,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,WAAUnB,wLAAAA,EAAmBgB,OAAQ;oBAC9Cb,IAAIiB,YAAY,CAACH,MAAME;gBACzB;YACF,OAAO;gBACL,8EAA8E;gBAC9E,4CAA4C;gBAC5C,MAAME,kBAAkB,OAAOlB,IAAImB,SAAS,CAACL,UAAU;gBACvD,IACEJ,iCAAiCU,QAAQ,CAACN,KAAKC,WAAW,OAC1D,CAACG,iBACD;oBACAlB,IAAIiB,YAAY,CAACH,MAAMD;gBACzB;YACF;QACF;QAEA;;;;;KAKC,GAED,MAAM,EAAEQ,gBAAgB,EAAE,GAAGrB;QAE7B,qGAAqG;QACrG,IAAIC,SAASqB,IAAI,IAAIvB,IAAIwB,MAAM,KAAK,QAAQ;YAC1C,8KAAM3B,qBAAAA,EAAmBK,SAASqB,IAAI,EAAED,kBAAkBnB;QAC5D,OAAO;YACLmB,iBAAiBG,GAAG;QACtB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/server/lib/cache-control.ts"], "sourcesContent": ["import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n"], "names": ["CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>", "undefined"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,sBAAqB;;AAgB7C,SAASC,sBAAsB,EACpCC,UAAU,EACVC,MAAM,EACO;IACb,MAAMC,YACJ,OAAOF,eAAe,YACtBC,WAAWE,aACXH,aAAaC,SACT,CAAC,yBAAyB,EAAEA,SAASD,YAAY,GACjD;IAEN,IAAIA,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,OAAOA,eAAe,UAAU;QACzC,OAAO,CAAC,SAAS,EAAEA,aAAaE,WAAW;IAC7C;IAEA,OAAO,CAAC,SAAS,4JAAEJ,iBAAAA,GAAiBI,WAAW;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import {\n  AppRouteRouteModule,\n  type AppRouteRouteHandlerContext,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { getTracer, type Span, SpanKind } from '../../server/lib/trace/tracer'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { sendResponse } from '../../server/send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  toNodeOutgoingHttpHeaders,\n} from '../../server/web/utils'\nimport { getCacheControlHeader } from '../../server/lib/cache-control'\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport {\n  CachedRouteKind,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    params,\n    nextConfig,\n    isDraftMode,\n    prerenderManifest,\n    routerServerContext,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    resolvedPathname,\n  } = prepareResult\n\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let isIsr = Boolean(\n    prerenderManifest.dynamicRoutes[normalizedSrcPage] ||\n      prerenderManifest.routes[resolvedPathname]\n  )\n\n  if (isIsr && !isDraftMode) {\n    const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname])\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n\n    if (prerenderInfo) {\n      if (prerenderInfo.fallback === false && !isPrerendered) {\n        throw new NoFallbackError()\n      }\n    }\n  }\n\n  let cacheKey: string | null = null\n\n  if (isIsr && !routeModule.isDev && !isDraftMode) {\n    cacheKey = resolvedPathname\n    // ensure /index and / is normalized to one key\n    cacheKey = cacheKey === '/index' ? '/' : cacheKey\n  }\n\n  const supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr\n\n  // This is a revalidation request if the request is for a static\n  // page and it is not being resumed from a postponed render and\n  // it is not a dynamic RSC request then it is a revalidation\n  // request.\n  const isRevalidate = isIsr && !supportsDynamicResponse\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest,\n    renderOpts: {\n      experimental: {\n        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n      },\n      supportsDynamicResponse,\n      incrementalCache: getRequestMeta(req, 'incrementalCache'),\n      cacheLifeProfiles: nextConfig.experimental?.cacheLife,\n      isRevalidate,\n      waitUntil: ctx.waitUntil,\n      onClose: (cb) => {\n        res.on('close', cb)\n      },\n      onAfterTaskError: undefined,\n      onInstrumentationRequestError: (error, _request, errorContext) =>\n        routeModule.onRequestError(\n          req,\n          error,\n          errorContext,\n          routerServerContext\n        ),\n    },\n    sharedContext: {\n      buildId,\n    },\n  }\n  const nodeNextReq = new NodeNextRequest(req)\n  const nodeNextRes = new NodeNextResponse(res)\n\n  const nextReq = NextRequestAdapter.fromNodeNextRequest(\n    nodeNextReq,\n    signalFromNodeResponse(res)\n  )\n\n  try {\n    const invokeRouteModule = async (span?: Span) => {\n      return routeModule.handle(nextReq, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const handleResponse = async (currentSpan?: Span) => {\n      const responseGenerator: ResponseGenerator = async ({\n        previousCacheEntry,\n      }) => {\n        try {\n          if (\n            !getRequestMeta(req, 'minimalMode') &&\n            isOnDemandRevalidate &&\n            revalidateOnlyGenerated &&\n            !previousCacheEntry\n          ) {\n            res.statusCode = 404\n            // on-demand revalidate always sets this header\n            res.setHeader('x-nextjs-cache', 'REVALIDATED')\n            res.end('This page could not be found')\n            return null\n          }\n\n          const response = await invokeRouteModule(currentSpan)\n\n          ;(req as any).fetchMetrics = (context.renderOpts as any).fetchMetrics\n          let pendingWaitUntil = context.renderOpts.pendingWaitUntil\n\n          // Attempt using provided waitUntil if available\n          // if it's not we fallback to sendResponse's handling\n          if (pendingWaitUntil) {\n            if (ctx.waitUntil) {\n              ctx.waitUntil(pendingWaitUntil)\n              pendingWaitUntil = undefined\n            }\n          }\n          const cacheTags = context.renderOpts.collectedTags\n\n          // If the request is for a static response, we can cache it so long\n          // as it's not edge.\n          if (isIsr) {\n            const blob = await response.blob()\n\n            // Copy the headers from the response.\n            const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n            if (cacheTags) {\n              headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n            }\n\n            if (!headers['content-type'] && blob.type) {\n              headers['content-type'] = blob.type\n            }\n\n            const revalidate =\n              typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n              context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                ? false\n                : context.renderOpts.collectedRevalidate\n\n            const expire =\n              typeof context.renderOpts.collectedExpire === 'undefined' ||\n              context.renderOpts.collectedExpire >= INFINITE_CACHE\n                ? undefined\n                : context.renderOpts.collectedExpire\n\n            // Create the cache entry for the response.\n            const cacheEntry: ResponseCacheEntry = {\n              value: {\n                kind: CachedRouteKind.APP_ROUTE,\n                status: response.status,\n                body: Buffer.from(await blob.arrayBuffer()),\n                headers,\n              },\n              cacheControl: { revalidate, expire },\n            }\n\n            return cacheEntry\n          } else {\n            // send response without caching if not ISR\n            await sendResponse(\n              nodeNextReq,\n              nodeNextRes,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          }\n        } catch (err) {\n          // if this is a background revalidate we need to report\n          // the request error here as it won't be bubbled\n          if (previousCacheEntry?.isStale) {\n            await routeModule.onRequestError(\n              req,\n              err,\n              {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate,\n                  isOnDemandRevalidate,\n                }),\n              },\n              routerServerContext\n            )\n          }\n          throw err\n        }\n      }\n\n      const cacheEntry = await routeModule.handleResponse({\n        req,\n        nextConfig,\n        cacheKey,\n        routeKind: RouteKind.APP_ROUTE,\n        isFallback: false,\n        prerenderManifest,\n        isRoutePPREnabled: false,\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated,\n        responseGenerator,\n        waitUntil: ctx.waitUntil,\n      })\n\n      // we don't create a cacheEntry for ISR\n      if (!isIsr) {\n        return null\n      }\n\n      if (cacheEntry?.value?.kind !== CachedRouteKind.APP_ROUTE) {\n        throw new Error(\n          `Invariant: app-route received invalid cache entry ${cacheEntry?.value?.kind}`\n        )\n      }\n\n      if (!getRequestMeta(req, 'minimalMode')) {\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n\n      // Draft mode should never be cached\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers)\n\n      if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        cacheEntry.cacheControl &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      await sendResponse(\n        nodeNextReq,\n        nodeNextRes,\n        new Response(cacheEntry.value.body, {\n          headers,\n          status: cacheEntry.value.status || 200,\n        })\n      )\n      return null\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan) {\n      await routeModule.onRequestError(req, err, {\n        routerKind: 'App Router',\n        routePath: normalizedSrcPage,\n        routeType: 'route',\n        revalidateReason: getRevalidateReason({\n          isRevalidate,\n          isOnDemandRevalidate,\n        }),\n      })\n    }\n\n    // rethrow so that we can handle serving error page\n\n    // If this is during static generation, throw the error again.\n    if (isIsr) throw err\n\n    // Otherwise, send a 500 response.\n    await sendResponse(\n      nodeNextReq,\n      nodeNextRes,\n      new Response(null, { status: 500 })\n    )\n    return null\n  }\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "getRequestMeta", "getTracer", "SpanKind", "normalizeAppPath", "NodeNextRequest", "NodeNextResponse", "NextRequestAdapter", "signalFromNodeResponse", "BaseServerSpan", "getRevalidateReason", "sendResponse", "fromNodeOutgoingHttpHeaders", "toNodeOutgoingHttpHeaders", "getCacheControlHeader", "INFINITE_CACHE", "NEXT_CACHE_TAGS_HEADER", "NoFallbackError", "CachedRouteKind", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks", "handler", "req", "res", "ctx", "nextConfig", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "params", "isDraftMode", "prerenderManifest", "routerServerContext", "isOnDemandRevalidate", "revalidateOnlyGenerated", "resolvedPathname", "normalizedSrcPage", "isIsr", "Boolean", "dynamicRoutes", "routes", "isP<PERSON>endered", "prerenderInfo", "fallback", "cache<PERSON>ey", "isDev", "supportsDynamicResponse", "isRevalidate", "method", "tracer", "activeSpan", "getActiveScopeSpan", "context", "renderOpts", "experimental", "dynamicIO", "authInterrupts", "incrementalCache", "cacheLifeProfiles", "cacheLife", "onClose", "cb", "on", "onAfterTaskError", "undefined", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "sharedContext", "nodeNextReq", "nodeNextRes", "nextReq", "fromNodeNextRequest", "invokeRouteModule", "span", "handle", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "handleResponse", "currentSpan", "cacheEntry", "responseGenerator", "previousCacheEntry", "<PERSON><PERSON><PERSON><PERSON>", "response", "fetchMetrics", "pendingWaitUntil", "cacheTags", "collectedTags", "blob", "headers", "type", "revalidate", "collectedRevalidate", "expire", "collectedExpire", "value", "status", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "err", "isStale", "routerKind", "routePath", "routeType", "revalidateReason", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "Error", "isMiss", "delete", "<PERSON><PERSON><PERSON><PERSON>", "set", "Response", "withPropagatedContext", "trace", "spanName", "SERVER", "attributes"], "mappings": ";;;;;;;;AAAA,SACEA,mBAAmB,QAGd,uDAAsD;AAC7D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,cAAcC,WAAW,QAAQ,+BAA8B;AAExE,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,SAAS,EAAaC,QAAQ,QAAQ,gCAA+B;AAC9E,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,8BAA6B;AAC/E,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,wDAAuD;AAC9D,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,cAAc,EAAEC,sBAAsB,QAAQ,sBAAqB;AAC5E,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SACEC,eAAe,QAGV,8BAA6B;;AAEpC,YAAYC,cAAc,eAAc;;;;;;;;;;;;;;;;;;;;;;AAOxC,2EAA2E;AAC3E,UAAU;AACV,MAAA,mBAAA,CAA0B;AAE1B,MAAMC,cAAc,8MAAIvB,sBAAAA,CAAoB;IAC1CwB,YAAY;QACVC,uKAAMxB,YAAAA,CAAUyB,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,SAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,CAAI;IACvDC,kBAAkB;IAClBC;cACAhB;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEiB,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAE,GAAGlB;AAEhE,SAASrB;IACP,oLAAOC,aAAAA,EAAY;QACjBoC;QACAC;IACF;AACF;;AAUO,eAAeE,QACpBC,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QA6FsBC;IA3FvB,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIf,QAAQC,GAAG,CAACe,SAAS,eAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBlB,QAAQC,GAAG,CACnCkB,4BAA4B;IAE/B,MAAMC,gBAAgB,MAAM7B,YAAY8B,OAAO,CAACV,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACE,eAAe;QAClBR,IAAIU,UAAU,GAAG;QACjBV,IAAIW,GAAG,CAAC;QACRV,IAAIW,SAAS,IAAA,OAAA,KAAA,IAAbX,IAAIW,SAAS,CAAA,IAAA,CAAbX,KAAgBY,QAAQC,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,EACJC,OAAO,EACPC,MAAM,EACNd,UAAU,EACVe,WAAW,EACXC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,uBAAuB,EACvBC,gBAAgB,EACjB,GAAGd;IAEJ,MAAMe,oBAAoB5D,iNAAAA,EAAiBwC;IAE3C,IAAIqB,QAAQC,QACVP,kBAAkBQ,aAAa,CAACH,kBAAkB,IAChDL,kBAAkBS,MAAM,CAACL,iBAAiB;IAG9C,IAAIE,SAAS,CAACP,aAAa;QACzB,MAAMW,gBAAgBH,QAAQP,kBAAkBS,MAAM,CAACL,iBAAiB;QACxE,MAAMO,gBAAgBX,kBAAkBQ,aAAa,CAACH,kBAAkB;QAExE,IAAIM,eAAe;YACjB,IAAIA,cAAcC,QAAQ,KAAK,SAAS,CAACF,eAAe;gBACtD,MAAM,iPAAIpD,mBAAAA;YACZ;QACF;IACF;IAEA,IAAIuD,WAA0B;IAE9B,IAAIP,SAAS,CAAC7C,YAAYqD,KAAK,IAAI,CAACf,aAAa;QAC/Cc,WAAWT;QACX,+CAA+C;QAC/CS,WAAWA,aAAa,WAAW,MAAMA;IAC3C;IAEA,MAAME,0BACJ,AACAtD,YAAYqD,KAAK,KAAK,QACtB,4BAF0D,yCAEW;IACrE,gBAAgB;IAChB,CAACR;IAEH,gEAAgE;IAChE,+DAA+D;IAC/D,4DAA4D;IAC5D,WAAW;IACX,MAAMU,eAAeV,SAAS,CAACS;IAE/B,MAAME,SAASpC,IAAIoC,MAAM,IAAI;IAC7B,MAAMC,aAAS3E,sLAAAA;IACf,MAAM4E,aAAaD,OAAOE,kBAAkB;IAE5C,MAAMC,UAAuC;QAC3CvB;QACAE;QACAsB,YAAY;YACVC,cAAc;gBACZC,WAAWjB,QAAQvB,WAAWuC,YAAY,CAACC,SAAS;gBACpDC,gBAAgBlB,QAAQvB,WAAWuC,YAAY,CAACE,cAAc;YAChE;YACAV;YACAW,yLAAkBpF,iBAAAA,EAAeuC,KAAK;YACtC8C,iBAAiB,EAAA,CAAE3C,2BAAAA,WAAWuC,YAAY,KAAA,OAAA,KAAA,IAAvBvC,yBAAyB4C,SAAS;YACrDZ;YACAtB,WAAWX,IAAIW,SAAS;YACxBmC,SAAS,CAACC;gBACRhD,IAAIiD,EAAE,CAAC,SAASD;YAClB;YACAE,kBAAkBC;YAClBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/C5E,YAAY6E,cAAc,CACxBzD,KACAsD,OACAE,cACApC;QAEN;QACAsC,eAAe;YACb1C;QACF;IACF;IACA,MAAM2C,cAAc,4KAAI9F,kBAAAA,CAAgBmC;IACxC,MAAM4D,cAAc,IAAI9F,2LAAAA,CAAiBmC;IAEzC,MAAM4D,qNAAU9F,qBAAAA,CAAmB+F,mBAAmB,CACpDH,4NACA3F,yBAAAA,EAAuBiC;IAGzB,IAAI;QACF,MAAM8D,oBAAoB,OAAOC;YAC/B,OAAOpF,YAAYqF,MAAM,CAACJ,SAASrB,SAAS0B,OAAO,CAAC;gBAClD,IAAI,CAACF,MAAM;gBAEXA,KAAKG,aAAa,CAAC;oBACjB,oBAAoBlE,IAAIU,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMyD,qBAAqB/B,OAAOgC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBrG,8LAAAA,CAAesG,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMI,QAAQN,mBAAmBE,GAAG,CAAC;gBACrC,IAAII,OAAO;oBACT,MAAMC,OAAO,GAAGvC,OAAO,CAAC,EAAEsC,OAAO;oBAEjCV,KAAKG,aAAa,CAAC;wBACjB,cAAcO;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAX,KAAKY,UAAU,CAACD;gBAClB,OAAO;oBACLX,KAAKY,UAAU,CAAC,GAAGxC,OAAO,CAAC,EAAEpC,IAAI6E,GAAG,EAAE;gBACxC;YACF;QACF;QAEA,MAAMC,iBAAiB,OAAOC;gBA6HxBC;YA5HJ,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;gBACC,IAAI;oBACF,IACE,wKAACzH,iBAAAA,EAAeuC,KAAK,kBACrBqB,wBACAC,2BACA,CAAC4D,oBACD;wBACAjF,IAAIU,UAAU,GAAG;wBACjB,+CAA+C;wBAC/CV,IAAIkF,SAAS,CAAC,kBAAkB;wBAChClF,IAAIW,GAAG,CAAC;wBACR,OAAO;oBACT;oBAEA,MAAMwE,WAAW,MAAMrB,kBAAkBgB;oBAEvC/E,IAAYqF,YAAY,GAAI7C,QAAQC,UAAU,CAAS4C,YAAY;oBACrE,IAAIC,mBAAmB9C,QAAQC,UAAU,CAAC6C,gBAAgB;oBAE1D,gDAAgD;oBAChD,qDAAqD;oBACrD,IAAIA,kBAAkB;wBACpB,IAAIpF,IAAIW,SAAS,EAAE;4BACjBX,IAAIW,SAAS,CAACyE;4BACdA,mBAAmBlC;wBACrB;oBACF;oBACA,MAAMmC,YAAY/C,QAAQC,UAAU,CAAC+C,aAAa;oBAElD,mEAAmE;oBACnE,oBAAoB;oBACpB,IAAI/D,OAAO;wBACT,MAAMgE,OAAO,MAAML,SAASK,IAAI;wBAEhC,sCAAsC;wBACtC,MAAMC,8KAAUrH,4BAAAA,EAA0B+G,SAASM,OAAO;wBAE1D,IAAIH,WAAW;4BACbG,OAAO,2JAAClH,yBAAAA,CAAuB,GAAG+G;wBACpC;wBAEA,IAAI,CAACG,OAAO,CAAC,eAAe,IAAID,KAAKE,IAAI,EAAE;4BACzCD,OAAO,CAAC,eAAe,GAAGD,KAAKE,IAAI;wBACrC;wBAEA,MAAMC,aACJ,OAAOpD,QAAQC,UAAU,CAACoD,mBAAmB,KAAK,eAClDrD,QAAQC,UAAU,CAACoD,mBAAmB,IAAItH,2KAAAA,GACtC,QACAiE,QAAQC,UAAU,CAACoD,mBAAmB;wBAE5C,MAAMC,SACJ,OAAOtD,QAAQC,UAAU,CAACsD,eAAe,KAAK,eAC9CvD,QAAQC,UAAU,CAACsD,eAAe,8JAAIxH,iBAAAA,GAClC6E,YACAZ,QAAQC,UAAU,CAACsD,eAAe;wBAExC,2CAA2C;wBAC3C,MAAMf,aAAiC;4BACrCgB,OAAO;gCACLlH,oLAAMJ,kBAAAA,CAAgBK,SAAS;gCAC/BkH,QAAQb,SAASa,MAAM;gCACvBC,MAAMC,OAAOC,IAAI,CAAC,MAAMX,KAAKY,WAAW;gCACxCX;4BACF;4BACAY,cAAc;gCAAEV;gCAAYE;4BAAO;wBACrC;wBAEA,OAAOd;oBACT,OAAO;wBACL,2CAA2C;wBAC3C,8KAAM7G,eAAAA,EACJwF,aACAC,aACAwB,UACA5C,QAAQC,UAAU,CAAC6C,gBAAgB;wBAErC,OAAO;oBACT;gBACF,EAAE,OAAOiB,KAAK;oBACZ,uDAAuD;oBACvD,gDAAgD;oBAChD,IAAIrB,sBAAAA,OAAAA,KAAAA,IAAAA,mBAAoBsB,OAAO,EAAE;wBAC/B,MAAM5H,YAAY6E,cAAc,CAC9BzD,KACAuG,KACA;4BACEE,YAAY;4BACZC,WAAWtG;4BACXuG,WAAW;4BACXC,kMAAkB1I,sBAAAA,EAAoB;gCACpCiE;gCACAd;4BACF;wBACF,GACAD;oBAEJ;oBACA,MAAMmF;gBACR;YACF;YAEA,MAAMvB,aAAa,MAAMpG,YAAYkG,cAAc,CAAC;gBAClD9E;gBACAG;gBACA6B;gBACA6E,4KAAWvJ,YAAAA,CAAUyB,SAAS;gBAC9B+H,YAAY;gBACZ3F;gBACA4F,mBAAmB;gBACnB1F;gBACAC;gBACA2D;gBACApE,WAAWX,IAAIW,SAAS;YAC1B;YAEA,uCAAuC;YACvC,IAAI,CAACY,OAAO;gBACV,OAAO;YACT;YAEA,IAAIuD,CAAAA,cAAAA,OAAAA,KAAAA,IAAAA,CAAAA,oBAAAA,WAAYgB,KAAK,KAAA,OAAA,KAAA,IAAjBhB,kBAAmBlG,IAAI,oLAAKJ,kBAAAA,CAAgBK,SAAS,EAAE;oBAEFiG;gBADvD,MAAM,OAAA,cAEL,CAFK,IAAIgC,MACR,CAAC,kDAAkD,EAAEhC,cAAAA,OAAAA,KAAAA,IAAAA,CAAAA,qBAAAA,WAAYgB,KAAK,KAAA,OAAA,KAAA,IAAjBhB,mBAAmBlG,IAAI,EAAE,GAD1E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,EAACrB,uLAAAA,EAAeuC,KAAK,gBAAgB;gBACvCC,IAAIkF,SAAS,CACX,kBACA9D,uBACI,gBACA2D,WAAWiC,MAAM,GACf,SACAjC,WAAWwB,OAAO,GAChB,UACA;YAEZ;YAEA,oCAAoC;YACpC,IAAItF,aAAa;gBACfjB,IAAIkF,SAAS,CACX,iBACA;YAEJ;YAEA,MAAMO,8KAAUtH,8BAAAA,EAA4B4G,WAAWgB,KAAK,CAACN,OAAO;YAEpE,IAAI,CAAEjI,wKAAAA,iBAAAA,EAAeuC,KAAK,kBAAkByB,KAAI,GAAI;gBAClDiE,QAAQwB,MAAM,2JAAC1I,yBAAAA;YACjB;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACEwG,WAAWsB,YAAY,IACvB,CAACrG,IAAIkH,SAAS,CAAC,oBACf,CAACzB,QAAQpB,GAAG,CAAC,kBACb;gBACAoB,QAAQ0B,GAAG,CACT,kBACA9I,sMAAAA,EAAsB0G,WAAWsB,YAAY;YAEjD;YAEA,8KAAMnI,eAAAA,EACJwF,aACAC,aACA,IAAIyD,SAASrC,WAAWgB,KAAK,CAACE,IAAI,EAAE;gBAClCR;gBACAO,QAAQjB,WAAWgB,KAAK,CAACC,MAAM,IAAI;YACrC;YAEF,OAAO;QACT;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAI3D,YAAY;YACd,MAAMwC,eAAexC;QACvB,OAAO;YACL,MAAMD,OAAOiF,qBAAqB,CAACtH,IAAI0F,OAAO,EAAE,IAC9CrD,OAAOkF,KAAK,CACVtJ,8LAAAA,CAAesG,aAAa,EAC5B;oBACEiD,UAAU,GAAGpF,OAAO,CAAC,EAAEpC,IAAI6E,GAAG,EAAE;oBAChC/F,gLAAMnB,WAAAA,CAAS8J,MAAM;oBACrBC,YAAY;wBACV,eAAetF;wBACf,eAAepC,IAAI6E,GAAG;oBACxB;gBACF,GACAC;QAGN;IACF,EAAE,OAAOyB,KAAK;QACZ,kDAAkD;QAClD,IAAI,CAACjE,YAAY;YACf,MAAM1D,YAAY6E,cAAc,CAACzD,KAAKuG,KAAK;gBACzCE,YAAY;gBACZC,WAAWlF;gBACXmF,WAAW;gBACXC,kMAAkB1I,sBAAAA,EAAoB;oBACpCiE;oBACAd;gBACF;YACF;QACF;QAEA,mDAAmD;QAEnD,8DAA8D;QAC9D,IAAII,OAAO,MAAM8E;QAEjB,kCAAkC;QAClC,8KAAMpI,eAAAA,EACJwF,aACAC,aACA,IAAIyD,SAAS,MAAM;YAAEpB,QAAQ;QAAI;QAEnC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}