{"version": 3, "file": "Realm.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Realm.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAEnE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AAItC,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,8BAA8B,CAAC;AAClE,OAAO,KAAK,EAAC,YAAY,EAAE,SAAS,EAAC,MAAM,oBAAoB,CAAC;AAShE,OAAO,KAAK,aAAa,MAAM,yBAAyB,CAAC;AAIzD,OAAO,KAAK,EACV,KAAK,IAAI,aAAa,EACtB,oBAAoB,EACpB,iBAAiB,EAClB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,iBAAiB,CAAC;AAEjD,OAAO,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AAErD,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAG3C,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,gBAAgB,CAAC;AAElD;;GAEG;AACH,8BAAsB,SAAU,SAAQ,KAAK;;IAC3C,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;gBAElB,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe;IAKlE,SAAS,CAAC,UAAU,IAAI,IAAI;IAW5B,SAAS,CAAC,qBAAqB,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;IACvE,IAAI,aAAa,IAAI,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAexD;IAEc,cAAc,CAC3B,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAIjC,QAAQ,CACrB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAiGrC,YAAY,CACV,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,GAC9B,YAAY,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC;IAU5C,cAAc,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAOnE,SAAS,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;IA0BzC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAwB3D,WAAW,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAM5D,cAAc,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,CAAC,EACpD,MAAM,EAAE,CAAC,GACR,OAAO,CAAC,CAAC,CAAC;CAQd;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,SAAS;;IAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,GAAG,cAAc;IAKjE,SAAiB,KAAK,EAAE,WAAW,CAAC;IAIpC,OAAO;IAgBP,IAAa,aAAa,IAAI,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAiCjE;IAED,IAAI,OAAO,IAAI,MAAM,GAAG,SAAS,CAEhC;IAED,IAAa,WAAW,IAAI,SAAS,CAEpC;IAEc,gBAAgB,CAC7B,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,GACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;CAiB3B;AAED;;GAEG;AACH,qBAAa,eAAgB,SAAQ,SAAS;;IAC5C,MAAM,CAAC,IAAI,CACT,KAAK,EAAE,oBAAoB,GAAG,iBAAiB,EAC/C,MAAM,EAAE,aAAa,GACpB,eAAe;IAKlB,SAAiB,KAAK,EAAE,oBAAoB,GAAG,iBAAiB,CAAC;IAIjE,OAAO;IAQP,IAAa,WAAW,IAAI,aAAa,CAExC;IAEc,gBAAgB,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;CAG3D"}