# CVCraft - Professional Resume Builder SaaS

CVCraft is a modern, full-stack SaaS application built with Next.js that allows users to create professional resumes using beautiful templates and download them as PDFs.

## 🚀 Features

### Core Features
- **Multi-step Resume Builder**: Intuitive form with personal info, education, experience, skills, and languages
- **Live Preview**: Real-time preview as you build your resume
- **Professional Templates**: 3 beautifully designed templates (Modern, Elegant, Minimal)
- **PDF Export**: High-quality PDF generation that matches the preview exactly
- **User Authentication**: Secure login/register with NextAuth.js
- **Responsive Design**: Works perfectly on desktop and mobile

### Premium Features
- **Unlimited Resumes**: Create as many resumes as you need
- **Premium Templates**: Access to exclusive professional templates
- **Advanced Customization**: More styling options and layouts
- **Priority Support**: Get help faster with premium support

### SaaS Features
- **Stripe Integration**: Secure payment processing for subscriptions
- **Subscription Management**: User billing portal and subscription handling
- **Access Control**: Feature restrictions based on subscription tier
- **User Dashboard**: Manage all your resumes in one place

## 🛠 Tech Stack

### Frontend & Backend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **NextAuth.js** - Authentication solution

### Database & ORM
- **Prisma** - Modern database toolkit
- **SQLite** - Database (easily switchable to PostgreSQL)

### Payments & PDF
- **Stripe** - Payment processing and subscription management
- **@react-pdf/renderer** - PDF generation

### Deployment
- **Vercel** - Hosting and deployment platform

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cvcraft
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   # Database
   DATABASE_URL="file:./dev.db"

   # NextAuth.js
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"

   # Stripe (use test keys for development)
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
   STRIPE_SECRET_KEY="sk_test_..."
   STRIPE_WEBHOOK_SECRET="whsec_..."
   STRIPE_PRICE_ID_PREMIUM="price_..."
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── auth/          # Authentication endpoints
│   │   ├── resumes/       # Resume CRUD operations
│   │   ├── stripe/        # Stripe integration
│   │   └── subscription/  # Subscription management
│   ├── auth/              # Auth pages (login/register)
│   ├── billing/           # Billing and subscription page
│   ├── dashboard/         # User dashboard
│   └── resume/            # Resume builder and preview
├── components/            # Reusable React components
│   ├── resume-forms/      # Multi-step form components
│   ├── templates/         # HTML resume templates
│   ├── pdf-templates/     # PDF resume templates
│   └── ui/               # UI components
├── lib/                   # Utility functions
│   ├── auth.ts           # NextAuth configuration
│   ├── db.ts             # Database connection
│   ├── stripe.ts         # Stripe configuration
│   └── premium.ts        # Premium features logic
├── types/                 # TypeScript type definitions
└── prisma/               # Database schema and migrations
```

## 🔧 Configuration

### Stripe Setup
1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe dashboard
3. Create a product and price for the premium subscription
4. Set up webhooks for subscription events

### Database Setup
The app uses SQLite by default for development. For production, you can easily switch to PostgreSQL:

1. Update the `DATABASE_URL` in your environment variables
2. Change the provider in `prisma/schema.prisma` from `sqlite` to `postgresql`
3. Run `npx prisma db push` to create the tables

## 🚀 Deployment

### Deploy to Vercel

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure environment variables
   - Deploy

3. **Set up production database**
   - Use PlanetScale, Supabase, or any PostgreSQL provider
   - Update the `DATABASE_URL` environment variable
   - Run database migrations

4. **Configure Stripe webhooks**
   - Add your production domain to Stripe webhook endpoints
   - Update the `STRIPE_WEBHOOK_SECRET` environment variable

## 📝 Usage

### For Users
1. **Sign up** for a free account
2. **Create your first resume** using the step-by-step builder
3. **Choose a template** and see live preview
4. **Download as PDF** when you're satisfied
5. **Upgrade to Premium** for unlimited resumes and premium templates

### For Developers
1. **Add new templates** by creating components in `src/components/templates/`
2. **Extend the form** by adding new sections to the resume builder
3. **Add premium features** using the access control system
4. **Customize styling** with Tailwind CSS classes

## 🧪 Testing

Run the test suite:
```bash
npm test
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support, email <EMAIL> or join our Discord community.

## 🎯 Roadmap

- [ ] More resume templates
- [ ] Cover letter builder
- [ ] LinkedIn integration
- [ ] Resume analytics
- [ ] Team collaboration features
- [ ] Mobile app

---

Built with ❤️ using Next.js and modern web technologies.
