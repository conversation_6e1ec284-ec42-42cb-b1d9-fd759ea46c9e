// Performance monitoring and logging utilities

interface PerformanceMetric {
  name: string
  duration: number
  timestamp: number
  metadata?: Record<string, any>
}

interface ErrorLog {
  message: string
  stack?: string
  timestamp: number
  userId?: string
  metadata?: Record<string, any>
}

class MonitoringService {
  private metrics: PerformanceMetric[] = []
  private errors: ErrorLog[] = []
  private maxMetrics = 1000
  private maxErrors = 500

  // Performance monitoring
  startTimer(name: string) {
    const startTime = performance.now()
    return {
      end: (metadata?: Record<string, any>) => {
        const duration = performance.now() - startTime
        this.recordMetric({
          name,
          duration,
          timestamp: Date.now(),
          metadata
        })
        return duration
      }
    }
  }

  recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric)
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log slow operations in development
    if (process.env.NODE_ENV === 'development' && metric.duration > 1000) {
      console.warn(`Slow operation detected: ${metric.name} took ${metric.duration.toFixed(2)}ms`)
    }
  }

  // Error logging
  logError(error: Error | string, userId?: string, metadata?: Record<string, any>) {
    const errorLog: ErrorLog = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      timestamp: Date.now(),
      userId,
      metadata
    }

    this.errors.push(errorLog)
    
    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors)
    }

    // Always log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorLog)
    }

    // In production, you would send this to an external service like Sentry
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external monitoring service
      // this.sendToExternalService(errorLog)
    }
  }

  // Get performance statistics
  getPerformanceStats(name?: string) {
    const filteredMetrics = name 
      ? this.metrics.filter(m => m.name === name)
      : this.metrics

    if (filteredMetrics.length === 0) {
      return null
    }

    const durations = filteredMetrics.map(m => m.duration)
    const sorted = durations.sort((a, b) => a - b)

    return {
      count: filteredMetrics.length,
      average: durations.reduce((a, b) => a + b, 0) / durations.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      min: Math.min(...durations),
      max: Math.max(...durations)
    }
  }

  // Get recent errors
  getRecentErrors(limit = 50) {
    return this.errors
      .slice(-limit)
      .sort((a, b) => b.timestamp - a.timestamp)
  }

  // Clear old data
  cleanup() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo)
    this.errors = this.errors.filter(e => e.timestamp > oneHourAgo)
  }
}

// Singleton instance
export const monitoring = new MonitoringService()

// Cleanup old data every hour
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    monitoring.cleanup()
  }, 60 * 60 * 1000)
}

// Utility functions for common monitoring patterns
export function withPerformanceMonitoring<T extends any[], R>(
  name: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const timer = monitoring.startTimer(name)
    try {
      const result = await fn(...args)
      timer.end({ success: true })
      return result
    } catch (error) {
      timer.end({ success: false, error: error instanceof Error ? error.message : String(error) })
      monitoring.logError(error instanceof Error ? error : new Error(String(error)))
      throw error
    }
  }
}

// Database query monitoring
export function monitorDatabaseQuery<T>(queryName: string, query: Promise<T>): Promise<T> {
  const timer = monitoring.startTimer(`db:${queryName}`)
  
  return query
    .then(result => {
      timer.end({ success: true })
      return result
    })
    .catch(error => {
      timer.end({ success: false, error: error.message })
      monitoring.logError(error, undefined, { queryName })
      throw error
    })
}

// API endpoint monitoring middleware
export function createMonitoringMiddleware(endpointName: string) {
  return (handler: Function) => {
    return withPerformanceMonitoring(`api:${endpointName}`, handler)
  }
}
