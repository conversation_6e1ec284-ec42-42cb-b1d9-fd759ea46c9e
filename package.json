{"name": "cvcraft", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@react-pdf/renderer": "^4.3.0", "@stripe/stripe-js": "^7.5.0", "@types/bcryptjs": "^2.4.6", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "jspdf": "^3.0.1", "next": "15.4.1", "next-auth": "^4.24.11", "puppeteer": "^24.14.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.60.0", "stripe": "^18.3.0", "zod": "^4.0.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.4.1", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.5.6", "prisma": "^6.12.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}