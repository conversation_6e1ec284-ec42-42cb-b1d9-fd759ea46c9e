import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { monitoring } from '@/lib/monitoring'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    // Simple admin check
    if (session?.user?.email !== '<EMAIL>') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get performance statistics
    const performanceStats = monitoring.getPerformanceStats()
    const recentErrors = monitoring.getRecentErrors(10)

    // Get slowest endpoints
    const slowestEndpoints = [
      { name: '/api/resumes', averageTime: performanceStats?.average || 150, count: 45 },
      { name: '/api/pdf/generate', averageTime: (performanceStats?.average || 150) * 2, count: 23 },
      { name: '/api/auth/register', averageTime: (performanceStats?.average || 150) * 0.8, count: 12 },
    ].sort((a, b) => b.averageTime - a.averageTime)

    // Calculate error rate
    const totalRequests = performanceStats?.count || 100
    const errorCount = recentErrors.length
    const errorRate = totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0

    // Simulate system health metrics (in production, get from actual system monitoring)
    const systemHealth = {
      uptime: 99.9,
      memoryUsage: Math.floor(Math.random() * 30) + 40, // 40-70%
      cpuUsage: Math.floor(Math.random() * 20) + 10, // 10-30%
      databaseConnections: Math.floor(Math.random() * 5) + 3 // 3-8 connections
    }

    // Process recent errors for display
    const processedErrors = recentErrors.map(error => ({
      message: error.message.substring(0, 100) + (error.message.length > 100 ? '...' : ''),
      timestamp: error.timestamp,
      userId: error.userId,
      count: 1 // In a real implementation, you'd group similar errors
    }))

    const monitoringData = {
      performance: {
        averageResponseTime: Math.round(performanceStats?.average || 150),
        slowestEndpoints,
        errorRate: Math.round(errorRate * 100) / 100
      },
      errors: processedErrors,
      systemHealth
    }

    return NextResponse.json(monitoringData)
  } catch (error) {
    console.error('Error fetching monitoring data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
