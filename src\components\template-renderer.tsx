import { ResumeData, TemplateType, Resume } from '@/types/resume'
import { ModernTemplate } from './templates/modern-template'
import { ElegantTemplate } from './templates/elegant-template'
import { MinimalTemplate } from './templates/minimal-template'

interface TemplateRendererProps {
  data?: ResumeData
  resume?: Resume
  template: TemplateType
  className?: string
  isPrintMode?: boolean
}

export function TemplateRenderer({ data, resume, template, className, isPrintMode = false }: TemplateRendererProps) {
  // Use data from resume object if provided, otherwise use data prop
  const resumeData = resume?.data || data

  if (!resumeData) {
    return <div>No resume data available</div>
  }

  const commonProps = {
    data: resumeData,
    className,
    isPrintMode
  }

  switch (template) {
    case 'modern':
      return <ModernTemplate {...commonProps} />
    case 'elegant':
      return <ElegantTemplate {...commonProps} />
    case 'minimal':
      return <MinimalTemplate {...commonProps} />
    default:
      return <ModernTemplate {...commonProps} />
  }
}
