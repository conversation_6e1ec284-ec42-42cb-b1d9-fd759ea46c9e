import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  let browser = null

  try {
    console.log('Test PDF API called')

    // Try to import and use Puppeteer
    const puppeteer = await import('puppeteer')
    console.log('Puppeteer imported successfully')

    browser = await puppeteer.default.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-extensions'
      ]
    })

    console.log('Browser launched successfully')

    const page = await browser.newPage()
    console.log('New page created')

    // Set simple HTML content
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Test PDF</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 40px;
              background: white;
            }
            h1 { color: #2563eb; }
            .success { color: #059669; font-weight: bold; }
          </style>
        </head>
        <body>
          <h1>✅ Test PDF Generation Successful</h1>
          <p>This is a test PDF generated by Puppeteer.</p>
          <p class="success">If you can see this, the PDF generation is working perfectly!</p>
          <p>Generated at: ${new Date().toLocaleString()}</p>
        </body>
      </html>
    `

    await page.setContent(htmlContent, { waitUntil: 'networkidle0' })
    console.log('HTML content set')

    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      }
    })

    console.log('PDF generated successfully, size:', pdf.length, 'bytes')

    await browser.close()
    browser = null
    console.log('Browser closed')

    // Validate PDF
    if (!pdf || pdf.length === 0) {
      throw new Error('Generated PDF is empty')
    }

    // Return with proper headers
    return new NextResponse(pdf, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="test.pdf"',
        'Content-Length': pdf.length.toString(),
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    // Clean up browser on error
    if (browser) {
      try {
        await browser.close()
      } catch (closeError) {
        console.error('Error closing browser:', closeError)
      }
    }

    console.error('Error in test PDF API:', error)
    return NextResponse.json(
      {
        error: 'PDF generation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.stack : undefined) : undefined
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

export async function POST(request: NextRequest) {
  return GET(request)
}
