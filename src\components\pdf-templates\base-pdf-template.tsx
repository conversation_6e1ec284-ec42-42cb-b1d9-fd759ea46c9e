import { ResumeData } from '@/types/resume'

export interface PDFTemplateProps {
  data: ResumeData
}

export function formatPDFDate(dateString: string): string {
  if (!dateString) return ''
  const date = new Date(dateString + '-01')
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short' 
  })
}

export function formatPDFDateRange(startDate: string, endDate: string, current: boolean): string {
  const start = formatPDFDate(startDate)
  if (current) {
    return `${start} - Present`
  }
  const end = formatPDFDate(endDate)
  return `${start} - ${end}`
}
