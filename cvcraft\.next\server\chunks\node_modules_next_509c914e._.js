module.exports = {

"[project]/node_modules/next/server.js [app-route] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const serverExports = {
    NextRequest: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/request.js [app-route] (ecmascript)").NextRequest,
    NextResponse: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/response.js [app-route] (ecmascript)").NextResponse,
    ImageResponse: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/image-response.js [app-route] (ecmascript)").ImageResponse,
    userAgentFromString: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/user-agent.js [app-route] (ecmascript)").userAgentFromString,
    userAgent: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/user-agent.js [app-route] (ecmascript)").userAgent,
    URLPattern: __turbopack_context__.r("[project]/node_modules/next/dist/server/web/spec-extension/url-pattern.js [app-route] (ecmascript)").URLPattern,
    after: __turbopack_context__.r("[project]/node_modules/next/dist/server/after/index.js [app-route] (ecmascript)").after,
    connection: __turbopack_context__.r("[project]/node_modules/next/dist/server/request/connection.js [app-route] (ecmascript)").connection,
    unstable_rootParams: __turbopack_context__.r("[project]/node_modules/next/dist/server/request/root-params.js [app-route] (ecmascript)").unstable_rootParams
};
// https://nodejs.org/api/esm.html#commonjs-namespaces
// When importing CommonJS modules, the module.exports object is provided as the default export
module.exports = serverExports;
// make import { xxx } from 'next/server' work
exports.NextRequest = serverExports.NextRequest;
exports.NextResponse = serverExports.NextResponse;
exports.ImageResponse = serverExports.ImageResponse;
exports.userAgentFromString = serverExports.userAgentFromString;
exports.userAgent = serverExports.userAgent;
exports.URLPattern = serverExports.URLPattern;
exports.after = serverExports.after;
exports.connection = serverExports.connection;
exports.unstable_rootParams = serverExports.unstable_rootParams;
}}),
"[project]/node_modules/next/headers.js [app-route] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
module.exports.cookies = __turbopack_context__.r("[project]/node_modules/next/dist/server/request/cookies.js [app-route] (ecmascript)").cookies;
module.exports.headers = __turbopack_context__.r("[project]/node_modules/next/dist/server/request/headers.js [app-route] (ecmascript)").headers;
module.exports.draftMode = __turbopack_context__.r("[project]/node_modules/next/dist/server/request/draft-mode.js [app-route] (ecmascript)").draftMode;
}}),

};

//# sourceMappingURL=node_modules_next_509c914e._.js.map