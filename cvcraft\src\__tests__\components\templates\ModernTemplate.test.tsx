import { render, screen } from '@testing-library/react'
import ModernTemplate from '@/components/templates/ModernTemplate'

const mockResumeData = {
  id: '1',
  title: 'Software Engineer Resume',
  fullName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  address: 'San Francisco, CA',
  profileImage: 'https://example.com/profile.jpg',
  summary: 'Experienced software engineer with 5+ years of experience.',
  template: 'modern',
  education: [
    {
      id: '1',
      institution: 'University of California',
      degree: 'Bachelor of Science',
      fieldOfStudy: 'Computer Science',
      startDate: '2018-09',
      endDate: '2022-05',
      current: false,
      description: 'Graduated with honors',
    },
  ],
  experience: [
    {
      id: '1',
      company: 'Tech Corp',
      position: 'Software Engineer',
      location: 'San Francisco, CA',
      startDate: '2022-06',
      endDate: '',
      current: true,
      description: 'Developed web applications using React and Node.js',
    },
  ],
  skills: [
    {
      id: '1',
      name: 'JavaScript',
      level: 'expert',
    },
    {
      id: '2',
      name: 'React',
      level: 'advanced',
    },
  ],
  languages: [
    {
      id: '1',
      name: 'English',
      level: 'native',
    },
    {
      id: '2',
      name: 'Spanish',
      level: 'conversational',
    },
  ],
}

describe('ModernTemplate', () => {
  it('renders resume data correctly', () => {
    render(<ModernTemplate data={mockResumeData} />)

    // Check if personal information is displayed
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('+****************')).toBeInTheDocument()
    expect(screen.getAllByText('San Francisco, CA').length).toBeGreaterThan(0)

    // Check if summary is displayed
    expect(screen.getByText('Experienced software engineer with 5+ years of experience.')).toBeInTheDocument()

    // Check if education is displayed
    expect(screen.getByText('Bachelor of Science')).toBeInTheDocument()
    expect(screen.getByText('University of California')).toBeInTheDocument()

    // Check if experience is displayed
    expect(screen.getByText('Software Engineer')).toBeInTheDocument()
    expect(screen.getByText('Tech Corp')).toBeInTheDocument()

    // Check if skills are displayed
    expect(screen.getByText('JavaScript')).toBeInTheDocument()
    expect(screen.getByText('React')).toBeInTheDocument()

    // Check if languages are displayed
    expect(screen.getByText('English')).toBeInTheDocument()
    expect(screen.getByText('Spanish')).toBeInTheDocument()
  })

  it('handles missing optional fields gracefully', () => {
    const minimalData = {
      ...mockResumeData,
      phone: undefined,
      address: undefined,
      profileImage: undefined,
      summary: undefined,
      education: [],
      experience: [],
      skills: [],
      languages: [],
    }

    render(<ModernTemplate data={minimalData} />)

    // Should still render the name and email
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })
})
