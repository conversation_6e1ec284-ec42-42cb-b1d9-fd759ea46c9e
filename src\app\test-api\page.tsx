'use client'

export default function TestAPIPage() {
  const testPuppeteerAPI = async () => {
    try {
      console.log('Testing Puppeteer API...')
      
      const response = await fetch('/api/test-pdf', {
        method: 'GET'
      })
      
      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))
      
      if (response.ok) {
        const blob = await response.blob()
        console.log('PDF blob size:', blob.size)
        
        // Download the PDF
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'test.pdf'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        alert('Test PDF downloaded successfully!')
      } else {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        alert(`API Error: ${errorData.error}\nDetails: ${errorData.details}`)
      }
    } catch (error) {
      console.error('Fetch error:', error)
      alert(`Fetch error: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6">Test Puppeteer API</h1>
        <button
          onClick={testPuppeteerAPI}
          className="w-full bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium"
        >
          🧪 Test Puppeteer PDF API
        </button>
        <div className="mt-4 p-4 bg-yellow-50 rounded-md">
          <p className="text-sm text-yellow-800">
            This will test if Puppeteer is working on your system.
            Check the browser console for detailed logs.
          </p>
        </div>
      </div>
    </div>
  )
}
