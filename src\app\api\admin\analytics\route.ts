import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    // Simple admin check - in production, you'd want proper role-based access
    if (session?.user?.email !== '<EMAIL>') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user growth over last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const userGrowth = await prisma.user.groupBy({
      by: ['createdAt'],
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      _count: {
        id: true
      }
    })

    // Get resume creation stats by template
    const resumesByTemplate = await prisma.resume.groupBy({
      by: ['template'],
      _count: {
        id: true
      }
    })

    // Get subscription conversion rate
    const totalUsers = await prisma.user.count()
    const premiumUsers = await prisma.user.count({
      where: { isPremium: true }
    })
    const conversionRate = totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0

    // Get monthly recurring revenue (MRR) - simplified calculation
    const activeSubscriptions = await prisma.subscription.count({
      where: { status: 'active' }
    })
    const mrr = activeSubscriptions * 9.99 // Assuming $9.99/month

    // Get recent activity
    const recentResumes = await prisma.resume.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            isPremium: true
          }
        }
      }
    })

    return NextResponse.json({
      userGrowth: userGrowth.map(item => ({
        date: item.createdAt,
        count: item._count.id
      })),
      resumesByTemplate: resumesByTemplate.map(item => ({
        template: item.template,
        count: item._count.id
      })),
      conversionRate: Math.round(conversionRate * 100) / 100,
      mrr: Math.round(mrr * 100) / 100,
      recentResumes: recentResumes.map(resume => ({
        id: resume.id,
        title: resume.title,
        template: resume.template,
        createdAt: resume.createdAt,
        user: resume.user
      }))
    })
  } catch (error) {
    console.error('Error fetching admin analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
