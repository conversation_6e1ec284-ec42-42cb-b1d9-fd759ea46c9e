'use client'

import { ResumeData } from '@/types/resume'

interface ResumeScoreProps {
  data: ResumeData
  className?: string
}

interface ScoreItem {
  category: string
  score: number
  maxScore: number
  feedback: string[]
  suggestions: string[]
}

export function ResumeScore({ data, className = '' }: ResumeScoreProps) {
  const analyzeResume = (resumeData: ResumeData): ScoreItem[] => {
    const scores: ScoreItem[] = []

    // Personal Information Analysis
    const personalScore = analyzePersonalInfo(resumeData.personalInfo)
    scores.push(personalScore)

    // Experience Analysis
    const experienceScore = analyzeExperience(resumeData.experience)
    scores.push(experienceScore)

    // Education Analysis
    const educationScore = analyzeEducation(resumeData.education)
    scores.push(educationScore)

    // Skills Analysis
    const skillsScore = analyzeSkills(resumeData.skills)
    scores.push(skillsScore)

    return scores
  }

  const analyzePersonalInfo = (personalInfo: any): ScoreItem => {
    let score = 0
    const feedback: string[] = []
    const suggestions: string[] = []
    const maxScore = 25

    // Check required fields
    if (personalInfo.fullName?.trim()) {
      score += 5
      feedback.push('✓ Full name provided')
    } else {
      suggestions.push('Add your full name')
    }

    if (personalInfo.email?.includes('@')) {
      score += 5
      feedback.push('✓ Email address provided')
    } else {
      suggestions.push('Add a professional email address')
    }

    if (personalInfo.phone?.trim()) {
      score += 3
      feedback.push('✓ Phone number provided')
    } else {
      suggestions.push('Add your phone number')
    }

    if (personalInfo.location?.trim()) {
      score += 2
      feedback.push('✓ Location provided')
    } else {
      suggestions.push('Add your location (city, state)')
    }

    // Check summary quality
    if (personalInfo.summary?.trim()) {
      const summaryLength = personalInfo.summary.trim().length
      if (summaryLength >= 100 && summaryLength <= 300) {
        score += 10
        feedback.push('✓ Professional summary is well-sized')
      } else if (summaryLength > 0) {
        score += 5
        if (summaryLength < 100) {
          suggestions.push('Expand your professional summary (aim for 100-300 characters)')
        } else {
          suggestions.push('Shorten your professional summary (aim for 100-300 characters)')
        }
      }
    } else {
      suggestions.push('Add a compelling professional summary')
    }

    return {
      category: 'Personal Information',
      score,
      maxScore,
      feedback,
      suggestions
    }
  }

  const analyzeExperience = (experience: any[]): ScoreItem => {
    let score = 0
    const feedback: string[] = []
    const suggestions: string[] = []
    const maxScore = 30

    if (experience.length === 0) {
      suggestions.push('Add your work experience')
      return { category: 'Work Experience', score, maxScore, feedback, suggestions }
    }

    // Check if has experience
    if (experience.length > 0) {
      score += 10
      feedback.push(`✓ ${experience.length} work experience${experience.length > 1 ? 's' : ''} added`)
    }

    // Check experience quality
    experience.forEach((exp, index) => {
      if (exp.position?.trim() && exp.company?.trim()) {
        score += 2
      }
      
      if (exp.description?.trim()) {
        const descLength = exp.description.trim().length
        if (descLength >= 50) {
          score += 3
        } else {
          suggestions.push(`Add more details to experience #${index + 1}`)
        }
      } else {
        suggestions.push(`Add description to experience #${index + 1}`)
      }
    })

    // Bonus for recent experience
    const hasRecentExperience = experience.some(exp => exp.current || 
      new Date(exp.endDate + '-01') > new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000)
    )
    
    if (hasRecentExperience) {
      score += 5
      feedback.push('✓ Recent work experience included')
    }

    return {
      category: 'Work Experience',
      score: Math.min(score, maxScore),
      maxScore,
      feedback,
      suggestions
    }
  }

  const analyzeEducation = (education: any[]): ScoreItem => {
    let score = 0
    const feedback: string[] = []
    const suggestions: string[] = []
    const maxScore = 20

    if (education.length === 0) {
      suggestions.push('Add your education background')
      return { category: 'Education', score, maxScore, feedback, suggestions }
    }

    // Check if has education
    if (education.length > 0) {
      score += 10
      feedback.push(`✓ ${education.length} education entr${education.length > 1 ? 'ies' : 'y'} added`)
    }

    // Check education completeness
    education.forEach((edu, index) => {
      if (edu.degree?.trim() && edu.field?.trim() && edu.institution?.trim()) {
        score += 5
      } else {
        suggestions.push(`Complete all fields for education #${index + 1}`)
      }
    })

    return {
      category: 'Education',
      score: Math.min(score, maxScore),
      maxScore,
      feedback,
      suggestions
    }
  }

  const analyzeSkills = (skills: any[]): ScoreItem => {
    let score = 0
    const feedback: string[] = []
    const suggestions: string[] = []
    const maxScore = 25

    if (skills.length === 0) {
      suggestions.push('Add your relevant skills')
      return { category: 'Skills', score, maxScore, feedback, suggestions }
    }

    // Check skills quantity
    if (skills.length >= 5) {
      score += 15
      feedback.push('✓ Good number of skills listed')
    } else if (skills.length > 0) {
      score += 10
      suggestions.push('Add more skills (aim for 5-10 relevant skills)')
    }

    // Check skills have proficiency levels
    const skillsWithLevels = skills.filter(skill => skill.level?.trim())
    if (skillsWithLevels.length === skills.length) {
      score += 5
      feedback.push('✓ All skills have proficiency levels')
    } else {
      suggestions.push('Add proficiency levels to all skills')
    }

    // Check for skill categories
    const skillsWithCategories = skills.filter(skill => skill.category?.trim())
    if (skillsWithCategories.length > 0) {
      score += 5
      feedback.push('✓ Skills are categorized')
    } else {
      suggestions.push('Organize skills into categories (e.g., "Programming Languages", "Tools")')
    }

    return {
      category: 'Skills',
      score: Math.min(score, maxScore),
      maxScore,
      feedback,
      suggestions
    }
  }

  const scores = analyzeResume(data)
  const totalScore = scores.reduce((sum, item) => sum + item.score, 0)
  const totalMaxScore = scores.reduce((sum, item) => sum + item.maxScore, 0)
  const percentage = Math.round((totalScore / totalMaxScore) * 100)

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreLabel = (percentage: number) => {
    if (percentage >= 80) return 'Excellent'
    if (percentage >= 60) return 'Good'
    if (percentage >= 40) return 'Fair'
    return 'Needs Improvement'
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Resume Score</h2>
        <div className="flex items-center gap-4">
          <div className={`text-4xl font-bold ${getScoreColor(percentage)}`}>
            {percentage}%
          </div>
          <div>
            <div className={`text-lg font-semibold ${getScoreColor(percentage)}`}>
              {getScoreLabel(percentage)}
            </div>
            <div className="text-gray-600">
              {totalScore} out of {totalMaxScore} points
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 mt-4">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${
              percentage >= 80 ? 'bg-green-500' : 
              percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
      </div>

      {/* Detailed scores */}
      <div className="space-y-6">
        {scores.map((scoreItem) => (
          <div key={scoreItem.category} className="border-l-4 border-blue-500 pl-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-gray-900">{scoreItem.category}</h3>
              <span className="text-sm text-gray-600">
                {scoreItem.score}/{scoreItem.maxScore}
              </span>
            </div>
            
            {scoreItem.feedback.length > 0 && (
              <div className="mb-2">
                {scoreItem.feedback.map((item, index) => (
                  <div key={index} className="text-sm text-green-600 mb-1">
                    {item}
                  </div>
                ))}
              </div>
            )}
            
            {scoreItem.suggestions.length > 0 && (
              <div>
                {scoreItem.suggestions.map((suggestion, index) => (
                  <div key={index} className="text-sm text-orange-600 mb-1">
                    💡 {suggestion}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
