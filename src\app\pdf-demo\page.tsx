'use client'

import { useState } from 'react'
import { ModernTemplate } from '@/components/templates/modern-template'
import { ResumeData } from '@/types/resume'

export default function PDFDemoPage() {
  const [isGenerating, setIsGenerating] = useState(false)

  const demoResumeData: ResumeData = {
    personalInfo: {
      fullName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      website: 'https://johndoe.com',
      linkedin: 'https://linkedin.com/in/johndoe',
      summary: 'Experienced software developer with a passion for creating innovative solutions. Skilled in modern web technologies and committed to delivering high-quality applications.',
      photo: undefined
    },
    education: [
      {
        id: '1',
        degree: 'Bachelor of Science',
        field: 'Computer Science',
        institution: 'University of Technology',
        startDate: '2018-09',
        endDate: '2022-05',
        current: false,
        description: 'Graduated with honors. Focused on software engineering and web development.'
      }
    ],
    experience: [
      {
        id: '1',
        position: 'Senior Software Developer',
        company: 'Tech Solutions Inc.',
        location: 'New York, NY',
        startDate: '2022-06',
        endDate: '',
        current: true,
        description: 'Led development of multiple web applications using React and Node.js. Collaborated with cross-functional teams to deliver projects on time. Mentored junior developers and conducted code reviews.'
      },
      {
        id: '2',
        position: 'Junior Developer',
        company: 'StartupCorp',
        location: 'San Francisco, CA',
        startDate: '2021-01',
        endDate: '2022-05',
        current: false,
        description: 'Developed and maintained web applications using JavaScript and Python. Worked closely with designers to implement user interfaces.'
      }
    ],
    skills: [
      { id: '1', name: 'JavaScript', level: 'Expert' },
      { id: '2', name: 'React', level: 'Expert' },
      { id: '3', name: 'Node.js', level: 'Advanced' },
      { id: '4', name: 'TypeScript', level: 'Advanced' },
      { id: '5', name: 'Python', level: 'Intermediate' }
    ],
    languages: [
      { id: '1', name: 'English', proficiency: 'Native' },
      { id: '2', name: 'Spanish', proficiency: 'Intermediate' }
    ]
  }

  const downloadPDF = async () => {
    setIsGenerating(true)
    
    try {
      console.log('Starting PDF download...')
      
      // Try server-side PDF generation first
      try {
        const response = await fetch('/api/generate-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            resumeData: demoResumeData,
            template: 'modern'
          })
        })

        console.log('Response status:', response.status)
        console.log('Response headers:', Object.fromEntries(response.headers.entries()))

        if (response.ok) {
          const blob = await response.blob()
          console.log('PDF blob size:', blob.size)
          
          if (blob.size === 0) {
            throw new Error('Received empty PDF')
          }
          
          // Create download link
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = 'John_Doe_Resume.pdf'
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          
          console.log('Server PDF downloaded successfully')
          alert('✅ Server PDF downloaded successfully!')
          return
        } else {
          const errorText = await response.text()
          console.error('Server PDF generation failed:', errorText)
          throw new Error('Server PDF generation failed')
        }
      } catch (serverError) {
        console.log('Server PDF failed, trying client-side generation...')
        
        // Fallback to client-side PDF generation
        const { jsPDF } = await import('jspdf')
        
        const doc = new jsPDF()
        
        // Add content with proper formatting
        doc.setFontSize(24)
        doc.setTextColor(37, 99, 235)
        doc.text('John Doe', 105, 30, { align: 'center' })
        
        doc.setFontSize(12)
        doc.setTextColor(0, 0, 0)
        doc.text('📧 <EMAIL>', 105, 45, { align: 'center' })
        doc.text('📱 +****************', 105, 55, { align: 'center' })
        doc.text('📍 New York, NY', 105, 65, { align: 'center' })
        
        // Add line
        doc.setLineWidth(0.5)
        doc.setDrawColor(37, 99, 235)
        doc.line(20, 75, 190, 75)
        
        // Add summary
        doc.setFontSize(16)
        doc.setTextColor(37, 99, 235)
        doc.text('Professional Summary', 20, 90)
        
        doc.setFontSize(11)
        doc.setTextColor(0, 0, 0)
        const summary = 'Experienced software developer with a passion for creating innovative solutions.'
        const splitSummary = doc.splitTextToSize(summary, 170)
        doc.text(splitSummary, 20, 105)
        
        // Add experience
        doc.setFontSize(16)
        doc.setTextColor(37, 99, 235)
        doc.text('Experience', 20, 130)
        
        doc.setFontSize(12)
        doc.setTextColor(0, 0, 0)
        doc.setFont('helvetica', 'bold')
        doc.text('Senior Software Developer', 20, 145)
        
        doc.setFont('helvetica', 'normal')
        doc.setFontSize(10)
        doc.setTextColor(100, 100, 100)
        doc.text('Tech Solutions Inc. • New York, NY • 2022 - Present', 20, 155)
        
        doc.setFontSize(11)
        doc.setTextColor(0, 0, 0)
        doc.text('• Led development of multiple web applications', 20, 170)
        doc.text('• Collaborated with cross-functional teams', 20, 180)
        doc.text('• Mentored junior developers', 20, 190)
        
        // Save the PDF
        doc.save('John_Doe_Resume.pdf')
        
        console.log('Client-side PDF generated successfully')
        alert('✅ Client-side PDF generated successfully!')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      alert('❌ Error downloading PDF: ' + error)
    } finally {
      setIsGenerating(false)
    }
  }

  const testServerAPI = async () => {
    try {
      const response = await fetch('/api/test-pdf')
      console.log('Test API response:', response.status)
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'test.pdf'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        alert('✅ Test PDF downloaded!')
      } else {
        const error = await response.json()
        console.error('Test API error:', error)
        alert('❌ Test API failed: ' + error.message)
      }
    } catch (error) {
      console.error('Test API error:', error)
      alert('❌ Test API error: ' + error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              PDF Demo - No Auth Required
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Test PDF generation without authentication issues
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Preview */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Resume Preview
              </h2>
              <div className="border rounded-lg overflow-hidden">
                <ModernTemplate data={demoResumeData} />
              </div>
            </div>

            {/* Controls */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                PDF Generation
              </h2>
              
              <div className="space-y-4">
                <button
                  onClick={downloadPDF}
                  disabled={isGenerating}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  {isGenerating ? '⏳ Generating...' : '📄 Download PDF (Server + Fallback)'}
                </button>

                <button
                  onClick={testServerAPI}
                  className="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  🧪 Test Server API
                </button>

                <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-md">
                  <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    ✅ How This Works:
                  </h3>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• <strong>Server First:</strong> Tries Puppeteer PDF generation</li>
                    <li>• <strong>Client Fallback:</strong> Uses jsPDF if server fails</li>
                    <li>• <strong>No Auth:</strong> Bypasses NextAuth issues</li>
                    <li>• <strong>Proper Headers:</strong> Implements your recommendations</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
