// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  stripeId  String?
  isPremium Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  resumes       Resume[]
  subscriptions Subscription[]

  @@map("users")
}

model Resume {
  id        String   @id @default(cuid())
  userId    String
  title     String   @default("My Resume")
  data      Json     // Store resume data as JSON
  template  String   @default("modern") // Template name
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("resumes")
}

model Subscription {
  id                   String   @id @default(cuid())
  userId               String
  stripeSubscriptionId String   @unique
  status               String   // active, canceled, past_due, etc.
  priceId              String
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model NewsletterSubscriber {
  id           String   @id @default(cuid())
  email        String   @unique
  isActive     Boolean  @default(true)
  subscribedAt DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("newsletter_subscribers")
}
