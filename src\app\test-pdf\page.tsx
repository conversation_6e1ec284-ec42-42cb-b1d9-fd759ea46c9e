'use client'

export default function TestPDFPage() {
  const downloadPDF = () => {
    // Create simple HTML for PDF
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title><PERSON> - <PERSON>sume</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px 20px;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #2563eb;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              margin: 0;
              font-size: 2.5em;
              color: #1f2937;
            }
            .contact-info {
              margin: 15px 0;
              display: flex;
              justify-content: center;
              flex-wrap: wrap;
              gap: 20px;
            }
            .contact-info span {
              color: #6b7280;
            }
            @media print {
              body { margin: 0; padding: 20px; }
            }
            @page {
              size: A4;
              margin: 0.5in;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1><PERSON></h1>
            <div class="contact-info">
              <span>📧 <EMAIL></span>
              <span>📱 123-456-7890</span>
              <span>📍 New York, NY</span>
            </div>
          </div>

          <div>
            <h2>Professional Summary</h2>
            <p>This is a test resume generated for PDF download testing.</p>
          </div>
        </body>
      </html>
    `

    // Create blob and download
    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'John_Doe_Resume.html'
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }

  const testPDF = () => {
    // Create simple HTML for PDF
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>John Doe - Resume</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px 20px;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #2563eb;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              margin: 0;
              font-size: 2.5em;
              color: #1f2937;
            }
            .contact-info {
              margin: 15px 0;
              display: flex;
              justify-content: center;
              flex-wrap: wrap;
              gap: 20px;
            }
            .contact-info span {
              color: #6b7280;
            }
            @media print {
              body { margin: 0; padding: 20px; }
            }
            @page {
              size: A4;
              margin: 0.5in;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>John Doe</h1>
            <div class="contact-info">
              <span>📧 <EMAIL></span>
              <span>📱 123-456-7890</span>
              <span>📍 New York, NY</span>
            </div>
          </div>

          <div>
            <h2>Professional Summary</h2>
            <p>This is a test resume generated for PDF download testing.</p>
          </div>
        </body>
      </html>
    `

    // Open print dialog
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(htmlContent)
      printWindow.document.close()

      // Wait for content to load then print
      setTimeout(() => {
        printWindow.print()
      }, 500)
    } else {
      alert('Please allow popups to download PDF')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4">PDF Test</h1>
        <div className="space-y-4">
          <button
            onClick={downloadPDF}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
          >
            Download HTML Resume
          </button>
          <button
            onClick={testPDF}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          >
            Print Resume (PDF)
          </button>
        </div>
      </div>
    </div>
  )
}
