import { notFound } from 'next/navigation'
import { prisma } from '@/lib/db'
import { Template<PERSON>enderer } from '@/components/template-renderer'
import { Resume } from '@/types/resume'

interface PrintPageProps {
  params: Promise<{ id: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function PrintPage({ params, searchParams }: PrintPageProps) {
  const { id } = await params
  const { print } = await searchParams

  // Fetch resume data
  const resume = await prisma.resume.findUnique({
    where: { id }
  })

  if (!resume) {
    notFound()
  }

  const resumeData: Resume = {
    id: resume.id,
    title: resume.title,
    template: resume.template as any,
    data: resume.data as any,
    createdAt: resume.createdAt,
    updatedAt: resume.updatedAt,
    userId: resume.userId
  }

  return (
    <html>
      <head>
        <title>{`${resume.title} - Resume`}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style dangerouslySetInnerHTML={{
          __html: `
            @media print {
              body { margin: 0; padding: 0; }
              .no-print { display: none !important; }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 0;
              background: white;
            }
            .print-container {
              width: 210mm;
              min-height: 297mm;
              margin: 0 auto;
              background: white;
              box-shadow: ${print ? 'none' : '0 0 10px rgba(0,0,0,0.1)'};
              padding: 0;
            }
            @page {
              size: A4;
              margin: 0;
            }
          `
        }} />
      </head>
      <body>
        <div className="print-container">
          <TemplateRenderer 
            resume={resumeData} 
            template={resumeData.template}
            isPrintMode={true}
          />
        </div>
      </body>
    </html>
  )
}
