import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { apiRateLimit } from '@/lib/rate-limit'
import { monitoring } from '@/lib/monitoring'

export async function POST(request: NextRequest) {
  const timer = monitoring.startTimer('api:newsletter:subscribe')
  
  // Apply rate limiting
  const rateLimitResult = apiRateLimit(request)
  if (!rateLimitResult.success) {
    timer.end({ success: false, error: 'Rate limited' })
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { 
        status: 429,
        headers: {
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || ''
        }
      }
    )
  }

  try {
    const { email } = await request.json()

    // Validate email
    if (!email || !email.includes('@')) {
      timer.end({ success: false, error: 'Invalid email' })
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingSubscriber = await prisma.newsletterSubscriber.findUnique({
      where: { email }
    })

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        timer.end({ success: false, error: 'Already subscribed' })
        return NextResponse.json(
          { error: 'This email is already subscribed to our newsletter' },
          { status: 409 }
        )
      } else {
        // Reactivate subscription
        await prisma.newsletterSubscriber.update({
          where: { email },
          data: { 
            isActive: true,
            subscribedAt: new Date()
          }
        })
      }
    } else {
      // Create new subscription
      await prisma.newsletterSubscriber.create({
        data: {
          email,
          isActive: true,
          subscribedAt: new Date()
        }
      })
    }

    // In production, you would:
    // 1. Send confirmation email
    // 2. Add to email marketing service (Mailchimp, ConvertKit, etc.)
    // 3. Send welcome email series

    timer.end({ success: true })
    return NextResponse.json({ 
      message: 'Successfully subscribed to newsletter' 
    })

  } catch (error) {
    timer.end({ success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    monitoring.logError(error instanceof Error ? error : new Error(String(error)))
    console.error('Newsletter subscription error:', error)
    
    return NextResponse.json(
      { error: 'Failed to subscribe. Please try again.' },
      { status: 500 }
    )
  }
}
