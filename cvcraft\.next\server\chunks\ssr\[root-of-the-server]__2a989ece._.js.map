{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/PersonalInfoStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\nimport { useState } from 'react'\n\nexport default function PersonalInfoStep() {\n  const { register, formState: { errors }, watch, setValue } = useFormContext<ResumeFormData>()\n  const [imagePreview, setImagePreview] = useState<string>('')\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    // Create local preview immediately\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      const result = e.target?.result as string\n      setImagePreview(result)\n    }\n    reader.readAsDataURL(file)\n\n    try {\n      // Upload to Cloudinary\n      const formData = new FormData()\n      formData.append('file', file)\n\n      const response = await fetch('/api/upload/image', {\n        method: 'POST',\n        body: formData,\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setValue('profileImage', data.url)\n      } else {\n        console.error('Failed to upload image')\n        // Keep the local preview but don't set the form value\n      }\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      // Keep the local preview but don't set the form value\n    }\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <h2 className=\"text-lg font-medium text-gray-900 mb-6\">Personal Information</h2>\n      \n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div className=\"sm:col-span-2\">\n          <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700\">\n            Resume Title *\n          </label>\n          <input\n            {...register('title')}\n            type=\"text\"\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"e.g., Software Engineer Resume\"\n          />\n          {errors.title && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.title.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700\">\n            Full Name *\n          </label>\n          <input\n            {...register('fullName')}\n            type=\"text\"\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"John Doe\"\n          />\n          {errors.fullName && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.fullName.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n            Email Address *\n          </label>\n          <input\n            {...register('email')}\n            type=\"email\"\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"<EMAIL>\"\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n            Phone Number\n          </label>\n          <input\n            {...register('phone')}\n            type=\"tel\"\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"+****************\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"address\" className=\"block text-sm font-medium text-gray-700\">\n            Address\n          </label>\n          <input\n            {...register('address')}\n            type=\"text\"\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"City, State, Country\"\n          />\n        </div>\n\n        <div className=\"sm:col-span-2\">\n          <label htmlFor=\"profileImage\" className=\"block text-sm font-medium text-gray-700\">\n            Profile Photo\n          </label>\n          <div className=\"mt-1 flex items-center space-x-4\">\n            {imagePreview && (\n              <img\n                src={imagePreview}\n                alt=\"Profile preview\"\n                className=\"w-16 h-16 rounded-full object-cover\"\n              />\n            )}\n            <input\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleImageUpload}\n              className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100\"\n            />\n          </div>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Upload a professional photo (optional)\n          </p>\n        </div>\n\n        <div className=\"sm:col-span-2\">\n          <label htmlFor=\"summary\" className=\"block text-sm font-medium text-gray-700\">\n            Professional Summary\n          </label>\n          <textarea\n            {...register('summary')}\n            rows={4}\n            className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n            placeholder=\"Brief overview of your professional background and key achievements...\"\n          />\n          <p className=\"mt-1 text-sm text-gray-500\">\n            A brief summary of your professional background (optional)\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,mCAAmC;QACnC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,SAAS,EAAE,MAAM,EAAE;YACzB,gBAAgB;QAClB;QACA,OAAO,aAAa,CAAC;QAErB,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,gBAAgB,KAAK,GAAG;YACnC,OAAO;gBACL,QAAQ,KAAK,CAAC;YACd,sDAAsD;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,sDAAsD;QACxD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAyC;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA0C;;;;;;0CAG3E,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAIlE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA0C;;;;;;0CAG9E,8OAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,QAAQ,kBACd,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAIrE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA0C;;;;;;0CAG3E,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAIlE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA0C;;;;;;0CAG3E,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA0C;;;;;;0CAG7E,8OAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA0C;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;oCACZ,8BACC,8OAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;kDAGd,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA0C;;;;;;0CAG7E,8OAAC;gCACE,GAAG,SAAS,UAAU;gCACvB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;0CAEd,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/EducationStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext, useFieldArray } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\n\nexport default function EducationStep() {\n  const { register, control, formState: { errors }, watch } = useFormContext<ResumeFormData>()\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: 'education',\n  })\n\n  const addEducation = () => {\n    append({\n      institution: '',\n      degree: '',\n      fieldOfStudy: '',\n      startDate: '',\n      endDate: '',\n      current: false,\n      description: '',\n    })\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-lg font-medium text-gray-900\">Education</h2>\n        <button\n          type=\"button\"\n          onClick={addEducation}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n        >\n          Add Education\n        </button>\n      </div>\n\n      {fields.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No education entries yet. Click \"Add Education\" to get started.</p>\n        </div>\n      )}\n\n      <div className=\"space-y-6\">\n        {fields.map((field, index) => {\n          const currentValue = watch(`education.${index}.current`)\n          \n          return (\n            <div key={field.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex justify-between items-start mb-4\">\n                <h3 className=\"text-md font-medium text-gray-900\">Education #{index + 1}</h3>\n                <button\n                  type=\"button\"\n                  onClick={() => remove(index)}\n                  className=\"text-red-600 hover:text-red-800 text-sm\"\n                >\n                  Remove\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Institution *\n                  </label>\n                  <input\n                    {...register(`education.${index}.institution`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"University of Example\"\n                  />\n                  {errors.education?.[index]?.institution && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.education[index]?.institution?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Degree *\n                  </label>\n                  <input\n                    {...register(`education.${index}.degree`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Bachelor of Science\"\n                  />\n                  {errors.education?.[index]?.degree && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.education[index]?.degree?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Field of Study\n                  </label>\n                  <input\n                    {...register(`education.${index}.fieldOfStudy`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Computer Science\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Start Date *\n                  </label>\n                  <input\n                    {...register(`education.${index}.startDate`)}\n                    type=\"month\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  />\n                  {errors.education?.[index]?.startDate && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.education[index]?.startDate?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <input\n                    {...register(`education.${index}.current`)}\n                    type=\"checkbox\"\n                    className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                  />\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    Currently studying here\n                  </label>\n                </div>\n\n                {!currentValue && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      End Date\n                    </label>\n                    <input\n                      {...register(`education.${index}.endDate`)}\n                      type=\"month\"\n                      className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    />\n                  </div>\n                )}\n\n                <div className=\"sm:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Description\n                  </label>\n                  <textarea\n                    {...register(`education.${index}.description`)}\n                    rows={3}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Relevant coursework, achievements, GPA, etc.\"\n                  />\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACzE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,MAAM,eAAe;QACnB,OAAO;YACL,aAAa;YACb,QAAQ;YACR,cAAc;YACd,WAAW;YACX,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO;oBAClB,MAAM,eAAe,MAAM,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;oBAEvD,qBACE,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAoC;4CAAY,QAAQ;;;;;;;kDACtE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO;wCACtB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,CAAC;gDAC9C,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,SAAS,EAAE,CAAC,MAAM,EAAE,6BAC1B,8OAAC;gDAAE,WAAU;0DACV,OAAO,SAAS,CAAC,MAAM,EAAE,aAAa;;;;;;;;;;;;kDAK7C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,CAAC;gDACzC,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,SAAS,EAAE,CAAC,MAAM,EAAE,wBAC1B,8OAAC;gDAAE,WAAU;0DACV,OAAO,SAAS,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;kDAKxC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,aAAa,CAAC,CAAC;gDAC/C,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,UAAU,CAAC,CAAC;gDAC5C,MAAK;gDACL,WAAU;;;;;;4CAEX,OAAO,SAAS,EAAE,CAAC,MAAM,EAAE,2BAC1B,8OAAC;gDAAE,WAAU;0DACV,OAAO,SAAS,CAAC,MAAM,EAAE,WAAW;;;;;;;;;;;;kDAK3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC,CAAC;gDAC1C,MAAK;gDACL,WAAU;;;;;;0DAEZ,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;;;;;;;oCAKtD,CAAC,8BACA,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC,CAAC;gDAC1C,MAAK;gDACL,WAAU;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,CAAC;gDAC9C,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;;uBA3GV,MAAM,EAAE;;;;;gBAiHtB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/ExperienceStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext, useFieldArray } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\n\nexport default function ExperienceStep() {\n  const { register, control, formState: { errors }, watch } = useFormContext<ResumeFormData>()\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: 'experience',\n  })\n\n  const addExperience = () => {\n    append({\n      company: '',\n      position: '',\n      location: '',\n      startDate: '',\n      endDate: '',\n      current: false,\n      description: '',\n    })\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-lg font-medium text-gray-900\">Work Experience</h2>\n        <button\n          type=\"button\"\n          onClick={addExperience}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n        >\n          Add Experience\n        </button>\n      </div>\n\n      {fields.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No work experience entries yet. Click \"Add Experience\" to get started.</p>\n        </div>\n      )}\n\n      <div className=\"space-y-6\">\n        {fields.map((field, index) => {\n          const currentValue = watch(`experience.${index}.current`)\n          \n          return (\n            <div key={field.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex justify-between items-start mb-4\">\n                <h3 className=\"text-md font-medium text-gray-900\">Experience #{index + 1}</h3>\n                <button\n                  type=\"button\"\n                  onClick={() => remove(index)}\n                  className=\"text-red-600 hover:text-red-800 text-sm\"\n                >\n                  Remove\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Company *\n                  </label>\n                  <input\n                    {...register(`experience.${index}.company`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Company Name\"\n                  />\n                  {errors.experience?.[index]?.company && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.experience[index]?.company?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Position *\n                  </label>\n                  <input\n                    {...register(`experience.${index}.position`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Job Title\"\n                  />\n                  {errors.experience?.[index]?.position && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.experience[index]?.position?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Location\n                  </label>\n                  <input\n                    {...register(`experience.${index}.location`)}\n                    type=\"text\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"City, State\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Start Date *\n                  </label>\n                  <input\n                    {...register(`experience.${index}.startDate`)}\n                    type=\"month\"\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  />\n                  {errors.experience?.[index]?.startDate && (\n                    <p className=\"mt-1 text-sm text-red-600\">\n                      {errors.experience[index]?.startDate?.message}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <input\n                    {...register(`experience.${index}.current`)}\n                    type=\"checkbox\"\n                    className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                  />\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    Currently working here\n                  </label>\n                </div>\n\n                {!currentValue && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      End Date\n                    </label>\n                    <input\n                      {...register(`experience.${index}.endDate`)}\n                      type=\"month\"\n                      className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    />\n                  </div>\n                )}\n\n                <div className=\"sm:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Description\n                  </label>\n                  <textarea\n                    {...register(`experience.${index}.description`)}\n                    rows={4}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Describe your responsibilities, achievements, and key contributions...\"\n                  />\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    Use bullet points to highlight your key achievements and responsibilities\n                  </p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACzE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,MAAM,gBAAgB;QACpB,OAAO;YACL,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO;oBAClB,MAAM,eAAe,MAAM,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC;oBAExD,qBACE,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAoC;4CAAa,QAAQ;;;;;;;kDACvE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO;wCACtB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC,CAAC;gDAC3C,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,yBAC3B,8OAAC;gDAAE,WAAU;0DACV,OAAO,UAAU,CAAC,MAAM,EAAE,SAAS;;;;;;;;;;;;kDAK1C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,SAAS,CAAC,CAAC;gDAC5C,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,0BAC3B,8OAAC;gDAAE,WAAU;0DACV,OAAO,UAAU,CAAC,MAAM,EAAE,UAAU;;;;;;;;;;;;kDAK3C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,SAAS,CAAC,CAAC;gDAC5C,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,UAAU,CAAC,CAAC;gDAC7C,MAAK;gDACL,WAAU;;;;;;4CAEX,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,2BAC3B,8OAAC;gDAAE,WAAU;0DACV,OAAO,UAAU,CAAC,MAAM,EAAE,WAAW;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC,CAAC;gDAC3C,MAAK;gDACL,WAAU;;;;;;0DAEZ,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;;;;;;;oCAKtD,CAAC,8BACA,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC,CAAC;gDAC3C,MAAK;gDACL,WAAU;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,WAAW,EAAE,MAAM,YAAY,CAAC,CAAC;gDAC/C,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;uBA7GtC,MAAM,EAAE;;;;;gBAoHtB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/SkillsStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext, useFieldArray } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\n\nexport default function SkillsStep() {\n  const { register, control } = useFormContext<ResumeFormData>()\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: 'skills',\n  })\n\n  const addSkill = () => {\n    append({\n      name: '',\n      level: 'intermediate',\n    })\n  }\n\n  const skillLevels = [\n    { value: 'beginner', label: 'Beginner' },\n    { value: 'intermediate', label: 'Intermediate' },\n    { value: 'advanced', label: 'Advanced' },\n    { value: 'expert', label: 'Expert' },\n  ]\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-lg font-medium text-gray-900\">Skills</h2>\n        <button\n          type=\"button\"\n          onClick={addSkill}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n        >\n          Add Skill\n        </button>\n      </div>\n\n      {fields.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No skills added yet. Click \"Add Skill\" to get started.</p>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n        {fields.map((field, index) => (\n          <div key={field.id} className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <h3 className=\"text-sm font-medium text-gray-900\">Skill #{index + 1}</h3>\n              <button\n                type=\"button\"\n                onClick={() => remove(index)}\n                className=\"text-red-600 hover:text-red-800 text-sm\"\n              >\n                Remove\n              </button>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Skill Name *\n                </label>\n                <input\n                  {...register(`skills.${index}.name`)}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"e.g., JavaScript, Project Management\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Proficiency Level\n                </label>\n                <select\n                  {...register(`skills.${index}.level`)}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                >\n                  {skillLevels.map((level) => (\n                    <option key={level.value} value={level.value}>\n                      {level.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {fields.length > 0 && (\n        <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n          <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Tips for adding skills:</h3>\n          <ul className=\"text-sm text-blue-800 space-y-1\">\n            <li>• Include both technical and soft skills</li>\n            <li>• Be honest about your proficiency levels</li>\n            <li>• Focus on skills relevant to your target job</li>\n            <li>• Consider grouping similar skills together</li>\n          </ul>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAC3C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,MAAM,WAAW;QACf,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAgB,OAAO;QAAe;QAC/C;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAoC;4CAAQ,QAAQ;;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO;wCACtB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,CAAC;gDACpC,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,MAAM,CAAC,CAAC;gDACrC,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC;wDAAyB,OAAO,MAAM,KAAK;kEACzC,MAAM,KAAK;uDADD,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;uBAlCxB,MAAM,EAAE;;;;;;;;;;YA6CrB,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 1310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/LanguagesStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext, useFieldArray } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\n\nexport default function LanguagesStep() {\n  const { register, control } = useFormContext<ResumeFormData>()\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: 'languages',\n  })\n\n  const addLanguage = () => {\n    append({\n      name: '',\n      level: 'conversational',\n    })\n  }\n\n  const languageLevels = [\n    { value: 'basic', label: 'Basic' },\n    { value: 'conversational', label: 'Conversational' },\n    { value: 'fluent', label: 'Fluent' },\n    { value: 'native', label: 'Native' },\n  ]\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-lg font-medium text-gray-900\">Languages</h2>\n        <button\n          type=\"button\"\n          onClick={addLanguage}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n        >\n          Add Language\n        </button>\n      </div>\n\n      {fields.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No languages added yet. Click \"Add Language\" to get started.</p>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n        {fields.map((field, index) => (\n          <div key={field.id} className=\"border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <h3 className=\"text-sm font-medium text-gray-900\">Language #{index + 1}</h3>\n              <button\n                type=\"button\"\n                onClick={() => remove(index)}\n                className=\"text-red-600 hover:text-red-800 text-sm\"\n              >\n                Remove\n              </button>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Language *\n                </label>\n                <input\n                  {...register(`languages.${index}.name`)}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"e.g., English, Spanish, French\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Proficiency Level *\n                </label>\n                <select\n                  {...register(`languages.${index}.level`)}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                >\n                  {languageLevels.map((level) => (\n                    <option key={level.value} value={level.value}>\n                      {level.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {fields.length > 0 && (\n        <div className=\"mt-6 p-4 bg-green-50 rounded-lg\">\n          <h3 className=\"text-sm font-medium text-green-900 mb-2\">Language Proficiency Guide:</h3>\n          <div className=\"text-sm text-green-800 space-y-1\">\n            <div><strong>Basic:</strong> Can understand and use familiar everyday expressions</div>\n            <div><strong>Conversational:</strong> Can handle most situations while traveling and discuss familiar topics</div>\n            <div><strong>Fluent:</strong> Can express ideas fluently and spontaneously without much searching for expressions</div>\n            <div><strong>Native:</strong> Native speaker or equivalent proficiency</div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAC3C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAkB,OAAO;QAAiB;QACnD;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAoC;4CAAW,QAAQ;;;;;;;kDACrE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO;wCACtB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,CAAC;gDACvC,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA0C;;;;;;0DAG3D,8OAAC;gDACE,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,CAAC;gDACxC,WAAU;0DAET,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;wDAAyB,OAAO,MAAM,KAAK;kEACzC,MAAM,KAAK;uDADD,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;uBAlCxB,MAAM,EAAE;;;;;;;;;;YA6CrB,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDAAI,8OAAC;kDAAO;;;;;;oCAAe;;;;;;;0CAC5B,8OAAC;;kDAAI,8OAAC;kDAAO;;;;;;oCAAwB;;;;;;;0CACrC,8OAAC;;kDAAI,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;0CAC7B,8OAAC;;kDAAI,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/resume-form/TemplateStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useFormContext } from 'react-hook-form'\nimport { ResumeFormData } from '@/app/resume/create/page'\n\nexport default function TemplateStep() {\n  const { register, watch } = useFormContext<ResumeFormData>()\n  const selectedTemplate = watch('template')\n\n  const templates = [\n    {\n      id: 'modern',\n      name: 'Modern',\n      description: 'Clean and contemporary design with a professional look',\n      preview: '/templates/modern-preview.jpg',\n      features: ['Clean typography', 'Color accents', 'Modern layout', 'ATS-friendly'],\n    },\n    {\n      id: 'elegant',\n      name: 'Elegant',\n      description: 'Sophisticated design with elegant typography and spacing',\n      preview: '/templates/elegant-preview.jpg',\n      features: ['Elegant fonts', 'Refined layout', 'Professional styling', 'Minimalist design'],\n    },\n    {\n      id: 'simple',\n      name: 'Simple',\n      description: 'Minimalist design focusing on content and readability',\n      preview: '/templates/simple-preview.jpg',\n      features: ['Minimalist design', 'Easy to read', 'Classic layout', 'Highly compatible'],\n    },\n  ]\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <h2 className=\"text-lg font-medium text-gray-900 mb-6\">Choose a Template</h2>\n      \n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n        {templates.map((template) => (\n          <div key={template.id} className=\"relative\">\n            <input\n              {...register('template')}\n              type=\"radio\"\n              value={template.id}\n              id={template.id}\n              className=\"sr-only\"\n            />\n            <label\n              htmlFor={template.id}\n              className={`block cursor-pointer rounded-lg border-2 p-4 transition-all ${\n                selectedTemplate === template.id\n                  ? 'border-indigo-600 bg-indigo-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              {/* Template Preview */}\n              <div className=\"aspect-[3/4] bg-gray-100 rounded-lg mb-4 flex items-center justify-center\">\n                <div className=\"text-gray-400 text-sm\">\n                  {template.name} Preview\n                </div>\n                {/* TODO: Add actual template preview images */}\n              </div>\n\n              {/* Template Info */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  {template.name}\n                  {selectedTemplate === template.id && (\n                    <span className=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800\">\n                      Selected\n                    </span>\n                  )}\n                </h3>\n                <p className=\"text-sm text-gray-600 mb-3\">\n                  {template.description}\n                </p>\n                \n                {/* Features */}\n                <ul className=\"text-xs text-gray-500 space-y-1\">\n                  {template.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center\">\n                      <svg className=\"w-3 h-3 text-green-500 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </label>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"mt-8 p-4 bg-yellow-50 rounded-lg\">\n        <h3 className=\"text-sm font-medium text-yellow-900 mb-2\">Template Selection Tips:</h3>\n        <ul className=\"text-sm text-yellow-800 space-y-1\">\n          <li>• <strong>Modern:</strong> Great for tech, creative, and startup roles</li>\n          <li>• <strong>Elegant:</strong> Perfect for executive, consulting, and finance positions</li>\n          <li>• <strong>Simple:</strong> Ideal for traditional industries and ATS systems</li>\n          <li>• All templates are optimized for both digital viewing and printing</li>\n        </ul>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,mBAAmB,MAAM;IAE/B,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;gBAAC;gBAAoB;gBAAiB;gBAAiB;aAAe;QAClF;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;gBAAC;gBAAiB;gBAAkB;gBAAwB;aAAoB;QAC5F;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;gBAAC;gBAAqB;gBAAgB;gBAAkB;aAAoB;QACxF;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAyC;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wBAAsB,WAAU;;0CAC/B,8OAAC;gCACE,GAAG,SAAS,WAAW;gCACxB,MAAK;gCACL,OAAO,SAAS,EAAE;gCAClB,IAAI,SAAS,EAAE;gCACf,WAAU;;;;;;0CAEZ,8OAAC;gCACC,SAAS,SAAS,EAAE;gCACpB,WAAW,CAAC,4DAA4D,EACtE,qBAAqB,SAAS,EAAE,GAC5B,mCACA,yCACJ;;kDAGF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,SAAS,IAAI;gDAAC;;;;;;;;;;;;kDAMnB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDACX,SAAS,IAAI;oDACb,qBAAqB,SAAS,EAAE,kBAC/B,8OAAC;wDAAK,WAAU;kEAAsG;;;;;;;;;;;;0DAK1H,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAIvB,8OAAC;gDAAG,WAAU;0DACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J;;uDAJM;;;;;;;;;;;;;;;;;;;;;;;uBAzCT,SAAS,EAAE;;;;;;;;;;0BAuDzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;;oCAAG;kDAAE,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;0CAC9B,8OAAC;;oCAAG;kDAAE,8OAAC;kDAAO;;;;;;oCAAiB;;;;;;;0CAC/B,8OAAC;;oCAAG;kDAAE,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;0CAC9B,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/resume/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useForm, FormProvider } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport PersonalInfoStep from '@/components/resume-form/PersonalInfoStep'\nimport EducationStep from '@/components/resume-form/EducationStep'\nimport ExperienceStep from '@/components/resume-form/ExperienceStep'\nimport SkillsStep from '@/components/resume-form/SkillsStep'\nimport LanguagesStep from '@/components/resume-form/LanguagesStep'\nimport TemplateStep from '@/components/resume-form/TemplateStep'\n\nconst resumeSchema = z.object({\n  // Personal Information\n  title: z.string().min(1, 'Resume title is required'),\n  fullName: z.string().min(1, 'Full name is required'),\n  email: z.string().email('Invalid email address'),\n  phone: z.string().optional(),\n  address: z.string().optional(),\n  profileImage: z.string().optional(),\n  summary: z.string().optional(),\n  \n  // Education\n  education: z.array(z.object({\n    institution: z.string().min(1, 'Institution is required'),\n    degree: z.string().min(1, 'Degree is required'),\n    fieldOfStudy: z.string().optional(),\n    startDate: z.string().min(1, 'Start date is required'),\n    endDate: z.string().optional(),\n    current: z.boolean().default(false),\n    description: z.string().optional(),\n  })).default([]),\n  \n  // Experience\n  experience: z.array(z.object({\n    company: z.string().min(1, 'Company is required'),\n    position: z.string().min(1, 'Position is required'),\n    location: z.string().optional(),\n    startDate: z.string().min(1, 'Start date is required'),\n    endDate: z.string().optional(),\n    current: z.boolean().default(false),\n    description: z.string().optional(),\n  })).default([]),\n  \n  // Skills\n  skills: z.array(z.object({\n    name: z.string().min(1, 'Skill name is required'),\n    level: z.string().optional(),\n  })).default([]),\n  \n  // Languages\n  languages: z.array(z.object({\n    name: z.string().min(1, 'Language name is required'),\n    level: z.string().min(1, 'Language level is required'),\n  })).default([]),\n  \n  // Template\n  template: z.string().default('modern'),\n})\n\nexport type ResumeFormData = z.infer<typeof resumeSchema>\n\nconst steps = [\n  { id: 1, name: 'Personal Info', component: PersonalInfoStep },\n  { id: 2, name: 'Education', component: EducationStep },\n  { id: 3, name: 'Experience', component: ExperienceStep },\n  { id: 4, name: 'Skills', component: SkillsStep },\n  { id: 5, name: 'Languages', component: LanguagesStep },\n  { id: 6, name: 'Template', component: TemplateStep },\n]\n\nexport default function CreateResumePage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const methods = useForm<ResumeFormData>({\n    resolver: zodResolver(resumeSchema),\n    defaultValues: {\n      title: '',\n      fullName: '',\n      email: session?.user?.email || '',\n      phone: '',\n      address: '',\n      profileImage: '',\n      summary: '',\n      education: [],\n      experience: [],\n      skills: [],\n      languages: [],\n      template: 'modern',\n    },\n  })\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    router.push('/auth/signin')\n    return null\n  }\n\n  const handleNext = async () => {\n    const currentStepFields = getCurrentStepFields()\n    const isValid = await methods.trigger(currentStepFields)\n    \n    if (isValid) {\n      if (currentStep < steps.length) {\n        setCurrentStep(currentStep + 1)\n      } else {\n        await handleSubmit()\n      }\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const getCurrentStepFields = (): (keyof ResumeFormData)[] => {\n    switch (currentStep) {\n      case 1:\n        return ['title', 'fullName', 'email']\n      case 2:\n        return ['education']\n      case 3:\n        return ['experience']\n      case 4:\n        return ['skills']\n      case 5:\n        return ['languages']\n      case 6:\n        return ['template']\n      default:\n        return []\n    }\n  }\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true)\n    \n    try {\n      const formData = methods.getValues()\n      \n      // Create the resume\n      const resumeResponse = await fetch('/api/resume', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          title: formData.title,\n          fullName: formData.fullName,\n          email: formData.email,\n          phone: formData.phone,\n          address: formData.address,\n          profileImage: formData.profileImage,\n          summary: formData.summary,\n          template: formData.template,\n        }),\n      })\n\n      if (!resumeResponse.ok) {\n        throw new Error('Failed to create resume')\n      }\n\n      const resume = await resumeResponse.json()\n\n      // Add education, experience, skills, and languages\n      await Promise.all([\n        ...formData.education.map(edu => \n          fetch(`/api/resume/${resume.id}/education`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(edu),\n          })\n        ),\n        ...formData.experience.map(exp => \n          fetch(`/api/resume/${resume.id}/experience`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(exp),\n          })\n        ),\n        ...formData.skills.map(skill => \n          fetch(`/api/resume/${resume.id}/skills`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(skill),\n          })\n        ),\n        ...formData.languages.map(lang => \n          fetch(`/api/resume/${resume.id}/languages`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(lang),\n          })\n        ),\n      ])\n\n      router.push(`/resume/preview/${resume.id}`)\n    } catch (error) {\n      console.error('Error creating resume:', error)\n      alert('Failed to create resume. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const CurrentStepComponent = steps[currentStep - 1].component\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <h1 className=\"text-2xl font-bold text-indigo-600\">Create Resume</h1>\n            <div className=\"text-sm text-gray-500\">\n              Step {currentStep} of {steps.length}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Progress Bar */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-4\">\n            <div className=\"flex items-center\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div\n                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${\n                      currentStep >= step.id\n                        ? 'bg-indigo-600 text-white'\n                        : 'bg-gray-300 text-gray-500'\n                    }`}\n                  >\n                    {step.id}\n                  </div>\n                  <span\n                    className={`ml-2 text-sm font-medium ${\n                      currentStep >= step.id ? 'text-indigo-600' : 'text-gray-500'\n                    }`}\n                  >\n                    {step.name}\n                  </span>\n                  {index < steps.length - 1 && (\n                    <div\n                      className={`ml-4 w-16 h-0.5 ${\n                        currentStep > step.id ? 'bg-indigo-600' : 'bg-gray-300'\n                      }`}\n                    />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <main className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <FormProvider {...methods}>\n          <form className=\"space-y-6\">\n            <CurrentStepComponent />\n            \n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6\">\n              <button\n                type=\"button\"\n                onClick={handlePrevious}\n                disabled={currentStep === 1}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleNext}\n                disabled={isSubmitting}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isSubmitting ? 'Creating...' : currentStep === steps.length ? 'Create Resume' : 'Next'}\n              </button>\n            </div>\n          </form>\n        </FormProvider>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeA,MAAM,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,uBAAuB;IACvB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE5B,YAAY;IACZ,WAAW,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC1B,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC/B,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACjC,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,IAAI,OAAO,CAAC,EAAE;IAEd,aAAa;IACb,YAAY,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC3B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC3B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC5B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,IAAI,OAAO,CAAC,EAAE;IAEd,SAAS;IACT,QAAQ,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,IAAI,OAAO,CAAC,EAAE;IAEd,YAAY;IACZ,WAAW,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC1B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,IAAI,OAAO,CAAC,EAAE;IAEd,WAAW;IACX,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAC/B;AAIA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,MAAM;QAAiB,WAAW,wJAAA,CAAA,UAAgB;IAAC;IAC5D;QAAE,IAAI;QAAG,MAAM;QAAa,WAAW,qJAAA,CAAA,UAAa;IAAC;IACrD;QAAE,IAAI;QAAG,MAAM;QAAc,WAAW,sJAAA,CAAA,UAAc;IAAC;IACvD;QAAE,IAAI;QAAG,MAAM;QAAU,WAAW,kJAAA,CAAA,UAAU;IAAC;IAC/C;QAAE,IAAI;QAAG,MAAM;QAAa,WAAW,qJAAA,CAAA,UAAa;IAAC;IACrD;QAAE,IAAI;QAAG,MAAM;QAAY,WAAW,oJAAA,CAAA,UAAY;IAAC;CACpD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkB;QACtC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;YACV,OAAO,SAAS,MAAM,SAAS;YAC/B,OAAO;YACP,SAAS;YACT,cAAc;YACd,SAAS;YACT,WAAW,EAAE;YACb,YAAY,EAAE;YACd,QAAQ,EAAE;YACV,WAAW,EAAE;YACb,UAAU;QACZ;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,MAAM,oBAAoB;QAC1B,MAAM,UAAU,MAAM,QAAQ,OAAO,CAAC;QAEtC,IAAI,SAAS;YACX,IAAI,cAAc,MAAM,MAAM,EAAE;gBAC9B,eAAe,cAAc;YAC/B,OAAO;gBACL,MAAM;YACR;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC;oBAAS;oBAAY;iBAAQ;YACvC,KAAK;gBACH,OAAO;oBAAC;iBAAY;YACtB,KAAK;gBACH,OAAO;oBAAC;iBAAa;YACvB,KAAK;gBACH,OAAO;oBAAC;iBAAS;YACnB,KAAK;gBACH,OAAO;oBAAC;iBAAY;YACtB,KAAK;gBACH,OAAO;oBAAC;iBAAW;YACrB;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,QAAQ,SAAS;YAElC,oBAAoB;YACpB,MAAM,iBAAiB,MAAM,MAAM,eAAe;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,cAAc,SAAS,YAAY;oBACnC,SAAS,SAAS,OAAO;oBACzB,UAAU,SAAS,QAAQ;gBAC7B;YACF;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,eAAe,IAAI;YAExC,mDAAmD;YACnD,MAAM,QAAQ,GAAG,CAAC;mBACb,SAAS,SAAS,CAAC,GAAG,CAAC,CAAA,MACxB,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;wBAC1C,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;oBACvB;mBAEC,SAAS,UAAU,CAAC,GAAG,CAAC,CAAA,MACzB,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;wBAC3C,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;oBACvB;mBAEC,SAAS,MAAM,CAAC,GAAG,CAAC,CAAA,QACrB,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,EAAE;wBACvC,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;oBACvB;mBAEC,SAAS,SAAS,CAAC,GAAG,CAAC,CAAA,OACxB,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;wBAC1C,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;oBACvB;aAEH;YAED,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,uBAAuB,KAAK,CAAC,cAAc,EAAE,CAAC,SAAS;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;;oCAAwB;oCAC/B;oCAAY;oCAAK,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CACC,WAAW,CAAC,0EAA0E,EACpF,eAAe,KAAK,EAAE,GAClB,6BACA,6BACJ;sDAED,KAAK,EAAE;;;;;;sDAEV,8OAAC;4CACC,WAAW,CAAC,yBAAyB,EACnC,eAAe,KAAK,EAAE,GAAG,oBAAoB,iBAC7C;sDAED,KAAK,IAAI;;;;;;wCAEX,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CACC,WAAW,CAAC,gBAAgB,EAC1B,cAAc,KAAK,EAAE,GAAG,kBAAkB,eAC1C;;;;;;;mCArBE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0BAgC3B,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,8JAAA,CAAA,eAAY;oBAAE,GAAG,OAAO;8BACvB,cAAA,8OAAC;wBAAK,WAAU;;0CACd,8OAAC;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,gBAAgB;wCAC1B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,gBAAgB,gBAAgB,MAAM,MAAM,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjG", "debugId": null}}]}