'use client'

import { GoogleAnalytics, analytics } from './google-analytics'
import { FacebookPixel, fbPixel } from './facebook-pixel'

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <GoogleAnalytics />
      <FacebookPixel />
      {children}
    </>
  )
}

// Unified analytics interface
export const trackEvent = {
  // User authentication events
  signUp: (method: string = 'email') => {
    analytics.signUp(method)
    fbPixel.completeRegistration()
  },

  login: (method: string = 'email') => {
    analytics.login(method)
  },

  // Resume creation events
  resumeCreated: (template: string) => {
    analytics.resumeCreated(template)
    fbPixel.addToCart(`Resume - ${template}`)
  },

  // PDF download events
  pdfDownload: (resumeId: string, template: string) => {
    analytics.pdfDownload(resumeId, template)
  },

  // Subscription events
  subscriptionStarted: (plan: string, value: number) => {
    fbPixel.initiateCheckout(value)
  },

  subscriptionCompleted: (plan: string, value: number) => {
    analytics.subscribe(plan, value)
    fbPixel.purchase(value)
  },

  // Template interaction events
  templateViewed: (template: string, isPremium: boolean) => {
    analytics.templateSelected(template, isPremium)
    fbPixel.viewContent(`Template - ${template}`, 'template')
  },

  // Form progression events
  formStepCompleted: (step: number, stepName: string) => {
    analytics.formStep(step, stepName)
  },

  // Lead generation events
  leadGenerated: () => {
    fbPixel.lead()
  },

  // Error tracking
  error: (errorMessage: string, errorLocation: string) => {
    analytics.error(errorMessage, errorLocation)
  },

  // Custom events
  custom: (eventName: string, parameters?: Record<string, any>) => {
    analytics.event(eventName, parameters)
    fbPixel.event(eventName, parameters)
  },
}
