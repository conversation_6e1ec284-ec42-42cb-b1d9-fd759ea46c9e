'use client'

import { ResumeData } from '@/types/resume'

interface ResumeTipsProps {
  data: ResumeData
  className?: string
}

interface Tip {
  id: string
  title: string
  description: string
  category: 'content' | 'formatting' | 'ats' | 'strategy'
  priority: 'high' | 'medium' | 'low'
  icon: string
}

export function ResumeTips({ data, className = '' }: ResumeTipsProps) {
  const generateTips = (resumeData: ResumeData): Tip[] => {
    const tips: Tip[] = []

    // Content tips
    if (!resumeData.personalInfo.summary || resumeData.personalInfo.summary.length < 100) {
      tips.push({
        id: 'summary-length',
        title: 'Strengthen Your Professional Summary',
        description: 'Write a compelling 2-3 sentence summary that highlights your key achievements and career goals. Aim for 100-300 characters.',
        category: 'content',
        priority: 'high',
        icon: '📝'
      })
    }

    if (resumeData.experience.length === 0) {
      tips.push({
        id: 'add-experience',
        title: 'Add Work Experience',
        description: 'Include your relevant work experience with specific achievements and quantifiable results.',
        category: 'content',
        priority: 'high',
        icon: '💼'
      })
    }

    // Check for quantifiable achievements
    const hasQuantifiableResults = resumeData.experience.some(exp => 
      exp.description && /\d+%|\$\d+|\d+\+|increased|decreased|improved|reduced/i.test(exp.description)
    )
    
    if (!hasQuantifiableResults && resumeData.experience.length > 0) {
      tips.push({
        id: 'quantify-achievements',
        title: 'Quantify Your Achievements',
        description: 'Use numbers, percentages, and metrics to demonstrate your impact. For example: "Increased sales by 25%" or "Managed team of 10 people".',
        category: 'content',
        priority: 'high',
        icon: '📊'
      })
    }

    // Skills tips
    if (resumeData.skills.length < 5) {
      tips.push({
        id: 'add-more-skills',
        title: 'Add More Relevant Skills',
        description: 'Include 5-10 skills that are relevant to your target job. Mix technical and soft skills.',
        category: 'content',
        priority: 'medium',
        icon: '🛠️'
      })
    }

    // ATS optimization tips
    const hasKeywords = resumeData.skills.some(skill => 
      ['JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'AWS', 'Docker'].some(keyword =>
        skill.name.toLowerCase().includes(keyword.toLowerCase())
      )
    )

    if (!hasKeywords) {
      tips.push({
        id: 'ats-keywords',
        title: 'Optimize for ATS Systems',
        description: 'Include industry-specific keywords and technical terms that match job descriptions in your field.',
        category: 'ats',
        priority: 'high',
        icon: '🤖'
      })
    }

    // Formatting tips
    tips.push({
      id: 'consistent-formatting',
      title: 'Maintain Consistent Formatting',
      description: 'Use consistent date formats, bullet points, and spacing throughout your resume.',
      category: 'formatting',
      priority: 'medium',
      icon: '📐'
    })

    // Strategy tips
    if (resumeData.experience.length > 0) {
      const oldestExperience = resumeData.experience.reduce((oldest, current) => {
        const oldestDate = new Date(oldest.startDate + '-01')
        const currentDate = new Date(current.startDate + '-01')
        return currentDate < oldestDate ? current : oldest
      })

      const yearsAgo = (new Date().getFullYear() - new Date(oldestExperience.startDate + '-01').getFullYear())
      
      if (yearsAgo > 10) {
        tips.push({
          id: 'limit-experience',
          title: 'Focus on Recent Experience',
          description: 'Consider limiting your experience to the last 10-15 years unless older experience is highly relevant.',
          category: 'strategy',
          priority: 'low',
          icon: '⏰'
        })
      }
    }

    // Education tips
    if (resumeData.education.length === 0) {
      tips.push({
        id: 'add-education',
        title: 'Include Your Education',
        description: 'Add your educational background, including degrees, certifications, and relevant coursework.',
        category: 'content',
        priority: 'medium',
        icon: '🎓'
      })
    }

    // Contact information tips
    if (!resumeData.personalInfo.linkedin) {
      tips.push({
        id: 'add-linkedin',
        title: 'Add LinkedIn Profile',
        description: 'Include your LinkedIn profile URL to provide recruiters with more information about your background.',
        category: 'content',
        priority: 'medium',
        icon: '🔗'
      })
    }

    return tips.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  const tips = generateTips(data)

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'content': return 'bg-blue-100 text-blue-800'
      case 'formatting': return 'bg-green-100 text-green-800'
      case 'ats': return 'bg-purple-100 text-purple-800'
      case 'strategy': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500'
      case 'medium': return 'border-yellow-500'
      case 'low': return 'border-green-500'
      default: return 'border-gray-300'
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Resume Improvement Tips</h2>
        <p className="text-gray-600">
          Personalized suggestions to make your resume stand out
        </p>
      </div>

      {tips.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎉</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Great job!</h3>
          <p className="text-gray-600">Your resume looks comprehensive. Keep it updated with your latest achievements.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {tips.map((tip) => (
            <div 
              key={tip.id} 
              className={`border-l-4 ${getPriorityColor(tip.priority)} bg-gray-50 p-4 rounded-r-lg`}
            >
              <div className="flex items-start gap-3">
                <span className="text-2xl">{tip.icon}</span>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{tip.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(tip.category)}`}>
                      {tip.category.charAt(0).toUpperCase() + tip.category.slice(1)}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      tip.priority === 'high' ? 'bg-red-100 text-red-800' :
                      tip.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {tip.priority.charAt(0).toUpperCase() + tip.priority.slice(1)} Priority
                    </span>
                  </div>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {tip.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">💡 Pro Tip</h3>
        <p className="text-blue-800 text-sm">
          Tailor your resume for each job application by incorporating keywords from the job description 
          and highlighting the most relevant experience and skills.
        </p>
      </div>
    </div>
  )
}
