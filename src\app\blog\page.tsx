import Link from 'next/link'
import { Navigation } from '@/components/navigation'
import { createMetadata } from '@/lib/seo'

export const metadata = createMetadata({
  title: 'Resume Tips & Career Advice Blog',
  description: 'Expert tips on resume writing, job searching, career development, and interview preparation. Get the latest insights to advance your career.'
})

// Sample blog posts - in production, this would come from a CMS or database
const blogPosts = [
  {
    id: '1',
    title: 'How to Write a Resume That Gets Past ATS Systems',
    excerpt: 'Learn the secrets to creating an ATS-friendly resume that gets noticed by both robots and humans.',
    author: 'CVCraft Team',
    publishedAt: '2024-01-15',
    readTime: '5 min read',
    category: 'Resume Tips',
    image: '/blog/ats-resume-tips.jpg'
  },
  {
    id: '2',
    title: '10 Common Resume Mistakes That Cost You Job Interviews',
    excerpt: 'Avoid these critical resume mistakes that could be preventing you from landing your dream job.',
    author: '<PERSON>',
    publishedAt: '2024-01-10',
    readTime: '7 min read',
    category: 'Resume Tips',
    image: '/blog/resume-mistakes.jpg'
  },
  {
    id: '3',
    title: 'The Ultimate Guide to Resume Templates in 2024',
    excerpt: 'Discover which resume template styles work best for different industries and career levels.',
    author: '<PERSON> Chen',
    publishedAt: '2024-01-05',
    readTime: '8 min read',
    category: 'Templates',
    image: '/blog/resume-templates-guide.jpg'
  },
  {
    id: '4',
    title: 'How to Tailor Your Resume for Different Job Applications',
    excerpt: 'Master the art of customizing your resume for each job application to increase your success rate.',
    author: 'Emily Davis',
    publishedAt: '2024-01-01',
    readTime: '6 min read',
    category: 'Job Search',
    image: '/blog/tailor-resume.jpg'
  }
]

const categories = ['All', 'Resume Tips', 'Templates', 'Job Search', 'Career Advice', 'Interview Tips']

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Resume Tips & Career Advice
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Expert insights, tips, and strategies to help you create better resumes and advance your career
          </p>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-colors"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">Blog Image</span>
              </div>
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    {post.category}
                  </span>
                  <span className="text-gray-500 text-sm">{post.readTime}</span>
                </div>
                
                <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                  <Link href={`/blog/${post.id}`} className="hover:text-blue-600">
                    {post.title}
                  </Link>
                </h2>
                
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>By {post.author}</span>
                  <time dateTime={post.publishedAt}>
                    {new Date(post.publishedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-blue-600 rounded-lg p-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated with Career Tips
          </h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Get the latest resume tips, job search strategies, and career advice delivered to your inbox weekly.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-md border-0 focus:ring-2 focus:ring-blue-300"
            />
            <button className="bg-white text-blue-600 px-6 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
