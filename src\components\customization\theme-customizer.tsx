'use client'

import { useState } from 'react'

interface ThemeCustomizerProps {
  onThemeChange: (theme: CustomTheme) => void
  currentTheme: CustomTheme
  isPremium: boolean
  className?: string
}

export interface CustomTheme {
  primaryColor: string
  secondaryColor: string
  textColor: string
  backgroundColor: string
  fontFamily: string
  fontSize: string
  lineHeight: string
  sectionSpacing: string
}

const defaultTheme: CustomTheme = {
  primaryColor: '#2563eb',
  secondaryColor: '#64748b',
  textColor: '#1f2937',
  backgroundColor: '#ffffff',
  fontFamily: 'Inter',
  fontSize: '14px',
  lineHeight: '1.5',
  sectionSpacing: '24px'
}

const colorPresets = [
  { name: 'Blue', primary: '#2563eb', secondary: '#64748b' },
  { name: 'Green', primary: '#059669', secondary: '#6b7280' },
  { name: '<PERSON>', primary: '#7c3aed', secondary: '#6b7280' },
  { name: 'Red', primary: '#dc2626', secondary: '#6b7280' },
  { name: 'Orange', primary: '#ea580c', secondary: '#6b7280' },
  { name: 'Teal', primary: '#0d9488', secondary: '#6b7280' }
]

const fontOptions = [
  { name: 'Inter', value: 'Inter' },
  { name: 'Roboto', value: 'Roboto' },
  { name: 'Open Sans', value: 'Open Sans' },
  { name: 'Lato', value: 'Lato' },
  { name: 'Source Sans Pro', value: 'Source Sans Pro' },
  { name: 'Montserrat', value: 'Montserrat' }
]

export function ThemeCustomizer({ onThemeChange, currentTheme, isPremium, className = '' }: ThemeCustomizerProps) {
  const [theme, setTheme] = useState<CustomTheme>(currentTheme)
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'spacing'>('colors')

  const updateTheme = (updates: Partial<CustomTheme>) => {
    const newTheme = { ...theme, ...updates }
    setTheme(newTheme)
    onThemeChange(newTheme)
  }

  const resetToDefault = () => {
    setTheme(defaultTheme)
    onThemeChange(defaultTheme)
  }

  if (!isPremium) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Premium Feature</h3>
          <p className="text-gray-600 mb-4">
            Customize colors, fonts, and spacing with CVCraft Premium
          </p>
          <button className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-md font-medium">
            Upgrade to Premium
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Customize Your Resume</h2>
        <p className="text-gray-600">Personalize the look and feel of your resume</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        {[
          { id: 'colors', label: 'Colors', icon: '🎨' },
          { id: 'typography', label: 'Typography', icon: '📝' },
          { id: 'spacing', label: 'Spacing', icon: '📐' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center gap-2 px-4 py-2 border-b-2 font-medium text-sm ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Colors Tab */}
      {activeTab === 'colors' && (
        <div className="space-y-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Color Presets</h3>
            <div className="grid grid-cols-3 gap-3">
              {colorPresets.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => updateTheme({ 
                    primaryColor: preset.primary, 
                    secondaryColor: preset.secondary 
                  })}
                  className="flex items-center gap-2 p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex gap-1">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.primary }}
                    />
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.secondary }}
                    />
                  </div>
                  <span className="text-sm">{preset.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Color
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={theme.primaryColor}
                  onChange={(e) => updateTheme({ primaryColor: e.target.value })}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={theme.primaryColor}
                  onChange={(e) => updateTheme({ primaryColor: e.target.value })}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secondary Color
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={theme.secondaryColor}
                  onChange={(e) => updateTheme({ secondaryColor: e.target.value })}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={theme.secondaryColor}
                  onChange={(e) => updateTheme({ secondaryColor: e.target.value })}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Typography Tab */}
      {activeTab === 'typography' && (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Font Family
            </label>
            <select
              value={theme.fontFamily}
              onChange={(e) => updateTheme({ fontFamily: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              {fontOptions.map((font) => (
                <option key={font.value} value={font.value}>
                  {font.name}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Size
              </label>
              <select
                value={theme.fontSize}
                onChange={(e) => updateTheme({ fontSize: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="12px">Small (12px)</option>
                <option value="14px">Medium (14px)</option>
                <option value="16px">Large (16px)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Line Height
              </label>
              <select
                value={theme.lineHeight}
                onChange={(e) => updateTheme({ lineHeight: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="1.3">Tight (1.3)</option>
                <option value="1.5">Normal (1.5)</option>
                <option value="1.7">Relaxed (1.7)</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Spacing Tab */}
      {activeTab === 'spacing' && (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Spacing
            </label>
            <select
              value={theme.sectionSpacing}
              onChange={(e) => updateTheme({ sectionSpacing: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="16px">Compact (16px)</option>
              <option value="24px">Normal (24px)</option>
              <option value="32px">Spacious (32px)</option>
            </select>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={resetToDefault}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 text-sm font-medium"
        >
          Reset to Default
        </button>
        <div className="text-sm text-gray-500">
          Changes are applied automatically
        </div>
      </div>
    </div>
  )
}
