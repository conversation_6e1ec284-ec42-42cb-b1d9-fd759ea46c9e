import { TemplateType } from '@/types/resume'


interface TemplateSelectorProps {
  selectedTemplate: TemplateType
  onTemplateChange: (template: TemplateType) => void
  isPremium?: boolean
}

const templates = [
  {
    id: 'modern' as TemplateType,
    name: 'Modern',
    description: 'Clean and professional with blue accents',
    isPremium: false,
    preview: '/template-previews/modern.png'
  },
  {
    id: 'elegant' as TemplateType,
    name: 'Elegant',
    description: 'Sophisticated sidebar layout with dark theme',
    isPremium: false,
    preview: '/template-previews/elegant.png'
  },
  {
    id: 'minimal' as TemplateType,
    name: 'Minimal',
    description: 'Simple and clean centered layout',
    isPremium: false,
    preview: '/template-previews/minimal.png'
  }
]

export function TemplateSelector({ selectedTemplate, onTemplateChange, isPremium = false }: TemplateSelectorProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Choose Template</h3>

        <div className="space-y-4">
          {templates.map((template) => {
            const isLocked = false // Everything is free now!
            const isSelected = selectedTemplate === template.id

            return (
              <div
                key={template.id}
                className={`relative border-2 rounded-lg p-4 transition-all duration-200 ${
                  isLocked ? 'cursor-not-allowed opacity-60' : 'cursor-pointer hover:shadow-md'
                } ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 shadow-md'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                }`}
                onClick={() => !isLocked && onTemplateChange(template.id)}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-3 right-3">
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}

                {/* Lock indicator */}
                {isLocked && (
                  <div className="absolute top-3 right-3">
                    <div className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}

                {/* Template preview */}
                <div className="aspect-[4/3] bg-gray-100 dark:bg-gray-600 rounded-md mb-3 flex items-center justify-center transition-colors duration-200">
                  <span className="text-gray-500 dark:text-gray-300 text-sm font-medium">Preview</span>
                </div>

                {/* Template info */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-gray-900 dark:text-white">{template.name}</h4>
                    {template.isPremium && (
                      <span className="bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-xs font-medium px-2 py-1 rounded-full">
                        Premium
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">{template.description}</p>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Premium upgrade notice */}
      {!isPremium && (
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-yellow-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-semibold text-yellow-800">Upgrade to Premium</h4>
              <p className="text-sm text-yellow-700 mt-1 leading-relaxed">
                Unlock premium templates and create unlimited resumes.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
