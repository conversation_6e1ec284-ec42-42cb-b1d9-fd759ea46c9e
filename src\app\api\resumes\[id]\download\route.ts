import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { TemplateType } from '@/types/resume'
import puppeteer from 'puppeteer'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const resume = await prisma.resume.findFirst({
      where: {
        id: id,
        userId: session.user.id
      }
    })

    if (!resume) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    // Generate PDF using Puppeteer - renders the actual HTML preview
    const printUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/resume/print?template=modern&data=${encodeURIComponent(JSON.stringify(resume))}`

    console.log('Generating PDF from URL:', printUrl)

    // Launch Puppeteer with better error handling
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    }).catch(error => {
      console.error('Failed to launch Puppeteer:', error)
      throw new Error('PDF generation service unavailable')
    })

    const page = await browser.newPage()

    // Set viewport for consistent rendering
    await page.setViewport({ width: 1200, height: 1600 })

    // Navigate to the print page with better error handling
    try {
      await page.goto(printUrl, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })
    } catch (navigationError) {
      console.error('Failed to navigate to print page:', navigationError)
      await browser.close()
      throw new Error('Failed to load resume for PDF generation')
    }

    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      }
    })

    await browser.close()

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${resume.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf"`
      }
    })
  } catch (error) {
    console.error('Error generating PDF:', error)
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}
