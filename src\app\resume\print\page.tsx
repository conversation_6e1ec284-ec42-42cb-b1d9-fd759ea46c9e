'use client'

import { useSearchParams } from 'next/navigation'
import { useState, useEffect } from 'react'
import { ModernTemplate } from '@/components/templates/modern-template'
import { ElegantTemplate } from '@/components/templates/elegant-template'
import { MinimalTemplate } from '@/components/templates/minimal-template'
import { ResumeData } from '@/types/resume'

export default function ResumePrintPage() {
  const searchParams = useSearchParams()
  const template = searchParams.get('template') || 'modern'
  const [resumeData, setResumeData] = useState<ResumeData | null>(null)

  useEffect(() => {
    // Get resume data from URL params or localStorage for demo
    const dataParam = searchParams.get('data')
    if (dataParam) {
      try {
        const data = JSON.parse(decodeURIComponent(dataParam))
        setResumeData(data)
      } catch (error) {
        console.error('Error parsing resume data:', error)
        // Fallback to demo data
        setDemoData()
      }
    } else {
      // Use demo data for testing
      setDemoData()
    }
  }, [searchParams])

  const setDemoData = () => {
    setResumeData({
      personalInfo: {
        fullName: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'New York, NY',
        website: 'https://johndoe.com',
        linkedin: 'https://linkedin.com/in/johndoe',
        summary: 'Experienced software developer with a passion for creating innovative solutions. Skilled in modern web technologies and committed to delivering high-quality applications.',
        photo: undefined
      },
      education: [
        {
          id: '1',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          institution: 'University of Technology',
          startDate: '2018-09',
          endDate: '2022-05',
          current: false,
          description: 'Graduated with honors. Focused on software engineering and web development.'
        }
      ],
      experience: [
        {
          id: '1',
          position: 'Senior Software Developer',
          company: 'Tech Solutions Inc.',
          location: 'New York, NY',
          startDate: '2022-06',
          endDate: '',
          current: true,
          description: 'Led development of multiple web applications using React and Node.js. Collaborated with cross-functional teams to deliver projects on time. Mentored junior developers and conducted code reviews.'
        },
        {
          id: '2',
          position: 'Junior Developer',
          company: 'StartupCorp',
          location: 'San Francisco, CA',
          startDate: '2021-01',
          endDate: '2022-05',
          current: false,
          description: 'Developed and maintained web applications using JavaScript and Python. Worked closely with designers to implement user interfaces.'
        }
      ],
      skills: [
        { id: '1', name: 'JavaScript', level: 'Expert' },
        { id: '2', name: 'React', level: 'Expert' },
        { id: '3', name: 'Node.js', level: 'Advanced' },
        { id: '4', name: 'TypeScript', level: 'Advanced' },
        { id: '5', name: 'Python', level: 'Intermediate' }
      ],
      languages: [
        { id: '1', name: 'English', proficiency: 'Native' },
        { id: '2', name: 'Spanish', proficiency: 'Intermediate' }
      ]
    })
  }

  if (!resumeData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading resume...</p>
        </div>
      </div>
    )
  }

  const renderTemplate = () => {
    switch (template) {
      case 'elegant':
        return <ElegantTemplate data={resumeData} />
      case 'minimal':
        return <MinimalTemplate data={resumeData} />
      default:
        return <ModernTemplate data={resumeData} />
    }
  }

  return (
    <>
      {/* Print-specific styles */}
      <style jsx global>{`
        @media print {
          body {
            margin: 0 !important;
            padding: 0 !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          
          @page {
            size: A4;
            margin: 0.5in;
          }
          
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
        }
        
        /* Hide scrollbars for clean PDF */
        body {
          overflow: hidden;
        }
      `}</style>
      
      <div className="print-container">
        {renderTemplate()}
      </div>
    </>
  )
}
