'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Navigation } from '@/components/navigation'
import { PersonalInfoForm } from '@/components/resume-forms/personal-info-form'
import { EducationForm } from '@/components/resume-forms/education-form'
import { ExperienceForm } from '@/components/resume-forms/experience-form'
import { SkillsForm } from '@/components/resume-forms/skills-form'
import { LanguagesForm } from '@/components/resume-forms/languages-form'
import { TemplateSelector } from '@/components/template-selector'
import { TemplateRenderer } from '@/components/template-renderer'
import { Resume, ResumeData, PersonalInfo, Education, Experience, Skill, Language, TemplateType } from '@/types/resume'
import { useSession } from 'next-auth/react'

const steps = [
  { id: 'personal', title: 'Personal Information', component: PersonalInfoForm },
  { id: 'education', title: 'Education', component: EducationForm },
  { id: 'experience', title: 'Work Experience', component: ExperienceForm },
  { id: 'skills', title: 'Skills', component: SkillsForm },
  { id: 'languages', title: 'Languages', component: LanguagesForm }
]

export default function EditResumePage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [resume, setResume] = useState<Resume | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType>('modern')
  const [showPreview, setShowPreview] = useState(false)
  
  const [resumeData, setResumeData] = useState<ResumeData>({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      website: '',
      linkedin: '',
      summary: ''
    },
    education: [],
    experience: [],
    skills: [],
    languages: []
  })

  useEffect(() => {
    fetchResume()
  }, [params.id])

  const fetchResume = async () => {
    try {
      const response = await fetch(`/api/resumes/${params.id}`)
      if (response.ok) {
        const resumeData = await response.json()
        setResume(resumeData)
        setResumeData(resumeData.data)
        setSelectedTemplate(resumeData.template as TemplateType)
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('Error fetching resume:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }

  const updatePersonalInfo = (personalInfo: PersonalInfo) => {
    setResumeData(prev => ({ ...prev, personalInfo }))
  }

  const updateEducation = (education: Education[]) => {
    setResumeData(prev => ({ ...prev, education }))
  }

  const updateExperience = (experience: Experience[]) => {
    setResumeData(prev => ({ ...prev, experience }))
  }

  const updateSkills = (skills: Skill[]) => {
    setResumeData(prev => ({ ...prev, skills }))
  }

  const updateLanguages = (languages: Language[]) => {
    setResumeData(prev => ({ ...prev, languages }))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const saveResume = async () => {
    if (!resume) return

    setSaving(true)
    try {
      const response = await fetch(`/api/resumes/${resume.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: resumeData.personalInfo.fullName ? `${resumeData.personalInfo.fullName}'s Resume` : resume.title,
          data: resumeData,
          template: selectedTemplate
        })
      })

      if (response.ok) {
        router.push(`/resume/${resume.id}/preview`)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to save resume')
      }
    } catch (error) {
      console.error('Error saving resume:', error)
      alert('Failed to save resume')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  if (!resume) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900">Resume not found</h1>
        </div>
      </div>
    )
  }

  const CurrentStepComponent = steps[currentStep].component

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black transition-colors duration-300">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header with Preview Toggle */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Resume</h1>
              <p className="text-gray-600 dark:text-gray-300">{resume.title}</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </button>
              <button
                onClick={() => router.push(`/resume/${resume.id}/preview`)}
                className="bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Full Preview
              </button>
            </div>
          </div>

          <div className={`grid gap-6 ${showPreview ? 'grid-cols-2' : 'grid-cols-1'}`}>
            {/* Form Section */}
            <div className="space-y-6">
              {/* Progress indicator */}
              <div className="mb-8">
                <div className="flex items-center justify-between">
                  {steps.map((step, index) => (
                    <div key={step.id} className="flex items-center">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                      }`}>
                        {index + 1}
                      </div>
                      <span className={`ml-2 text-sm font-medium ${
                        index <= currentStep ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {step.title}
                      </span>
                      {index < steps.length - 1 && (
                        <div className={`w-16 h-0.5 mx-4 ${
                          index < currentStep ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Template Selection */}
              {currentStep === 0 && (
                <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 transition-colors duration-300">
                  <TemplateSelector
                    selectedTemplate={selectedTemplate}
                    onTemplateChange={setSelectedTemplate}
                    isPremium={session?.user?.isPremium}
                  />
                </div>
              )}

              {/* Form content */}
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 transition-colors duration-300">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {steps[currentStep].title}
                </h2>
                
                <CurrentStepComponent
                  data={resumeData}
                  onUpdatePersonalInfo={updatePersonalInfo}
                  onUpdateEducation={updateEducation}
                  onUpdateExperience={updateExperience}
                  onUpdateSkills={updateSkills}
                  onUpdateLanguages={updateLanguages}
                />

                {/* Navigation buttons */}
                <div className="flex justify-between mt-8">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  
                  {currentStep === steps.length - 1 ? (
                    <button
                      onClick={saveResume}
                      disabled={saving}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </button>
                  ) : (
                    <button
                      onClick={nextStep}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                    >
                      Next
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Preview Section */}
            {showPreview && (
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Live Preview</h2>
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="transform scale-75 origin-top-left" style={{ width: '133.33%' }}>
                    <TemplateRenderer
                      data={resumeData}
                      template={selectedTemplate}
                      className="shadow-none"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
