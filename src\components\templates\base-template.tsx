import { ResumeData } from '@/types/resume'

export interface TemplateProps {
  data: ResumeData
  className?: string
  isPrintMode?: boolean
}

export function formatDate(dateString: string): string {
  if (!dateString) return ''
  const date = new Date(dateString + '-01')
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short' 
  })
}

export function formatDateRange(startDate: string, endDate: string, current: boolean): string {
  const start = formatDate(startDate)
  if (current) {
    return `${start} - Present`
  }
  const end = formatDate(endDate)
  return `${start} - ${end}`
}
