import { TemplateProps, formatDateRange } from './base-template'
import Image from 'next/image'

export function MinimalTemplate({ data, className = '', isPrintMode = false }: TemplateProps) {
  const { personalInfo, education, experience, skills, languages } = data

  const containerClasses = isPrintMode
    ? 'bg-white p-6 w-full h-full'
    : `bg-white p-8 max-w-4xl mx-auto ${className}`

  return (
    <div className={containerClasses}>
      {/* Header */}
      <div className="text-center mb-12">
        {/* Profile Photo */}
        {personalInfo.photo && (
          <div className="mb-6">
            <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-300 mx-auto">
              <Image
                src={personalInfo.photo}
                alt={personalInfo.fullName}
                width={96}
                height={96}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        <h1 className="text-3xl font-light text-gray-900 mb-4">
          {personalInfo.fullName}
        </h1>
        <div className="flex justify-center flex-wrap gap-6 text-sm text-gray-600">
          {personalInfo.email && <span>{personalInfo.email}</span>}
          {personalInfo.phone && <span>{personalInfo.phone}</span>}
          {personalInfo.location && <span>{personalInfo.location}</span>}
          {personalInfo.website && <span>{personalInfo.website}</span>}
          {personalInfo.linkedin && <span>LinkedIn</span>}
        </div>
      </div>

      {/* Summary */}
      {personalInfo.summary && (
        <div className="mb-10">
          <p className="text-gray-700 leading-relaxed text-center italic">
            {personalInfo.summary}
          </p>
        </div>
      )}

      {/* Experience */}
      {experience.length > 0 && (
        <div className="mb-10">
          <h2 className="text-lg font-light text-gray-900 mb-6 text-center uppercase tracking-wider">
            Experience
          </h2>
          <div className="space-y-8">
            {experience.map((exp) => (
              <div key={exp.id} className="text-center">
                <h3 className="font-medium text-gray-900 text-lg">{exp.position}</h3>
                <p className="text-gray-600 mb-1">{exp.company}</p>
                <p className="text-gray-500 text-sm mb-3">
                  {formatDateRange(exp.startDate, exp.endDate, exp.current)}
                  {exp.location && ` • ${exp.location}`}
                </p>
                <div className="text-gray-700 text-sm leading-relaxed max-w-2xl mx-auto whitespace-pre-line">
                  {exp.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Education */}
      {education.length > 0 && (
        <div className="mb-10">
          <h2 className="text-lg font-light text-gray-900 mb-6 text-center uppercase tracking-wider">
            Education
          </h2>
          <div className="space-y-6">
            {education.map((edu) => (
              <div key={edu.id} className="text-center">
                <h3 className="font-medium text-gray-900">{edu.degree} in {edu.field}</h3>
                <p className="text-gray-600">{edu.institution}</p>
                <p className="text-gray-500 text-sm">
                  {formatDateRange(edu.startDate, edu.endDate, edu.current)}
                </p>
                {edu.description && (
                  <p className="text-gray-700 text-sm mt-2 max-w-xl mx-auto">{edu.description}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        {/* Skills */}
        {skills.length > 0 && (
          <div>
            <h2 className="text-lg font-light text-gray-900 mb-6 text-center uppercase tracking-wider">
              Skills
            </h2>
            <div className="space-y-3">
              {skills.map((skill) => (
                <div key={skill.id} className="text-center">
                  <span className="text-gray-900">{skill.name}</span>
                  <div className="text-gray-500 text-xs mt-1">{skill.level}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Languages */}
        {languages.length > 0 && (
          <div>
            <h2 className="text-lg font-light text-gray-900 mb-6 text-center uppercase tracking-wider">
              Languages
            </h2>
            <div className="space-y-3">
              {languages.map((lang) => (
                <div key={lang.id} className="text-center">
                  <span className="text-gray-900">{lang.name}</span>
                  <div className="text-gray-500 text-xs mt-1">{lang.proficiency}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
