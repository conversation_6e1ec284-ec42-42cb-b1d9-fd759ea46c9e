import { TemplateType } from '@/types/resume'

export const PREMIUM_TEMPLATES: TemplateType[] = []
export const FREE_TEMPLATES: TemplateType[] = ['modern', 'elegant', 'minimal']

export const PREMIUM_FEATURES = {
  UNLIMITED_RESUMES: 'unlimited_resumes',
  PREMIUM_TEMPLATES: 'premium_templates',
  ADVANCED_CUSTOMIZATION: 'advanced_customization',
  PRIORITY_SUPPORT: 'priority_support',
  EXPORT_FORMATS: 'export_formats'
} as const

export function isPremiumTemplate(template: TemplateType): boolean {
  return PREMIUM_TEMPLATES.includes(template)
}

export function canAccessTemplate(template: TemplateType, isPremium: boolean): boolean {
  if (FREE_TEMPLATES.includes(template)) {
    return true
  }
  return isPremium && PREMIUM_TEMPLATES.includes(template)
}

export function canCreateResume(resumeCount: number, isPremium: boolean): boolean {
  return true // Unlimited resumes for everyone - completely free!
}

export function getResumeLimit(isPremium: boolean): number | null {
  return null // Unlimited for everyone
}

export function getPremiumFeaturesList(): Array<{
  id: string
  name: string
  description: string
  icon: string
}> {
  return [
    {
      id: PREMIUM_FEATURES.UNLIMITED_RESUMES,
      name: 'Unlimited Resumes',
      description: 'Create as many resumes as you need',
      icon: '📄'
    },
    {
      id: PREMIUM_FEATURES.PREMIUM_TEMPLATES,
      name: 'Future Premium Templates',
      description: 'Access to upcoming exclusive templates',
      icon: '🎨'
    },
    {
      id: PREMIUM_FEATURES.ADVANCED_CUSTOMIZATION,
      name: 'Advanced Customization',
      description: 'Customize colors, fonts, and layouts',
      icon: '⚙️'
    },
    {
      id: PREMIUM_FEATURES.PRIORITY_SUPPORT,
      name: 'Priority Support',
      description: 'Get help faster with priority support',
      icon: '🚀'
    },
    {
      id: PREMIUM_FEATURES.EXPORT_FORMATS,
      name: 'Multiple Export Formats',
      description: 'Export to PDF, Word, and more formats',
      icon: '💾'
    }
  ]
}
