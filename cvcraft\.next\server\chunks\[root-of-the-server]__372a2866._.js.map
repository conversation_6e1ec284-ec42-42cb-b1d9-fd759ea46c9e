{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n      }\n      return session\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC5B;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/api/resume/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\nconst updateResumeSchema = z.object({\n  title: z.string().min(1, 'Title is required').optional(),\n  fullName: z.string().min(1, 'Full name is required').optional(),\n  email: z.string().email('Invalid email').optional(),\n  phone: z.string().optional(),\n  address: z.string().optional(),\n  profileImage: z.string().optional(),\n  summary: z.string().optional(),\n  template: z.string().optional(),\n})\n\n// GET /api/resume/[id] - Get a specific resume\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const resume = await prisma.resume.findFirst({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n      include: {\n        education: {\n          orderBy: { createdAt: 'asc' },\n        },\n        experience: {\n          orderBy: { createdAt: 'asc' },\n        },\n        skills: {\n          orderBy: { createdAt: 'asc' },\n        },\n        languages: {\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    })\n\n    if (!resume) {\n      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })\n    }\n\n    return NextResponse.json(resume)\n  } catch (error) {\n    console.error('Error fetching resume:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/resume/[id] - Update a specific resume\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const data = updateResumeSchema.parse(body)\n\n    // Check if resume exists and belongs to user\n    const existingResume = await prisma.resume.findFirst({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n    })\n\n    if (!existingResume) {\n      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })\n    }\n\n    const resume = await prisma.resume.update({\n      where: {\n        id: params.id,\n      },\n      data,\n      include: {\n        education: true,\n        experience: true,\n        skills: true,\n        languages: true,\n      },\n    })\n\n    return NextResponse.json(resume)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation failed', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Error updating resume:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/resume/[id] - Delete a specific resume\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Check if resume exists and belongs to user\n    const existingResume = await prisma.resume.findFirst({\n      where: {\n        id: params.id,\n        userId: session.user.id,\n      },\n    })\n\n    if (!existingResume) {\n      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })\n    }\n\n    await prisma.resume.delete({\n      where: {\n        id: params.id,\n      },\n    })\n\n    return NextResponse.json({ message: 'Resume deleted successfully' })\n  } catch (error) {\n    console.error('Error deleting resume:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,qBAAqB,QAAQ;IACtD,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,QAAQ;IAC7D,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,QAAQ;IACjD,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC/B;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3C,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,WAAW;oBACT,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,YAAY;oBACV,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,QAAQ;oBACN,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,WAAW;oBACT,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,OAAO,mBAAmB,KAAK,CAAC;QAEtC,6CAA6C;QAC7C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,OAAO;gBACL,IAAI,OAAO,EAAE;YACf;YACA;YACA,SAAS;gBACP,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAqB,SAAS,MAAM,MAAM;YAAC,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,6CAA6C;QAC7C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACzB,OAAO;gBACL,IAAI,OAAO,EAAE;YACf;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAA8B;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}