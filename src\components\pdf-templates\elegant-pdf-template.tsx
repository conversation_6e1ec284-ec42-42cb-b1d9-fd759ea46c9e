import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer'
import { PDFTemplateProps, formatPDFDateRange } from './base-pdf-template'

const styles = StyleSheet.create({
  page: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica'
  },
  sidebar: {
    width: '35%',
    backgroundColor: '#1f2937',
    color: '#ffffff',
    padding: 25
  },
  mainContent: {
    width: '65%',
    padding: 30
  },
  sidebarName: {
    fontSize: 20,
    fontWeight: 'light',
    marginBottom: 8
  },
  accent: {
    width: 40,
    height: 2,
    backgroundColor: '#fbbf24',
    marginBottom: 15
  },
  sidebarSection: {
    marginBottom: 25
  },
  sidebarSectionTitle: {
    fontSize: 14,
    color: '#fbbf24',
    marginBottom: 12,
    fontWeight: 'light'
  },
  contactItem: {
    fontSize: 9,
    marginBottom: 8,
    lineHeight: 1.3
  },
  skillItem: {
    marginBottom: 10
  },
  skillName: {
    fontSize: 10,
    marginBottom: 3
  },
  skillLevel: {
    fontSize: 8,
    color: '#d1d5db'
  },
  skillBar: {
    width: '100%',
    height: 3,
    backgroundColor: '#374151',
    marginTop: 3
  },
  skillProgress: {
    height: 3,
    backgroundColor: '#fbbf24'
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
    fontSize: 9
  },
  mainSection: {
    marginBottom: 25
  },
  mainSectionTitle: {
    fontSize: 18,
    color: '#1f2937',
    fontWeight: 'light',
    marginBottom: 12
  },
  mainAccent: {
    width: 50,
    height: 2,
    backgroundColor: '#fbbf24',
    marginBottom: 15
  },
  summary: {
    fontSize: 11,
    color: '#374151',
    lineHeight: 1.5
  },
  experienceItem: {
    marginBottom: 20,
    paddingLeft: 20,
    borderLeftWidth: 2,
    borderLeftColor: '#e5e7eb',
    position: 'relative'
  },
  experienceDot: {
    position: 'absolute',
    left: -6,
    top: 3,
    width: 10,
    height: 10,
    backgroundColor: '#fbbf24',
    borderRadius: 5
  },
  jobTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 3
  },
  company: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: 'bold',
    marginBottom: 3
  },
  jobDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    fontSize: 9,
    color: '#9ca3af',
    marginBottom: 8
  },
  description: {
    fontSize: 10,
    color: '#374151',
    lineHeight: 1.4
  },
  educationItem: {
    marginBottom: 15,
    paddingLeft: 20,
    borderLeftWidth: 2,
    borderLeftColor: '#e5e7eb',
    position: 'relative'
  },
  educationDot: {
    position: 'absolute',
    left: -6,
    top: 3,
    width: 10,
    height: 10,
    backgroundColor: '#fbbf24',
    borderRadius: 5
  },
  degree: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 3
  },
  institution: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 3
  },
  educationDate: {
    fontSize: 9,
    color: '#9ca3af'
  }
})

export function ElegantPDFTemplate({ data }: PDFTemplateProps) {
  const { personalInfo, education, experience, skills, languages } = data

  const getSkillWidth = (level: string) => {
    switch (level) {
      case 'Expert': return '100%'
      case 'Advanced': return '80%'
      case 'Intermediate': return '60%'
      default: return '40%'
    }
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Sidebar */}
        <View style={styles.sidebar}>
          <View style={styles.sidebarSection}>
            <Text style={styles.sidebarName}>{personalInfo.fullName}</Text>
            <View style={styles.accent} />
          </View>

          {/* Contact */}
          <View style={styles.sidebarSection}>
            <Text style={styles.sidebarSectionTitle}>Contact</Text>
            {personalInfo.email && (
              <Text style={styles.contactItem}>{personalInfo.email}</Text>
            )}
            {personalInfo.phone && (
              <Text style={styles.contactItem}>{personalInfo.phone}</Text>
            )}
            {personalInfo.location && (
              <Text style={styles.contactItem}>{personalInfo.location}</Text>
            )}
            {personalInfo.website && (
              <Text style={styles.contactItem}>{personalInfo.website}</Text>
            )}
            {personalInfo.linkedin && (
              <Text style={styles.contactItem}>LinkedIn</Text>
            )}
          </View>

          {/* Skills */}
          {skills.length > 0 && (
            <View style={styles.sidebarSection}>
              <Text style={styles.sidebarSectionTitle}>Skills</Text>
              {skills.map((skill) => (
                <View key={skill.id} style={styles.skillItem}>
                  <Text style={styles.skillName}>{skill.name}</Text>
                  <Text style={styles.skillLevel}>{skill.level}</Text>
                  <View style={styles.skillBar}>
                    <View style={[styles.skillProgress, { width: getSkillWidth(skill.level) }]} />
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Languages */}
          {languages.length > 0 && (
            <View style={styles.sidebarSection}>
              <Text style={styles.sidebarSectionTitle}>Languages</Text>
              {languages.map((lang) => (
                <View key={lang.id} style={styles.languageItem}>
                  <Text>{lang.name}</Text>
                  <Text style={{ color: '#d1d5db' }}>{lang.proficiency}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Summary */}
          {personalInfo.summary && (
            <View style={styles.mainSection}>
              <Text style={styles.mainSectionTitle}>Profile</Text>
              <View style={styles.mainAccent} />
              <Text style={styles.summary}>{personalInfo.summary}</Text>
            </View>
          )}

          {/* Experience */}
          {experience.length > 0 && (
            <View style={styles.mainSection}>
              <Text style={styles.mainSectionTitle}>Experience</Text>
              <View style={styles.mainAccent} />
              {experience.map((exp) => (
                <View key={exp.id} style={styles.experienceItem}>
                  <View style={styles.experienceDot} />
                  <Text style={styles.jobTitle}>{exp.position}</Text>
                  <Text style={styles.company}>{exp.company}</Text>
                  <View style={styles.jobDetails}>
                    <Text>{exp.location || ''}</Text>
                    <Text>{formatPDFDateRange(exp.startDate, exp.endDate, exp.current)}</Text>
                  </View>
                  <Text style={styles.description}>{exp.description}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Education */}
          {education.length > 0 && (
            <View style={styles.mainSection}>
              <Text style={styles.mainSectionTitle}>Education</Text>
              <View style={styles.mainAccent} />
              {education.map((edu) => (
                <View key={edu.id} style={styles.educationItem}>
                  <View style={styles.educationDot} />
                  <Text style={styles.degree}>{edu.degree} in {edu.field}</Text>
                  <Text style={styles.institution}>{edu.institution}</Text>
                  <Text style={styles.educationDate}>
                    {formatPDFDateRange(edu.startDate, edu.endDate, edu.current)}
                  </Text>
                  {edu.description && (
                    <Text style={styles.description}>{edu.description}</Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </Page>
    </Document>
  )
}
