import { withAuth } from 'next-auth/middleware'

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow print routes without authentication
        if (req.nextUrl.pathname.includes('/print')) {
          return true
        }
        return !!token
      }
    }
  }
)

export const config = {
  matcher: ['/dashboard/:path*', '/resume/:path*']
}
