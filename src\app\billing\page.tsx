'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Navigation } from '@/components/navigation'
import { getStripeJs } from '@/lib/stripe'

interface Subscription {
  id: string
  status: string
  priceId: string
  currentPeriodStart: string
  currentPeriodEnd: string
}

export default function BillingPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [loading, setLoading] = useState(true)
  const [checkoutLoading, setCheckoutLoading] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'paypal'>('card')
  const [cardForm, setCardForm] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: ''
  })

  useEffect(() => {
    fetchSubscription()
    
    // Handle success/cancel from Stripe checkout
    const success = searchParams.get('success')
    const canceled = searchParams.get('canceled')
    
    if (success) {
      // Show success message or redirect
      setTimeout(() => {
        router.replace('/billing')
      }, 3000)
    }
  }, [searchParams, router])

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscription(data)
      }
    } catch (error) {
      console.error('Error fetching subscription:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCardPayment = async () => {
    setCheckoutLoading(true)

    try {
      // Validate card form
      if (!cardForm.cardholderName || !cardForm.cardNumber || !cardForm.expiryDate || !cardForm.cvv) {
        alert('❌ Please fill in all card details')
        setCheckoutLoading(false)
        return
      }

      // Process payment through API
      const response = await fetch('/api/payment/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          paymentMethod: 'card',
          cardDetails: cardForm,
          amount: 9.99
        })
      })

      const result = await response.json()

      if (response.ok) {
        alert('🎉 Payment Successful!\n\nYour account has been upgraded to Premium.\nYou now have access to all premium features!')

        // Close modal and refresh page
        setShowPaymentModal(false)
        window.location.reload()
      } else {
        alert(`❌ Payment Failed\n\n${result.error || 'Please check your card details and try again.'}`)
      }
    } catch (error) {
      alert('❌ Payment Failed\n\nPlease check your connection and try again.')
    } finally {
      setCheckoutLoading(false)
    }
  }

  const handlePayPalPayment = async () => {
    setCheckoutLoading(true)

    try {
      // Process PayPal payment through API
      const response = await fetch('/api/payment/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          paymentMethod: 'paypal',
          amount: 9.99
        })
      })

      const result = await response.json()

      if (response.ok) {
        alert('🎉 PayPal Payment Successful!\n\nYour account has been upgraded to Premium.\nYou now have access to all premium features!')

        // Close modal and refresh page
        setShowPaymentModal(false)
        window.location.reload()
      } else {
        alert(`❌ PayPal Payment Failed\n\n${result.error || 'Please try again.'}`)
      }
    } catch (error) {
      alert('❌ PayPal Payment Failed\n\nPlease check your connection and try again.')
    } finally {
      setCheckoutLoading(false)
    }
  }

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g)
    const match = matches && matches[0] || ''
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(' ')
    } else {
      return v
    }
  }

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4)
    }
    return v
  }

  const handleCardInputChange = (field: string, value: string) => {
    let formattedValue = value

    if (field === 'cardNumber') {
      formattedValue = formatCardNumber(value)
    } else if (field === 'expiryDate') {
      formattedValue = formatExpiryDate(value)
    } else if (field === 'cvv') {
      formattedValue = value.replace(/[^0-9]/g, '')
    }

    setCardForm({...cardForm, [field]: formattedValue})
  }

  const handleUpgrade = async () => {
    setCheckoutLoading(true)

    try {
      // Show payment options modal
      setShowPaymentModal(true)
      setCheckoutLoading(false)

      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PREMIUM || 'price_1234567890abcdef'
        })
      })

      if (response.ok) {
        const { sessionId } = await response.json()
        const stripe = await getStripeJs()

        if (stripe) {
          const { error } = await stripe.redirectToCheckout({ sessionId })
          if (error) {
            console.error('Stripe checkout error:', error)
          }
        }
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create checkout session')
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
      alert('Failed to create checkout session')
    } finally {
      setCheckoutLoading(false)
    }
  }

  const handleManageSubscription = async () => {
    try {
      // Check if Stripe is properly configured
      const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

      if (!stripePublishableKey || stripePublishableKey.includes('51234567890abcdef')) {
        // Demo mode - show configuration message
        alert(`🔧 Subscription Management Demo

This would normally redirect you to the Stripe Customer Portal where you can:

• Update payment methods
• Download invoices
• Cancel or modify your subscription
• View billing history

To enable this feature, configure your Stripe API keys in the environment variables.`)
        return
      }

      const response = await fetch('/api/stripe/portal', {
        method: 'POST'
      })

      if (response.ok) {
        const { url } = await response.json()
        window.location.href = url
      }
    } catch (error) {
      console.error('Error creating portal session:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  const success = searchParams.get('success')
  const canceled = searchParams.get('canceled')

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Navigation />

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Billing & Subscription</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">Manage your CVCraft subscription</p>

            {/* Configuration Status */}
            {(!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
              process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.includes('51234567890abcdef')) && (
              <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <svg className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h4 className="text-sm font-medium text-blue-800">Demo Mode</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Billing functionality is in demo mode. Configure Stripe API keys to enable real payments.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Success/Cancel Messages */}
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-green-800">Payment successful!</h3>
                  <p className="text-sm text-green-700 mt-1">
                    Welcome to CVCraft Premium! You now have access to all premium features.
                  </p>
                </div>
              </div>
            </div>
          )}

          {canceled && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">Payment canceled</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    Your payment was canceled. You can try again anytime.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Current Plan */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 transition-colors duration-300">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Current Plan</h2>

              {session?.user?.isPremium ? (
                <div>
                  <div className="flex items-center mb-4">
                    <span className="bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-sm font-medium px-3 py-1 rounded-full">
                      Premium
                    </span>
                  </div>

                  {subscription && (
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <p><strong>Status:</strong> {subscription.status}</p>
                      <p><strong>Current period ends:</strong> {new Date(subscription.currentPeriodEnd).toLocaleDateString()}</p>
                    </div>
                  )}

                  <button
                    onClick={handleManageSubscription}
                    className="mt-4 bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    Manage Subscription
                  </button>
                </div>
              ) : (
                <div>
                  <div className="flex items-center mb-4">
                    <span className="bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 text-sm font-medium px-3 py-1 rounded-full">
                      Free
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    You're on the free plan with access to all features!
                  </p>
                </div>
              )}
            </div>

            {/* Free Plan Features */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 transition-colors duration-300">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Free Plan Features</h2>

              <div className="mb-6">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                  Free<span className="text-lg font-normal text-gray-600 dark:text-gray-300"> Forever</span>
                </div>
              </div>

              <ul className="space-y-3 mb-6">
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Unlimited resumes</span>
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700 dark:text-gray-300">All 3 professional templates</span>
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700 dark:text-gray-300">PDF downloads</span>
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700 dark:text-gray-300">Full customization</span>
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700 dark:text-gray-300">No ads or watermarks</span>
                </li>
              </ul>

              <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4 transition-colors duration-300">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Everything is Free!</h4>
                    <p className="text-sm text-green-700 dark:text-green-400 mt-1">
                      Enjoy all features without any cost. Create professional resumes today!
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Choose Payment Method</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Payment Method Tabs */}
            <div className="flex mb-6 border-b">
              <button
                onClick={() => setPaymentMethod('card')}
                className={`flex-1 py-2 px-4 text-sm font-medium ${
                  paymentMethod === 'card'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                💳 Credit Card
              </button>
              <button
                onClick={() => setPaymentMethod('paypal')}
                className={`flex-1 py-2 px-4 text-sm font-medium ${
                  paymentMethod === 'paypal'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                🅿️ PayPal
              </button>
            </div>

            {/* Card Payment Form */}
            {paymentMethod === 'card' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cardholder Name
                  </label>
                  <input
                    type="text"
                    value={cardForm.cardholderName}
                    onChange={(e) => handleCardInputChange('cardholderName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="John Doe"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Number
                  </label>
                  <input
                    type="text"
                    value={cardForm.cardNumber}
                    onChange={(e) => handleCardInputChange('cardNumber', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expiry Date
                    </label>
                    <input
                      type="text"
                      value={cardForm.expiryDate}
                      onChange={(e) => handleCardInputChange('expiryDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="MM/YY"
                      maxLength={5}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      CVV
                    </label>
                    <input
                      type="text"
                      value={cardForm.cvv}
                      onChange={(e) => handleCardInputChange('cvv', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="123"
                      maxLength={4}
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    onClick={handleCardPayment}
                    disabled={checkoutLoading}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md font-medium disabled:opacity-50"
                  >
                    {checkoutLoading ? 'Processing...' : 'Pay $9.99/month'}
                  </button>
                </div>
              </div>
            )}

            {/* PayPal Payment */}
            {paymentMethod === 'paypal' && (
              <div className="text-center space-y-4">
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="text-4xl mb-2">🅿️</div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">PayPal Payment</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    You will be redirected to PayPal to complete your payment securely.
                  </p>
                  <div className="text-2xl font-bold text-gray-900">$9.99/month</div>
                </div>

                <button
                  onClick={handlePayPalPayment}
                  disabled={checkoutLoading}
                  className="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-3 rounded-md font-medium disabled:opacity-50"
                >
                  {checkoutLoading ? 'Redirecting...' : 'Pay with PayPal'}
                </button>
              </div>
            )}

            <div className="mt-4 text-xs text-gray-500 text-center">
              🔒 Your payment information is secure and encrypted
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
