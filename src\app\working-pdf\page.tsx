'use client'

import { useState } from 'react'

export default function WorkingPDFPage() {
  const [isGenerating, setIsGenerating] = useState(false)

  const generateWorkingPDF = async () => {
    setIsGenerating(true)
    
    try {
      // Create a simple working PDF using jsPDF
      const { jsPDF } = await import('jspdf')
      
      const doc = new jsPDF()
      
      // Add content
      doc.setFontSize(24)
      doc.setTextColor(37, 99, 235)
      doc.text('<PERSON>', 105, 30, { align: 'center' })
      
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.text('Email: <EMAIL>', 105, 45, { align: 'center' })
      doc.text('Phone: ************', 105, 55, { align: 'center' })
      doc.text('Location: New York, NY', 105, 65, { align: 'center' })
      
      // Add line
      doc.setLineWidth(0.5)
      doc.setDrawColor(37, 99, 235)
      doc.line(20, 75, 190, 75)
      
      // Add sections
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Professional Summary', 20, 90)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      const summary = 'Experienced software developer with expertise in modern web technologies.'
      doc.text(summary, 20, 105)
      
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Experience', 20, 130)
      
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.setFont('helvetica', 'bold')
      doc.text('Senior Software Developer', 20, 145)
      
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(10)
      doc.setTextColor(100, 100, 100)
      doc.text('Tech Company • New York, NY • 2020 - Present', 20, 155)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      doc.text('• Led development of web applications', 20, 170)
      doc.text('• Collaborated with cross-functional teams', 20, 180)
      doc.text('• Mentored junior developers', 20, 190)
      
      // Save the PDF
      doc.save('Resume.pdf')
      
      alert('PDF downloaded successfully!')
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF: ' + error)
    } finally {
      setIsGenerating(false)
    }
  }

  const generateHTMLPDF = () => {
    // Create HTML content that looks like the CV preview
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>John Doe - Resume</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px 20px;
              background: white;
            }
            .header {
              border-bottom: 4px solid #2563eb;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              margin: 0;
              font-size: 2.5em;
              color: #1f2937;
              text-align: center;
            }
            .contact-info {
              margin: 15px 0;
              display: flex;
              justify-content: center;
              flex-wrap: wrap;
              gap: 20px;
            }
            .contact-info span {
              color: #6b7280;
            }
            .section {
              margin: 30px 0;
            }
            .section h2 {
              color: #2563eb;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 5px;
              margin-bottom: 15px;
              font-size: 1.5em;
            }
            .job {
              margin-bottom: 20px;
            }
            .job h3 {
              margin: 0 0 5px 0;
              color: #1f2937;
              font-size: 1.2em;
            }
            .job .meta {
              color: #6b7280;
              font-size: 0.9em;
              margin-bottom: 8px;
            }
            .job ul {
              margin: 10px 0;
              padding-left: 20px;
            }
            @media print {
              body { margin: 0; padding: 20px; }
            }
            @page {
              size: A4;
              margin: 0.5in;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>John Doe</h1>
            <div class="contact-info">
              <span>📧 <EMAIL></span>
              <span>📱 ************</span>
              <span>📍 New York, NY</span>
              <span>🌐 johndoe.com</span>
              <span>💼 LinkedIn</span>
            </div>
          </div>
          
          <div class="section">
            <h2>Professional Summary</h2>
            <p>Experienced software developer with a passion for creating innovative solutions. Skilled in modern web technologies including React, Node.js, and TypeScript. Committed to delivering high-quality applications and mentoring team members.</p>
          </div>

          <div class="section">
            <h2>Experience</h2>
            <div class="job">
              <h3>Senior Software Developer</h3>
              <div class="meta">Tech Solutions Inc. • New York, NY • June 2022 - Present</div>
              <ul>
                <li>Led development of multiple web applications using React and Node.js</li>
                <li>Collaborated with cross-functional teams to deliver projects on time</li>
                <li>Mentored junior developers and conducted code reviews</li>
                <li>Implemented CI/CD pipelines improving deployment efficiency by 40%</li>
              </ul>
            </div>
            
            <div class="job">
              <h3>Junior Developer</h3>
              <div class="meta">StartupCorp • San Francisco, CA • January 2021 - May 2022</div>
              <ul>
                <li>Developed and maintained web applications using JavaScript and Python</li>
                <li>Worked closely with designers to implement user interfaces</li>
                <li>Participated in agile development processes</li>
              </ul>
            </div>
          </div>

          <div class="section">
            <h2>Education</h2>
            <div class="job">
              <h3>Bachelor of Science in Computer Science</h3>
              <div class="meta">University of Technology • September 2018 - May 2022</div>
              <p>Graduated with honors. Focused on software engineering and web development.</p>
            </div>
          </div>

          <div class="section">
            <h2>Skills</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <div>JavaScript - Expert</div>
              <div>React - Expert</div>
              <div>Node.js - Advanced</div>
              <div>TypeScript - Advanced</div>
              <div>Python - Intermediate</div>
              <div>AWS - Intermediate</div>
            </div>
          </div>
        </body>
      </html>
    `

    // Open print dialog
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(htmlContent)
      printWindow.document.close()
      
      setTimeout(() => {
        printWindow.print()
      }, 1000)
    } else {
      alert('Please allow popups to print the resume')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white text-center">
          Working PDF Solutions
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 space-y-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Choose Your PDF Method
            </h2>
          </div>

          <button
            onClick={generateWorkingPDF}
            disabled={isGenerating}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-4 rounded-md font-medium transition-colors text-lg"
          >
            {isGenerating ? '⏳ Generating...' : '📄 Download PDF (jsPDF)'}
          </button>

          <button
            onClick={generateHTMLPDF}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-md font-medium transition-colors text-lg"
          >
            🖨️ Print Resume (HTML to PDF)
          </button>

          <div className="bg-green-50 dark:bg-green-900 p-4 rounded-md">
            <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">
              ✅ Guaranteed Working Solutions:
            </h3>
            <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <li>• <strong>jsPDF Method:</strong> Downloads actual PDF file</li>
              <li>• <strong>Print Method:</strong> Opens browser print dialog</li>
              <li>• <strong>No Server Required:</strong> Works entirely in browser</li>
              <li>• <strong>Professional Output:</strong> Clean, formatted resume</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
