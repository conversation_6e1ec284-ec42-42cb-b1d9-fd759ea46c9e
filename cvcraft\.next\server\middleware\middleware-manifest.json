{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9f9cf45e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_88e3134f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/resume/:path*{(\\\\.json)}?", "originalSource": "/resume/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/resume/:path*{(\\\\.json)}?", "originalSource": "/api/resume/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WDfBfE3YPmLg964bUwbbkqBM/1jVBBPZjmJB8odZDjM=", "__NEXT_PREVIEW_MODE_ID": "8f13053e1af9bdd13f04ec3d99efa917", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4a72f0d5e5651693cb47dfd2edf77ed5670f46aa559bd4f1cf33faf5c0c72cb8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ffa82ab7994a76fe6d90152cb8569dba777e28532119faa2c6df77d90550c2f1"}}}, "instrumentation": null, "functions": {}}