'use client'

export default function SimplePDFPage() {
  const downloadHTML = () => {
    // Create simple HTML content
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title><PERSON> - <PERSON>sume</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px 20px;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #2563eb;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              margin: 0;
              font-size: 2.5em;
              color: #1f2937;
            }
            .contact-info {
              margin: 15px 0;
              display: flex;
              justify-content: center;
              flex-wrap: wrap;
              gap: 20px;
            }
            .contact-info span {
              color: #6b7280;
            }
            .section {
              margin: 30px 0;
            }
            .section h2 {
              color: #2563eb;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              body { margin: 0; padding: 20px; }
            }
            @page {
              size: A4;
              margin: 0.5in;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1><PERSON></h1>
            <div class="contact-info">
              <span>📧 <EMAIL></span>
              <span>📱 123-456-7890</span>
              <span>📍 New York, NY</span>
            </div>
          </div>
          
          <div class="section">
            <h2>Professional Summary</h2>
            <p>Experienced software developer with a passion for creating innovative solutions. Skilled in modern web technologies and committed to delivering high-quality applications.</p>
          </div>

          <div class="section">
            <h2>Experience</h2>
            <div>
              <h3>Senior Software Developer</h3>
              <p><strong>Tech Company</strong> • New York, NY • 2020 - Present</p>
              <ul>
                <li>Led development of multiple web applications using React and Node.js</li>
                <li>Collaborated with cross-functional teams to deliver projects on time</li>
                <li>Mentored junior developers and conducted code reviews</li>
              </ul>
            </div>
          </div>

          <div class="section">
            <h2>Skills</h2>
            <ul>
              <li>JavaScript, TypeScript, React, Node.js</li>
              <li>HTML, CSS, Tailwind CSS</li>
              <li>Git, Docker, AWS</li>
            </ul>
          </div>
        </body>
      </html>
    `

    // Create blob and download
    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'John_Doe_Resume.html'
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }

  const printResume = () => {
    // Create simple HTML content
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>John Doe - Resume</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px 20px;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #2563eb;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              margin: 0;
              font-size: 2.5em;
              color: #1f2937;
            }
            .contact-info {
              margin: 15px 0;
              display: flex;
              justify-content: center;
              flex-wrap: wrap;
              gap: 20px;
            }
            .contact-info span {
              color: #6b7280;
            }
            .section {
              margin: 30px 0;
            }
            .section h2 {
              color: #2563eb;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              body { margin: 0; padding: 20px; }
            }
            @page {
              size: A4;
              margin: 0.5in;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>John Doe</h1>
            <div class="contact-info">
              <span>📧 <EMAIL></span>
              <span>📱 123-456-7890</span>
              <span>📍 New York, NY</span>
            </div>
          </div>
          
          <div class="section">
            <h2>Professional Summary</h2>
            <p>Experienced software developer with a passion for creating innovative solutions.</p>
          </div>

          <div class="section">
            <h2>Experience</h2>
            <div>
              <h3>Senior Software Developer</h3>
              <p><strong>Tech Company</strong> • New York, NY • 2020 - Present</p>
              <p>Led development of multiple web applications using React and Node.js</p>
            </div>
          </div>
        </body>
      </html>
    `

    // Open print dialog
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(htmlContent)
      printWindow.document.close()
      
      // Wait for content to load then print
      setTimeout(() => {
        printWindow.print()
      }, 500)
    } else {
      alert('Please allow popups to download PDF')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Simple PDF Download</h1>
        <div className="space-y-4">
          <button
            onClick={downloadHTML}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            📄 Download HTML Resume
          </button>
          <button
            onClick={printResume}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            🖨️ Print Resume (Save as PDF)
          </button>
          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900 rounded-md">
            <p className="text-sm text-green-800 dark:text-green-200">
              <strong>✅ Working Solutions:</strong><br/>
              • Download HTML: Downloads a styled HTML file<br/>
              • Print Resume: Opens print dialog to save as PDF
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
