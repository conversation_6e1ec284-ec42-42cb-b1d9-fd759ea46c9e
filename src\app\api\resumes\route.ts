import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { canCreateResume, canAccessTemplate } from '@/lib/premium'
import { TemplateType } from '@/types/resume'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resumes = await prisma.resume.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    return NextResponse.json(resumes)
  } catch (error) {
    console.error('Error fetching resumes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user can create more resumes
    const existingResumes = await prisma.resume.count({
      where: { userId: session.user.id }
    })

    if (!canCreateResume(existingResumes, session.user.isPremium)) {
      return NextResponse.json(
        { error: 'You have reached your resume limit. Upgrade to Premium for unlimited resumes.' },
        { status: 403 }
      )
    }

    const { title, data, template } = await request.json()

    // Validate template access
    const templateToUse = template || 'modern'
    if (!canAccessTemplate(templateToUse as TemplateType, session.user.isPremium)) {
      return NextResponse.json(
        { error: 'This template requires a Premium subscription.' },
        { status: 403 }
      )
    }

    const resume = await prisma.resume.create({
      data: {
        title: title || 'My Resume',
        data: data || {},
        template: templateToUse,
        userId: session.user.id
      }
    })

    return NextResponse.json(resume, { status: 201 })
  } catch (error) {
    console.error('Error creating resume:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
