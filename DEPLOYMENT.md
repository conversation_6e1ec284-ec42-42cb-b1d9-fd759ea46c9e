# CVCraft Deployment Guide

This guide will help you deploy CVCraft to production using Vercel and set up all necessary services.

## Prerequisites

- GitHub account
- Vercel account
- Stripe account
- Database provider (PlanetScale, Supabase, or any PostgreSQL provider)

## Step 1: Prepare Your Repository

1. **Push your code to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

## Step 2: Set Up Production Database

### Option A: PlanetScale (Recommended)
1. Go to [planetscale.com](https://planetscale.com) and create an account
2. Create a new database called `cvcraft`
3. Get the connection string from the dashboard
4. Update your Prisma schema to use MySQL:
   ```prisma
   datasource db {
     provider = "mysql"
     url      = env("DATABASE_URL")
     relationMode = "prisma"
   }
   ```

### Option B: Supabase
1. Go to [supabase.com](https://supabase.com) and create a project
2. Get the PostgreSQL connection string
3. Keep the Prisma schema as PostgreSQL

### Option C: Railway/Render PostgreSQL
1. Create a PostgreSQL database on your preferred provider
2. Get the connection string

## Step 3: Configure Stripe for Production

1. **Get Production Keys**
   - Go to your Stripe dashboard
   - Switch to "Live mode"
   - Copy your live publishable and secret keys

2. **Create Premium Product**
   ```bash
   # Using Stripe CLI (optional)
   stripe products create --name "CVCraft Premium"
   stripe prices create --product <product_id> --unit-amount 999 --currency usd --recurring interval=month
   ```

3. **Set Up Webhooks**
   - Go to Stripe Dashboard > Webhooks
   - Add endpoint: `https://your-domain.vercel.app/api/stripe/webhook`
   - Select events:
     - `checkout.session.completed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`

## Step 4: Deploy to Vercel

1. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select "Next.js" as framework

2. **Configure Environment Variables**
   Add these environment variables in Vercel dashboard:

   ```env
   # Database
   DATABASE_URL="your-production-database-url"

   # NextAuth.js
   NEXTAUTH_URL="https://your-domain.vercel.app"
   NEXTAUTH_SECRET="your-production-secret-key"

   # Stripe (LIVE KEYS)
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_..."
   STRIPE_SECRET_KEY="sk_live_..."
   STRIPE_WEBHOOK_SECRET="whsec_..."
   STRIPE_PRICE_ID_PREMIUM="price_..."
   ```

3. **Deploy**
   - Click "Deploy"
   - Wait for the build to complete

## Step 5: Set Up Database Schema

1. **Run Prisma Commands**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Push schema to production database
   npx prisma db push
   ```

   Or use Vercel CLI:
   ```bash
   vercel env pull .env.local
   npx prisma db push
   ```

## Step 6: Configure Custom Domain (Optional)

1. **Add Domain in Vercel**
   - Go to your project settings
   - Add your custom domain
   - Configure DNS records

2. **Update Environment Variables**
   - Update `NEXTAUTH_URL` to your custom domain
   - Update Stripe webhook URL

## Step 7: Test Production Deployment

1. **Test User Registration**
   - Create a new account
   - Verify email functionality works

2. **Test Resume Creation**
   - Create a resume
   - Test PDF download
   - Verify templates work

3. **Test Stripe Integration**
   - Test subscription signup
   - Verify webhook events
   - Test subscription management

4. **Test Premium Features**
   - Verify template restrictions
   - Test resume limits
   - Confirm premium access works

## Step 8: Monitoring and Analytics

1. **Set Up Error Monitoring**
   - Add Sentry or similar service
   - Monitor API errors and performance

2. **Analytics**
   - Add Google Analytics or Vercel Analytics
   - Track user behavior and conversions

## Environment Variables Reference

### Required for Production

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | Production database connection string | `mysql://...` or `postgresql://...` |
| `NEXTAUTH_URL` | Your production domain | `https://cvcraft.com` |
| `NEXTAUTH_SECRET` | Random secret for JWT signing | Generate with `openssl rand -base64 32` |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | Stripe live publishable key | `pk_live_...` |
| `STRIPE_SECRET_KEY` | Stripe live secret key | `sk_live_...` |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook signing secret | `whsec_...` |
| `STRIPE_PRICE_ID_PREMIUM` | Premium subscription price ID | `price_...` |

### Optional

| Variable | Description |
|----------|-------------|
| `SENTRY_DSN` | Error monitoring |
| `GOOGLE_ANALYTICS_ID` | Analytics tracking |

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify connection string format
   - Check database provider status
   - Ensure IP whitelist includes Vercel IPs

2. **Stripe Webhook Failures**
   - Verify webhook URL is correct
   - Check webhook secret matches
   - Ensure all required events are selected

3. **Authentication Issues**
   - Verify `NEXTAUTH_URL` matches your domain
   - Check `NEXTAUTH_SECRET` is set
   - Ensure cookies work with your domain

4. **PDF Generation Errors**
   - Check if all dependencies are installed
   - Verify memory limits in Vercel functions

### Performance Optimization

1. **Database Optimization**
   - Add database indexes for frequently queried fields
   - Use connection pooling

2. **Caching**
   - Implement Redis for session storage
   - Cache frequently accessed data

3. **CDN**
   - Use Vercel's built-in CDN
   - Optimize images and assets

## Security Checklist

- [ ] All environment variables are set correctly
- [ ] Database uses SSL connections
- [ ] Stripe webhooks are properly secured
- [ ] CORS is configured correctly
- [ ] Rate limiting is implemented
- [ ] Input validation is in place
- [ ] SQL injection protection is active

## Maintenance

### Regular Tasks
- Monitor error logs
- Update dependencies
- Backup database
- Review Stripe transactions
- Monitor performance metrics

### Updates
- Test in staging environment first
- Use Vercel preview deployments
- Monitor deployment for errors
- Rollback if issues occur

---

🎉 **Congratulations!** Your CVCraft application is now live in production!
