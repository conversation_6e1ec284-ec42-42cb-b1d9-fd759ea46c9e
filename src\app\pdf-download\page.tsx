'use client'

import { jsPDF } from 'jspdf'

export default function PDFDownloadPage() {
  const downloadPDF = () => {
    try {
      // Create new PDF document
      const doc = new jsPDF()
      
      // Set font
      doc.setFont('helvetica', 'normal')
      
      // Add title
      doc.setFontSize(24)
      doc.setTextColor(37, 99, 235) // Blue color
      doc.text('<PERSON>', 105, 30, { align: 'center' })
      
      // Add contact info
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0) // Black color
      doc.text('Email: <EMAIL>', 105, 45, { align: 'center' })
      doc.text('Phone: ************', 105, 55, { align: 'center' })
      doc.text('Location: New York, NY', 105, 65, { align: 'center' })
      
      // Add line separator
      doc.setLineWidth(0.5)
      doc.setDrawColor(37, 99, 235)
      doc.line(20, 75, 190, 75)
      
      // Add Professional Summary section
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Professional Summary', 20, 90)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      const summaryText = 'Experienced software developer with a passion for creating innovative solutions. Skilled in modern web technologies and committed to delivering high-quality applications.'
      const splitSummary = doc.splitTextToSize(summaryText, 170)
      doc.text(splitSummary, 20, 105)
      
      // Add Experience section
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Experience', 20, 135)
      
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.setFont('helvetica', 'bold')
      doc.text('Senior Software Developer', 20, 150)
      
      doc.setFont('helvetica', 'normal')
      doc.setFontSize(10)
      doc.setTextColor(100, 100, 100)
      doc.text('Tech Company • New York, NY • 2020 - Present', 20, 160)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      const expText = '• Led development of multiple web applications using React and Node.js\n• Collaborated with cross-functional teams to deliver projects on time\n• Mentored junior developers and conducted code reviews'
      const splitExp = doc.splitTextToSize(expText, 170)
      doc.text(splitExp, 20, 170)
      
      // Add Skills section
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Skills', 20, 210)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      doc.text('• JavaScript, TypeScript, React, Node.js', 20, 225)
      doc.text('• HTML, CSS, Tailwind CSS', 20, 235)
      doc.text('• Git, Docker, AWS', 20, 245)
      
      // Save the PDF
      doc.save('John_Doe_Resume.pdf')
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  const downloadWithPhoto = () => {
    try {
      const doc = new jsPDF()
      
      // Add a placeholder for photo (circle)
      doc.setDrawColor(37, 99, 235)
      doc.setFillColor(240, 240, 240)
      doc.circle(40, 40, 15, 'FD')
      
      // Add text indicating photo placeholder
      doc.setFontSize(8)
      doc.setTextColor(100, 100, 100)
      doc.text('Photo', 40, 45, { align: 'center' })
      
      // Add name next to photo
      doc.setFontSize(24)
      doc.setTextColor(37, 99, 235)
      doc.text('John Doe', 70, 35)
      
      // Add contact info
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.text('Email: <EMAIL>', 70, 45)
      doc.text('Phone: ************', 70, 55)
      doc.text('Location: New York, NY', 70, 65)
      
      // Add line separator
      doc.setLineWidth(0.5)
      doc.setDrawColor(37, 99, 235)
      doc.line(20, 75, 190, 75)
      
      // Add content similar to first function
      doc.setFontSize(16)
      doc.setTextColor(37, 99, 235)
      doc.text('Professional Summary', 20, 90)
      
      doc.setFontSize(11)
      doc.setTextColor(0, 0, 0)
      const summaryText = 'Experienced software developer with a passion for creating innovative solutions.'
      doc.text(summaryText, 20, 105)
      
      doc.save('John_Doe_Resume_With_Photo.pdf')
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">PDF Download Test</h1>
        <div className="space-y-4">
          <button
            onClick={downloadPDF}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            📄 Download PDF Resume
          </button>
          <button
            onClick={downloadWithPhoto}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
          >
            📸 Download PDF with Photo Placeholder
          </button>
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Note:</strong> These buttons will download actual PDF files to your computer using jsPDF library.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
