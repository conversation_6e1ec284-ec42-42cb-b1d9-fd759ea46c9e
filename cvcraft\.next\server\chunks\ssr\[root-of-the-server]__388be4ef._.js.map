{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/ModernTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface ModernTemplateProps {\n  data: ResumeData\n}\n\nexport default function ModernTemplate({ data }: ModernTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-8\">\n        <div className=\"flex items-center space-x-6\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-24 h-24 rounded-full border-4 border-white object-cover\"\n            />\n          )}\n          <div className=\"flex-1\">\n            <h1 className=\"text-4xl font-bold mb-2\">{data.fullName}</h1>\n            <div className=\"flex flex-wrap gap-4 text-blue-100\">\n              <span>{data.email}</span>\n              {data.phone && <span>{data.phone}</span>}\n              {data.address && <span>{data.address}</span>}\n            </div>\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6\">\n            <p className=\"text-blue-50 leading-relaxed\">{data.summary}</p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Experience */}\n            {data.experience.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Professional Experience\n                </h2>\n                <div className=\"space-y-6\">\n                  {data.experience.map((exp) => (\n                    <div key={exp.id}>\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-800\">{exp.position}</h3>\n                          <p className=\"text-blue-600 font-medium\">{exp.company}</p>\n                          {exp.location && <p className=\"text-gray-600 text-sm\">{exp.location}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-600\">\n                          <p>\n                            {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {exp.description && (\n                        <div className=\"text-gray-700 text-sm leading-relaxed\">\n                          {exp.description.split('\\n').map((line, index) => (\n                            <p key={index} className=\"mb-1\">{line}</p>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Education */}\n            {data.education.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Education\n                </h2>\n                <div className=\"space-y-4\">\n                  {data.education.map((edu) => (\n                    <div key={edu.id}>\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-800\">{edu.degree}</h3>\n                          <p className=\"text-blue-600 font-medium\">{edu.institution}</p>\n                          {edu.fieldOfStudy && <p className=\"text-gray-600 text-sm\">{edu.fieldOfStudy}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-600\">\n                          <p>\n                            {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {edu.description && (\n                        <p className=\"text-gray-700 text-sm leading-relaxed\">{edu.description}</p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-8\">\n            {/* Skills */}\n            {data.skills.length > 0 && (\n              <section>\n                <h2 className=\"text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Skills\n                </h2>\n                <div className=\"space-y-3\">\n                  {data.skills.map((skill) => (\n                    <div key={skill.id}>\n                      <div className=\"flex justify-between items-center mb-1\">\n                        <span className=\"text-gray-800 font-medium\">{skill.name}</span>\n                        {skill.level && (\n                          <span className=\"text-xs text-gray-600 capitalize\">{skill.level}</span>\n                        )}\n                      </div>\n                      {skill.level && (\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{\n                              width: skill.level === 'expert' ? '100%' : \n                                     skill.level === 'advanced' ? '80%' : \n                                     skill.level === 'intermediate' ? '60%' : '40%'\n                            }}\n                          ></div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Languages */}\n            {data.languages.length > 0 && (\n              <section>\n                <h2 className=\"text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600\">\n                  Languages\n                </h2>\n                <div className=\"space-y-2\">\n                  {data.languages.map((lang) => (\n                    <div key={lang.id} className=\"flex justify-between items-center\">\n                      <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                      <span className=\"text-sm text-blue-600 capitalize\">{lang.level}</span>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,eAAe,EAAE,IAAI,EAAuB;IAClE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,8OAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2B,KAAK,QAAQ;;;;;;kDACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,KAAK,KAAK;;;;;;4CAChB,KAAK,KAAK,kBAAI,8OAAC;0DAAM,KAAK,KAAK;;;;;;4CAC/B,KAAK,OAAO,kBAAI,8OAAC;0DAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oBAIzC,KAAK,OAAO,kBACX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgC,KAAK,OAAO;;;;;;;;;;;;;;;;;0BAK/D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,8OAAC;4CAAI,WAAU;sDACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,oBACpB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAuC,IAAI,QAAQ;;;;;;sFACjE,8OAAC;4EAAE,WAAU;sFAA6B,IAAI,OAAO;;;;;;wEACpD,IAAI,QAAQ,kBAAI,8OAAC;4EAAE,WAAU;sFAAyB,IAAI,QAAQ;;;;;;;;;;;;8EAErE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;;4EACE,WAAW,IAAI,SAAS;4EAAE;4EAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;wDAItG,IAAI,WAAW,kBACd,8OAAC;4DAAI,WAAU;sEACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,8OAAC;oEAAc,WAAU;8EAAQ;mEAAzB;;;;;;;;;;;mDAhBN,IAAI,EAAE;;;;;;;;;;;;;;;;gCA2BvB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,8OAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAuC,IAAI,MAAM;;;;;;sFAC/D,8OAAC;4EAAE,WAAU;sFAA6B,IAAI,WAAW;;;;;;wEACxD,IAAI,YAAY,kBAAI,8OAAC;4EAAE,WAAU;sFAAyB,IAAI,YAAY;;;;;;;;;;;;8EAE7E,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;;4EACE,WAAW,IAAI,SAAS;4EAAE;4EAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;wDAItG,IAAI,WAAW,kBACd,8OAAC;4DAAE,WAAU;sEAAyC,IAAI,WAAW;;;;;;;mDAd/D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAwB1B,8OAAC;4BAAI,WAAU;;gCAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAuE;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA6B,MAAM,IAAI;;;;;;gEACtD,MAAM,KAAK,kBACV,8OAAC;oEAAK,WAAU;8EAAoC,MAAM,KAAK;;;;;;;;;;;;wDAGlE,MAAM,KAAK,kBACV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,MAAM,KAAK,KAAK,WAAW,SAC3B,MAAM,KAAK,KAAK,aAAa,QAC7B,MAAM,KAAK,KAAK,iBAAiB,QAAQ;gEAClD;;;;;;;;;;;;mDAfE,MAAM,EAAE;;;;;;;;;;;;;;;;gCA0BzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAuE;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;sEAA6B,KAAK,IAAI;;;;;;sEACtD,8OAAC;4DAAK,WAAU;sEAAoC,KAAK,KAAK;;;;;;;mDAFtD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/ElegantTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface ElegantTemplateProps {\n  data: ResumeData\n}\n\nexport default function ElegantTemplate({ data }: ElegantTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"border-b-4 border-gray-800 p-8\">\n        <div className=\"text-center\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-32 h-32 rounded-full mx-auto mb-4 object-cover border-4 border-gray-200\"\n            />\n          )}\n          <h1 className=\"text-4xl font-serif font-bold text-gray-800 mb-2\">{data.fullName}</h1>\n          <div className=\"flex justify-center space-x-6 text-gray-600 text-sm\">\n            <span>{data.email}</span>\n            {data.phone && <span>{data.phone}</span>}\n            {data.address && <span>{data.address}</span>}\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-gray-700 leading-relaxed max-w-3xl mx-auto italic\">\n              \"{data.summary}\"\n            </p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8\">\n        {/* Experience */}\n        {data.experience.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n              Professional Experience\n            </h2>\n            <div className=\"space-y-6\">\n              {data.experience.map((exp, index) => (\n                <div key={exp.id} className=\"relative\">\n                  {index > 0 && <div className=\"absolute left-4 -top-3 w-0.5 h-6 bg-gray-300\"></div>}\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0 mt-1\">\n                      <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h3 className=\"text-lg font-serif font-semibold text-gray-800\">{exp.position}</h3>\n                          <p className=\"text-gray-600 font-medium\">{exp.company}</p>\n                          {exp.location && <p className=\"text-gray-500 text-sm\">{exp.location}</p>}\n                        </div>\n                        <div className=\"text-right text-sm text-gray-500\">\n                          <p>\n                            {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                          </p>\n                        </div>\n                      </div>\n                      {exp.description && (\n                        <div className=\"text-gray-700 text-sm leading-relaxed\">\n                          {exp.description.split('\\n').map((line, index) => (\n                            <p key={index} className=\"mb-1\">{line}</p>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Education */}\n          {data.education.length > 0 && (\n            <section>\n              <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                Education\n              </h2>\n              <div className=\"space-y-4\">\n                {data.education.map((edu) => (\n                  <div key={edu.id} className=\"text-center\">\n                    <h3 className=\"text-lg font-serif font-semibold text-gray-800\">{edu.degree}</h3>\n                    <p className=\"text-gray-600 font-medium\">{edu.institution}</p>\n                    {edu.fieldOfStudy && <p className=\"text-gray-500 text-sm\">{edu.fieldOfStudy}</p>}\n                    <p className=\"text-sm text-gray-500 mt-1\">\n                      {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                    </p>\n                    {edu.description && (\n                      <p className=\"text-gray-700 text-sm leading-relaxed mt-2\">{edu.description}</p>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n\n          {/* Skills & Languages */}\n          <div className=\"space-y-8\">\n            {/* Skills */}\n            {data.skills.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                  Skills\n                </h2>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  {data.skills.map((skill) => (\n                    <div key={skill.id} className=\"text-center\">\n                      <span className=\"text-gray-800 font-medium text-sm\">{skill.name}</span>\n                      {skill.level && (\n                        <p className=\"text-xs text-gray-500 capitalize\">{skill.level}</p>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Languages */}\n            {data.languages.length > 0 && (\n              <section>\n                <h2 className=\"text-2xl font-serif font-bold text-gray-800 mb-6 text-center\">\n                  Languages\n                </h2>\n                <div className=\"space-y-2\">\n                  {data.languages.map((lang) => (\n                    <div key={lang.id} className=\"text-center\">\n                      <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                      <p className=\"text-sm text-gray-500 capitalize\">{lang.level}</p>\n                    </div>\n                  ))}\n                </div>\n              </section>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,gBAAgB,EAAE,IAAI,EAAwB;IACpE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,8OAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,8OAAC;gCAAG,WAAU;0CAAoD,KAAK,QAAQ;;;;;;0CAC/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,KAAK,KAAK;;;;;;oCAChB,KAAK,KAAK,kBAAI,8OAAC;kDAAM,KAAK,KAAK;;;;;;oCAC/B,KAAK,OAAO,kBAAI,8OAAC;kDAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;oBAGvC,KAAK,OAAO,kBACX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAyD;gCAClE,KAAK,OAAO;gCAAC;;;;;;;;;;;;;;;;;;0BAMvB,8OAAC;gBAAI,WAAU;;oBAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;0CACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,sBACzB,8OAAC;wCAAiB,WAAU;;4CACzB,QAAQ,mBAAK,8OAAC;gDAAI,WAAU;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAkD,IAAI,QAAQ;;;;;;0FAC5E,8OAAC;gFAAE,WAAU;0FAA6B,IAAI,OAAO;;;;;;4EACpD,IAAI,QAAQ,kBAAI,8OAAC;gFAAE,WAAU;0FAAyB,IAAI,QAAQ;;;;;;;;;;;;kFAErE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;;gFACE,WAAW,IAAI,SAAS;gFAAE;gFAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4DAItG,IAAI,WAAW,kBACd,8OAAC;gEAAI,WAAU;0EACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,8OAAC;wEAAc,WAAU;kFAAQ;uEAAzB;;;;;;;;;;;;;;;;;;;;;;;uCAtBV,IAAI,EAAE;;;;;;;;;;;;;;;;kCAkCxB,8OAAC;wBAAI,WAAU;;4BAEZ,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,8OAAC;gDAAiB,WAAU;;kEAC1B,8OAAC;wDAAG,WAAU;kEAAkD,IAAI,MAAM;;;;;;kEAC1E,8OAAC;wDAAE,WAAU;kEAA6B,IAAI,WAAW;;;;;;oDACxD,IAAI,YAAY,kBAAI,8OAAC;wDAAE,WAAU;kEAAyB,IAAI,YAAY;;;;;;kEAC3E,8OAAC;wDAAE,WAAU;;4DACV,WAAW,IAAI,SAAS;4DAAE;4DAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;oDAElG,IAAI,WAAW,kBACd,8OAAC;wDAAE,WAAU;kEAA8C,IAAI,WAAW;;;;;;;+CARpE,IAAI,EAAE;;;;;;;;;;;;;;;;0CAiBxB,8OAAC;gCAAI,WAAU;;oCAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,8OAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAK,WAAU;0EAAqC,MAAM,IAAI;;;;;;4DAC9D,MAAM,KAAK,kBACV,8OAAC;gEAAE,WAAU;0EAAoC,MAAM,KAAK;;;;;;;uDAHtD,MAAM,EAAE;;;;;;;;;;;;;;;;oCAYzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,8OAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEAAK,WAAU;0EAA6B,KAAK,IAAI;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAAoC,KAAK,KAAK;;;;;;;uDAFnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarC", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/SimpleTemplate.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface SimpleTemplateProps {\n  data: ResumeData\n}\n\nexport default function SimpleTemplate({ data }: SimpleTemplateProps) {\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString + '-01')\n    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg\" id=\"resume-content\">\n      {/* Header */}\n      <div className=\"border-b-2 border-gray-300 p-8\">\n        <div className=\"flex items-center space-x-6\">\n          {data.profileImage && (\n            <img\n              src={data.profileImage}\n              alt={data.fullName}\n              className=\"w-20 h-20 rounded object-cover border border-gray-300\"\n            />\n          )}\n          <div className=\"flex-1\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{data.fullName}</h1>\n            <div className=\"text-gray-600 space-y-1\">\n              <p>{data.email}</p>\n              {data.phone && <p>{data.phone}</p>}\n              {data.address && <p>{data.address}</p>}\n            </div>\n          </div>\n        </div>\n        {data.summary && (\n          <div className=\"mt-6\">\n            <h2 className=\"text-lg font-bold text-gray-900 mb-2\">Professional Summary</h2>\n            <p className=\"text-gray-700 leading-relaxed\">{data.summary}</p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"p-8 space-y-8\">\n        {/* Experience */}\n        {data.experience.length > 0 && (\n          <section>\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n              Professional Experience\n            </h2>\n            <div className=\"space-y-6\">\n              {data.experience.map((exp) => (\n                <div key={exp.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-bold text-gray-900\">{exp.position}</h3>\n                      <p className=\"text-gray-700 font-medium\">{exp.company}</p>\n                      {exp.location && <p className=\"text-gray-600 text-sm\">{exp.location}</p>}\n                    </div>\n                    <div className=\"text-right text-sm text-gray-600\">\n                      <p>\n                        {formatDate(exp.startDate)} - {exp.current ? 'Present' : exp.endDate ? formatDate(exp.endDate) : 'Present'}\n                      </p>\n                    </div>\n                  </div>\n                  {exp.description && (\n                    <div className=\"text-gray-700 text-sm leading-relaxed\">\n                      {exp.description.split('\\n').map((line, index) => (\n                        <p key={index} className=\"mb-1\">{line}</p>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Education */}\n        {data.education.length > 0 && (\n          <section>\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n              Education\n            </h2>\n            <div className=\"space-y-4\">\n              {data.education.map((edu) => (\n                <div key={edu.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-bold text-gray-900\">{edu.degree}</h3>\n                      <p className=\"text-gray-700 font-medium\">{edu.institution}</p>\n                      {edu.fieldOfStudy && <p className=\"text-gray-600 text-sm\">{edu.fieldOfStudy}</p>}\n                    </div>\n                    <div className=\"text-right text-sm text-gray-600\">\n                      <p>\n                        {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : 'Present'}\n                      </p>\n                    </div>\n                  </div>\n                  {edu.description && (\n                    <p className=\"text-gray-700 text-sm leading-relaxed\">{edu.description}</p>\n                  )}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {/* Skills */}\n          {data.skills.length > 0 && (\n            <section>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n                Skills\n              </h2>\n              <div className=\"space-y-2\">\n                {data.skills.map((skill) => (\n                  <div key={skill.id} className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-800 font-medium\">{skill.name}</span>\n                    {skill.level && (\n                      <span className=\"text-sm text-gray-600 capitalize\">{skill.level}</span>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n\n          {/* Languages */}\n          {data.languages.length > 0 && (\n            <section>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4 uppercase tracking-wide\">\n                Languages\n              </h2>\n              <div className=\"space-y-2\">\n                {data.languages.map((lang) => (\n                  <div key={lang.id} className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-800 font-medium\">{lang.name}</span>\n                    <span className=\"text-sm text-gray-600 capitalize\">{lang.level}</span>\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAgDe,SAAS,eAAe,EAAE,IAAI,EAAuB;IAClE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK,aAAa;QACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;QAAQ;IAC5E;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAuC,IAAG;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,kBAChB,8OAAC;gCACC,KAAK,KAAK,YAAY;gCACtB,KAAK,KAAK,QAAQ;gCAClB,WAAU;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC,KAAK,QAAQ;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAG,KAAK,KAAK;;;;;;4CACb,KAAK,KAAK,kBAAI,8OAAC;0DAAG,KAAK,KAAK;;;;;;4CAC5B,KAAK,OAAO,kBAAI,8OAAC;0DAAG,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oBAItC,KAAK,OAAO,kBACX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAiC,KAAK,OAAO;;;;;;;;;;;;;;;;;;0BAKhE,8OAAC;gBAAI,WAAU;;oBAEZ,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;0CACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,oBACpB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC,IAAI,QAAQ;;;;;;0EAC7D,8OAAC;gEAAE,WAAU;0EAA6B,IAAI,OAAO;;;;;;4DACpD,IAAI,QAAQ,kBAAI,8OAAC;gEAAE,WAAU;0EAAyB,IAAI,QAAQ;;;;;;;;;;;;kEAErE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;gEACE,WAAW,IAAI,SAAS;gEAAE;gEAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4CAItG,IAAI,WAAW,kBACd,8OAAC;gDAAI,WAAU;0DACZ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtC,8OAAC;wDAAc,WAAU;kEAAQ;uDAAzB;;;;;;;;;;;uCAhBN,IAAI,EAAE;;;;;;;;;;;;;;;;oBA2BvB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,oBACnB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC,IAAI,MAAM;;;;;;0EAC3D,8OAAC;gEAAE,WAAU;0EAA6B,IAAI,WAAW;;;;;;4DACxD,IAAI,YAAY,kBAAI,8OAAC;gEAAE,WAAU;0EAAyB,IAAI,YAAY;;;;;;;;;;;;kEAE7E,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;gEACE,WAAW,IAAI,SAAS;gEAAE;gEAAI,IAAI,OAAO,GAAG,YAAY,IAAI,OAAO,GAAG,WAAW,IAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;4CAItG,IAAI,WAAW,kBACd,8OAAC;gDAAE,WAAU;0DAAyC,IAAI,WAAW;;;;;;;uCAd/D,IAAI,EAAE;;;;;;;;;;;;;;;;kCAsBxB,8OAAC;wBAAI,WAAU;;4BAEZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAK,WAAU;kEAA6B,MAAM,IAAI;;;;;;oDACtD,MAAM,KAAK,kBACV,8OAAC;wDAAK,WAAU;kEAAoC,MAAM,KAAK;;;;;;;+CAHzD,MAAM,EAAE;;;;;;;;;;;;;;;;4BAYzB,KAAK,SAAS,CAAC,MAAM,GAAG,mBACvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;wDAAK,WAAU;kEAA6B,KAAK,IAAI;;;;;;kEACtD,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAFtD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnC", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/components/templates/TemplateRenderer.tsx"], "sourcesContent": ["import React from 'react'\nimport ModernTemplate from './ModernTemplate'\nimport ElegantTemplate from './ElegantTemplate'\nimport SimpleTemplate from './SimpleTemplate'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\ninterface TemplateRendererProps {\n  data: ResumeData\n}\n\nexport default function TemplateRenderer({ data }: TemplateRendererProps) {\n  switch (data.template) {\n    case 'modern':\n      return <ModernTemplate data={data} />\n    case 'elegant':\n      return <ElegantTemplate data={data} />\n    case 'simple':\n      return <SimpleTemplate data={data} />\n    default:\n      return <ModernTemplate data={data} />\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAgDe,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IACtE,OAAQ,KAAK,QAAQ;QACnB,KAAK;YACH,qBAAO,8OAAC,iJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;QAC/B,KAAK;YACH,qBAAO,8OAAC,kJAAA,CAAA,UAAe;gBAAC,MAAM;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC,iJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;QAC/B;YACE,qBAAO,8OAAC,iJAAA,CAAA,UAAc;gBAAC,MAAM;;;;;;IACjC;AACF", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/resume/preview/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport TemplateRenderer from '@/components/templates/TemplateRenderer'\n\ninterface ResumeData {\n  id: string\n  title: string\n  fullName: string\n  email: string\n  phone?: string\n  address?: string\n  profileImage?: string\n  summary?: string\n  template: string\n  education: Array<{\n    id: string\n    institution: string\n    degree: string\n    fieldOfStudy?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  experience: Array<{\n    id: string\n    company: string\n    position: string\n    location?: string\n    startDate: string\n    endDate?: string\n    current: boolean\n    description?: string\n  }>\n  skills: Array<{\n    id: string\n    name: string\n    level?: string\n  }>\n  languages: Array<{\n    id: string\n    name: string\n    level: string\n  }>\n}\n\nexport default function ResumePreviewPage({ params }: { params: { id: string } }) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [resume, setResume] = useState<ResumeData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (status === 'loading') return\n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n    \n    fetchResume()\n  }, [session, status, router, params.id])\n\n  const fetchResume = async () => {\n    try {\n      const response = await fetch(`/api/resume/${params.id}`)\n      if (response.ok) {\n        const data = await response.json()\n        setResume(data)\n      } else if (response.status === 404) {\n        setError('Resume not found')\n      } else {\n        setError('Failed to load resume')\n      }\n    } catch (error) {\n      console.error('Error fetching resume:', error)\n      setError('Failed to load resume')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDownloadPDF = async () => {\n    try {\n      const response = await fetch(`/api/resume/${params.id}/pdf`)\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `${resume?.title || 'resume'}.pdf`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        alert('PDF generation is not yet implemented')\n      }\n    } catch (error) {\n      console.error('Error downloading PDF:', error)\n      alert('Failed to download PDF')\n    }\n  }\n\n  if (status === 'loading' || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Error</h1>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <Link\n            href=\"/dashboard\"\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n          >\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  if (!resume) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow sticky top-0 z-10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                ← Back to Dashboard\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">{resume.title}</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href={`/resume/edit/${resume.id}`}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\"\n              >\n                Edit Resume\n              </Link>\n              <button\n                onClick={handleDownloadPDF}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700\"\n              >\n                Download PDF\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Resume Preview */}\n      <main className=\"py-8\">\n        <div className=\"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"bg-white shadow-lg rounded-lg overflow-hidden\">\n            <TemplateRenderer data={resume} />\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkDe,SAAS,kBAAkB,EAAE,MAAM,EAA8B;IAC9E,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAC1B,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAQ;QAAQ,OAAO,EAAE;KAAC;IAEvC,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE;YACvD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU;YACZ,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC;YAC3D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,GAAG,QAAQ,SAAS,SAAS,IAAI,CAAC;gBAC/C,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAG,WAAU;kDAAuC,OAAO,KAAK;;;;;;;;;;;;0CAEnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE;wCACjC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,mJAAA,CAAA,UAAgB;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}]}