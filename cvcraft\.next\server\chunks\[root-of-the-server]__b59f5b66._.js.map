{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\n\nconst registerSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { name, email, password } = registerSchema.parse(body)\n\n    // Check if user already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: 'User with this email already exists' },\n        { status: 400 }\n      )\n    }\n\n    // Hash password\n    const hashedPassword = await bcrypt.hash(password, 12)\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        name,\n        email,\n        password: hashedPassword,\n      },\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        createdAt: true,\n      }\n    })\n\n    return NextResponse.json(\n      { message: 'User created successfully', user },\n      { status: 201 }\n    )\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation failed', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Registration error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,eAAe,KAAK,CAAC;QAEvD,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,cAAc;QACd,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;YACZ;YACA,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAA6B;QAAK,GAC7C;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAqB,SAAS,MAAM,MAAM;YAAC,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}