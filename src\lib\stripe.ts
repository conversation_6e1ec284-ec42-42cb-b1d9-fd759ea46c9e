import Stripe from 'stripe'

// Only initialize Stripe on the server-side and when we have a valid key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY

export const stripe = stripeSecretKey && !stripeSecretKey.includes('sk_test_51234567890abcdef')
  ? new Stripe(stripeSecretKey, {
      apiVersion: '2023-10-16',
      typescript: true,
    })
  : null

export const getStripeJs = async () => {
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

  // Only load Stripe if we have a valid publishable key
  if (!publishableKey || publishableKey.includes('pk_test_51234567890abcdef')) {
    console.warn('Stripe is not configured - using demo mode')
    return null
  }

  const stripeJs = await import('@stripe/stripe-js')
  return stripeJs.loadStripe(publishableKey)
}
