import { useState } from 'react'
import { Language, ResumeData } from '@/types/resume'

interface LanguagesFormProps {
  data: ResumeData
  onUpdateLanguages: (languages: Language[]) => void
}

export function LanguagesForm({ data, onUpdateLanguages }: LanguagesFormProps) {
  const { languages } = data

  const addLanguage = () => {
    const newLanguage: Language = {
      id: Date.now().toString(),
      name: '',
      proficiency: 'Conversational'
    }
    onUpdateLanguages([...languages, newLanguage])
  }

  const updateLanguage = (id: string, field: keyof Language, value: string) => {
    const updated = languages.map(lang => 
      lang.id === id ? { ...lang, [field]: value } : lang
    )
    onUpdateLanguages(updated)
  }

  const removeLanguage = (id: string) => {
    onUpdateLanguages(languages.filter(lang => lang.id !== id))
  }

  const proficiencyLevels: Language['proficiency'][] = ['Basic', 'Conversational', 'Fluent', 'Native']

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Add languages you speak and your proficiency level in each.
      </div>

      {languages.map((language, index) => (
        <div key={language.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-700 transition-colors duration-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Language {index + 1}
            </h3>
            <button
              onClick={() => removeLanguage(language.id)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm transition-colors"
            >
              Remove
            </button>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Language *
              </label>
              <input
                type="text"
                value={language.name}
                onChange={(e) => updateLanguage(language.id, 'name', e.target.value)}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black dark:text-white bg-white dark:bg-gray-700 transition-colors duration-200"
                placeholder="English"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Proficiency Level *
              </label>
              <select
                value={language.proficiency}
                onChange={(e) => updateLanguage(language.id, 'proficiency', e.target.value)}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-black dark:text-white bg-white dark:bg-gray-700 transition-colors duration-200"
                required
              >
                {proficiencyLevels.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      ))}

      <button
        onClick={addLanguage}
        className="w-full border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 transition-colors duration-200"
      >
        <span className="text-gray-600 dark:text-gray-300">+ Add Language</span>
      </button>

      {languages.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No languages added yet</p>
          <button
            onClick={addLanguage}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Add Your First Language
          </button>
        </div>
      )}
    </div>
  )
}
