import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { canAccessTemplate } from '@/lib/premium'
import { TemplateType } from '@/types/resume'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const resume = await prisma.resume.findFirst({
      where: {
        id: id,
        userId: session.user.id
      }
    })

    if (!resume) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    return NextResponse.json(resume)
  } catch (error) {
    console.error('Error fetching resume:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const { title, data, template } = await request.json()

    // Validate template access if template is being changed
    if (template && !canAccessTemplate(template as TemplateType, session.user.isPremium)) {
      return NextResponse.json(
        { error: 'This template requires a Premium subscription.' },
        { status: 403 }
      )
    }

    const resume = await prisma.resume.updateMany({
      where: {
        id: id,
        userId: session.user.id
      },
      data: {
        title,
        data,
        template,
        updatedAt: new Date()
      }
    })

    if (resume.count === 0) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    const updatedResume = await prisma.resume.findUnique({
      where: { id: id }
    })

    return NextResponse.json(updatedResume)
  } catch (error) {
    console.error('Error updating resume:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const result = await prisma.resume.deleteMany({
      where: {
        id: id,
        userId: session.user.id
      }
    })

    if (result.count === 0) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 })
    }

    return NextResponse.json({ message: 'Resume deleted successfully' })
  } catch (error) {
    console.error('Error deleting resume:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
