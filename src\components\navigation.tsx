'use client'

import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { ThemeToggle } from './theme-toggle'

export function Navigation() {
  const { data: session } = useSession()

  return (
    <nav className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/dashboard" className="text-xl font-bold text-blue-600 dark:text-blue-400">
              CVCraft
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            {session && (
              <>
                <Link href="/billing" className="text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
                  Billing
                </Link>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Welcome, {session.user.name}
                </span>
                {session.user.isPremium && (
                  <span className="bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-xs font-medium px-2.5 py-0.5 rounded">
                    Premium
                  </span>
                )}
                <button
                  onClick={() => signOut()}
                  className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-sm transition-colors"
                >
                  Sign out
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
