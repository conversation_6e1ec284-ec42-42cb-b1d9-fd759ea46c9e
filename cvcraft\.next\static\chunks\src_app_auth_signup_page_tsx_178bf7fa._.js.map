{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/CV%20SAAS/cvcraft/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signIn } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\n\nconst signupSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  confirmPassword: z.string(),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n})\n\ntype SignupForm = z.infer<typeof signupSchema>\n\nexport default function SignUpPage() {\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<SignupForm>({\n    resolver: zod<PERSON><PERSON><PERSON>ver(signupSchema),\n  })\n\n  const onSubmit = async (data: SignupForm) => {\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: data.name,\n          email: data.email,\n          password: data.password,\n        }),\n      })\n\n      if (response.ok) {\n        // Auto sign in after successful registration\n        const result = await signIn('credentials', {\n          email: data.email,\n          password: data.password,\n          redirect: false,\n        })\n\n        if (result?.ok) {\n          router.push('/dashboard')\n          router.refresh()\n        }\n      } else {\n        const errorData = await response.json()\n        setError(errorData.error || 'Registration failed')\n      }\n    } catch (error) {\n      setError('An error occurred. Please try again.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your CVCraft account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              href=\"/auth/signin\"\n              className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name\n              </label>\n              <input\n                {...register('name')}\n                type=\"text\"\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                placeholder=\"Enter your full name\"\n              />\n              {errors.name && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                {...register('email')}\n                type=\"email\"\n                autoComplete=\"email\"\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                placeholder=\"Enter your email address\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                {...register('password')}\n                type=\"password\"\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                placeholder=\"Enter your password\"\n              />\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <input\n                {...register('confirmPassword')}\n                type=\"password\"\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                placeholder=\"Confirm your password\"\n              />\n              {errors.confirmPassword && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.confirmPassword.message}</p>\n              )}\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"text-red-600 text-sm text-center\">{error}</div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Creating account...' : 'Create account'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAc;QACtB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,6CAA6C;gBAC7C,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;oBACzC,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,UAAU;gBACZ;gBAEA,IAAI,mBAAA,6BAAA,OAAQ,EAAE,EAAE;oBACd,OAAO,IAAI,CAAC;oBACZ,OAAO,OAAO;gBAChB;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAKL,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC;4CACE,GAAG,SAAS,OAAO;4CACpB,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAIjE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACE,GAAG,SAAS,QAAQ;4CACrB,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACE,GAAG,SAAS,WAAW;4CACxB,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAIrE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,6LAAC;4CACE,GAAG,SAAS,kBAAkB;4CAC/B,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,eAAe,kBACrB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;wBAK7E,uBACC,6LAAC;4BAAI,WAAU;sCAAoC;;;;;;sCAGrD,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAvJwB;;QAGP,qIAAA,CAAA,YAAS;QAMpB,iKAAA,CAAA,UAAO;;;KATW", "debugId": null}}]}