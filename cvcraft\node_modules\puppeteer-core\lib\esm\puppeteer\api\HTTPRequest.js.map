{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/api/HTTPRequest.ts"], "names": [], "mappings": "AAQA,OAAO,EAAC,UAAU,EAAE,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AACvD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AAsDvD;;;;GAIG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,OAAgB,WAAW;IAM/B;;OAEG;IACH,eAAe,CAAqB;IACpC;;OAEG;IACH,YAAY,GAAkB,IAAI,CAAC;IACnC;;OAEG;IACH,SAAS,GAAwB,IAAI,CAAC;IACtC;;OAEG;IACH,gBAAgB,GAAG,KAAK,CAAC;IACzB;;OAEG;IACH,cAAc,GAAkB,EAAE,CAAC;IAEnC;;OAEG;IACO,YAAY,GAQlB;QACF,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,EAAE;QACZ,eAAe,EAAE;YACf,MAAM,EAAE,yBAAyB,CAAC,IAAI;SACvC;QACD,gBAAgB,EAAE,EAAE;QACpB,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;KAClB,CAAC;IASF;;OAEG;IACH,gBAAe,CAAC;IAOhB;;;;OAIG;IACH,wBAAwB;QACtB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;IACvC,CAAC;IAED;;;;;;;;;;OAUG;IACH,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC/B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,QAAQ,EAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,cAAc,EAAC,CAAC;QAC5D,CAAC;QACD,OAAO,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,4BAA4B;QAC1B,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACH,sBAAsB,CACpB,cAAiD;QAEjD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAmBD;;;OAGG;IACH,KAAK,CAAC,qBAAqB;QACzB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,EAAE;YACxE,OAAO,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,CAAC;QAChC,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC1D,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACxC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBACD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACzD,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IA8GD,iBAAiB;QACf,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;IACnE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,KAAK,CAAC,QAAQ,CACZ,YAAsC,EAAE,EACxC,QAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAC/C,IACE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS;YACxD,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EACrD,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG;gBAClC,MAAM,EAAE,yBAAyB,CAAC,QAAQ;gBAC1C,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,QAAQ,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC5D,IACE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,KAAK,OAAO;gBACpD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,KAAK,SAAS,EACtD,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM;gBACtC,yBAAyB,CAAC,QAAQ,CAAC;QACvC,CAAC;QACD,OAAO;IACT,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,KAAK,CAAC,OAAO,CACX,QAAqC,EACrC,QAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtC,IACE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS;YACxD,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EACrD,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG;gBAClC,MAAM,EAAE,yBAAyB,CAAC,OAAO;gBACzC,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,QAAQ,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC5D,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBACzD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM;gBACtC,yBAAyB,CAAC,OAAO,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,KAAK,CACT,YAAuB,QAAQ,EAC/B,QAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,WAAW,EAAE,sBAAsB,GAAG,SAAS,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,sCAAsC,CAAC,CAAC;QAC1E,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;QAC5C,IACE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS;YACxD,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EACtD,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG;gBAClC,MAAM,EAAE,yBAAyB,CAAC,KAAK;gBACvC,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,IAAyB;QAI1C,uCAAuC;QACvC,MAAM,QAAQ,GAAe,QAAQ,CAAC,IAAI,CAAC;YACzC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC;QAET,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,UAAU;YAClC,MAAM,EAAE,kBAAkB,CAAC,QAAQ,CAAC;SACrC,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,yBAOX;AAPD,WAAY,yBAAyB;IACnC,4CAAe,CAAA;IACf,gDAAmB,CAAA;IACnB,kDAAqB,CAAA;IACrB,kDAAqB,CAAA;IACrB,0CAAa,CAAA;IACb,+DAAkC,CAAA;AACpC,CAAC,EAPW,yBAAyB,KAAzB,yBAAyB,QAOpC;AA0BD;;GAEG;AACH,MAAM,UAAU,YAAY,CAC1B,OAA0C;IAE1C,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,IAAI,CACT,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACpB,OAAO,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAC,CAAC;YACnC,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,YAAY,GAA2B;IAClD,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,gBAAgB;IACvB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,wBAAwB;IAC/B,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,iCAAiC;IACxC,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,4BAA4B;IACnC,KAAK,EAAE,yBAAyB;IAChC,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,iCAAiC;CAChC,CAAC;AAEX,MAAM,YAAY,GAAoD;IACpE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,cAAc;IAC5B,kBAAkB,EAAE,oBAAoB;IACxC,eAAe,EAAE,iBAAiB;IAClC,iBAAiB,EAAE,mBAAmB;IACtC,iBAAiB,EAAE,mBAAmB;IACtC,gBAAgB,EAAE,kBAAkB;IACpC,gBAAgB,EAAE,kBAAkB;IACpC,iBAAiB,EAAE,mBAAmB;IACtC,eAAe,EAAE,iBAAiB;IAClC,oBAAoB,EAAE,sBAAsB;IAC5C,eAAe,EAAE,iBAAiB;IAClC,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEX;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAoB;IAC9C,wEAAwE;IACxE,6BAA6B;IAC7B,IACE,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAChD,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC;QAC/C,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACnD,iEAAiE;QACjE,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAClD,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;IACD,kEAAkE;IAClE,oEAAoE;IACpE,UAAU;IACV,UAAU,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC"}