import { render, screen } from '@testing-library/react'
import Home from '@/app/page'

describe('Landing Page', () => {
  it('renders the main heading', () => {
    render(<Home />)
    
    const heading = screen.getByRole('heading', { 
      name: /Build Your Perfect Resume in Minutes/i 
    })
    expect(heading).toBeInTheDocument()
  })

  it('renders the get started button', () => {
    render(<Home />)
    
    const getStartedButton = screen.getByRole('link', { 
      name: /Start Building Free/i 
    })
    expect(getStartedButton).toBeInTheDocument()
    expect(getStartedButton).toHaveAttribute('href', '/auth/register')
  })

  it('renders the features section', () => {
    render(<Home />)
    
    expect(screen.getByText('Lightning Fast')).toBeInTheDocument()
    expect(screen.getByText('ATS-Friendly')).toBeInTheDocument()
    expect(screen.getByText('Professional Templates')).toBeInTheDocument()
    expect(screen.getByText('Live Preview')).toBeInTheDocument()
  })

  it('renders the pricing section', () => {
    render(<Home />)
    
    expect(screen.getByText('Simple, Transparent Pricing')).toBeInTheDocument()
    expect(screen.getByText('Free')).toBeInTheDocument()
    expect(screen.getByText('Premium')).toBeInTheDocument()
  })
})
